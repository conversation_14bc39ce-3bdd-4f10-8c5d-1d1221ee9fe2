from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from ..models.email_template import EmailTemplate
from ..models.user import User

logger = logging.getLogger(__name__)

class EmailTemplateService:
    """
    Service for managing email templates
    """
    
    @staticmethod
    def create_template(
        template_key: str,
        name: str,
        subjects: Dict[str, str],
        html_content: Dict[str, str],
        category: str = 'general',
        description: str = None,
        text_content: Dict[str, str] = None,
        variables: List[str] = None,
        supported_languages: List[str] = None,
        default_language: str = 'en',
        created_by: User = None,
        is_system_template: bool = False
    ) -> EmailTemplate:
        """
        Create a new email template
        
        Args:
            template_key: Unique template identifier
            name: Human readable name
            subjects: Dictionary of subjects by language
            html_content: Dictionary of HTML content by language
            category: Template category
            description: Template description
            text_content: Dictionary of text content by language
            variables: List of template variables
            supported_languages: List of supported language codes
            default_language: Default language code
            created_by: User who created the template
            is_system_template: Whether this is a system template
            
        Returns:
            Created EmailTemplate instance
        """
        try:
            # Check if template key already exists
            existing = EmailTemplate.objects(template_key=template_key).first()
            if existing:
                raise ValueError(f"Template with key '{template_key}' already exists")
            
            # Create template
            template = EmailTemplate(
                template_key=template_key,
                name=name,
                description=description,
                category=category,
                subjects=subjects,
                html_content=html_content,
                text_content=text_content or {},
                variables=variables or [],
                default_language=default_language,
                supported_languages=supported_languages or ['en', 'fa'],
                created_by=created_by,
                is_system_template=is_system_template
            )
            
            template.save()
            logger.info(f"Created email template: {template_key}")
            return template
            
        except Exception as e:
            logger.error(f"Failed to create email template '{template_key}': {str(e)}")
            raise
    
    @staticmethod
    def update_template(
        template_id: str,
        **kwargs
    ) -> EmailTemplate:
        """
        Update an existing email template
        
        Args:
            template_id: Template ID
            **kwargs: Fields to update
            
        Returns:
            Updated EmailTemplate instance
        """
        try:
            template = EmailTemplate.objects(id=template_id).first()
            if not template:
                raise ValueError(f"Template with ID '{template_id}' not found")
            
            # Don't allow updating system templates by non-admin users
            if template.is_system_template and 'is_system_template' not in kwargs:
                # TODO: Add admin check
                pass
            
            # Update fields
            for field, value in kwargs.items():
                if hasattr(template, field):
                    setattr(template, field, value)
            
            template.save()
            logger.info(f"Updated email template: {template.template_key}")
            return template
            
        except Exception as e:
            logger.error(f"Failed to update email template '{template_id}': {str(e)}")
            raise
    
    @staticmethod
    def get_template(template_key: str) -> Optional[EmailTemplate]:
        """Get template by key"""
        return EmailTemplate.objects(
            template_key=template_key,
            is_active=True
        ).first()
    
    @staticmethod
    def get_template_by_id(template_id: str) -> Optional[EmailTemplate]:
        """Get template by ID"""
        return EmailTemplate.objects(id=template_id).first()
    
    @staticmethod
    def list_templates(
        category: str = None,
        created_by: User = None,
        include_system: bool = True,
        active_only: bool = True
    ) -> List[EmailTemplate]:
        """
        List email templates with filters

        Args:
            category: Filter by category
            created_by: Filter by creator
            include_system: Include system templates
            active_only: Only active templates

        Returns:
            List of EmailTemplate instances
        """
        try:
            query = {}

            if category:
                query['category'] = category

            if created_by:
                query['created_by'] = created_by

            if not include_system:
                query['is_system_template'] = False

            if active_only:
                query['is_active'] = True

            templates = list(EmailTemplate.objects(**query).order_by('-created_at'))
            logger.info(f"EmailTemplateService.list_templates: Found {len(templates)} templates with query {query}")

            return templates
        except Exception as e:
            logger.error(f"Error in list_templates: {str(e)}")
            return []
    
    @staticmethod
    def delete_template(template_id: str, force: bool = False) -> bool:
        """
        Delete an email template
        
        Args:
            template_id: Template ID
            force: Force delete system templates
            
        Returns:
            True if deleted successfully
        """
        try:
            template = EmailTemplate.objects(id=template_id).first()
            if not template:
                raise ValueError(f"Template with ID '{template_id}' not found")
            
            if template.is_system_template and not force:
                raise ValueError("Cannot delete system template")
            
            template.delete()
            logger.info(f"Deleted email template: {template.template_key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete email template '{template_id}': {str(e)}")
            raise
    
    @staticmethod
    def duplicate_template(
        template_id: str,
        new_template_key: str,
        new_name: str,
        created_by: User = None
    ) -> EmailTemplate:
        """
        Duplicate an existing template
        
        Args:
            template_id: Source template ID
            new_template_key: New template key
            new_name: New template name
            created_by: User creating the duplicate
            
        Returns:
            New EmailTemplate instance
        """
        try:
            source_template = EmailTemplate.objects(id=template_id).first()
            if not source_template:
                raise ValueError(f"Source template with ID '{template_id}' not found")
            
            # Create duplicate
            duplicate = EmailTemplate(
                template_key=new_template_key,
                name=new_name,
                description=f"Copy of {source_template.name}",
                category=source_template.category,
                subjects=source_template.subjects.copy(),
                html_content=source_template.html_content.copy(),
                text_content=source_template.text_content.copy(),
                variables=source_template.variables.copy(),
                default_language=source_template.default_language,
                supported_languages=source_template.supported_languages.copy(),
                created_by=created_by,
                is_system_template=False  # Duplicates are never system templates
            )
            
            duplicate.save()
            logger.info(f"Duplicated email template: {new_template_key}")
            return duplicate
            
        except Exception as e:
            logger.error(f"Failed to duplicate email template '{template_id}': {str(e)}")
            raise
    
    @staticmethod
    def get_template_variables(template_id: str) -> List[str]:
        """Get list of variables used in a template"""
        template = EmailTemplate.objects(id=template_id).first()
        if not template:
            return []
        
        return template.variables or []
    
    @staticmethod
    def validate_template_content(
        subjects: Dict[str, str],
        html_content: Dict[str, str],
        variables: List[str] = None
    ) -> Dict[str, Any]:
        """
        Validate template content
        
        Args:
            subjects: Dictionary of subjects by language
            html_content: Dictionary of HTML content by language
            variables: List of expected variables
            
        Returns:
            Validation result dictionary
        """
        errors = []
        warnings = []
        
        # Check if subjects and content have same languages
        subject_langs = set(subjects.keys())
        content_langs = set(html_content.keys())
        
        if subject_langs != content_langs:
            errors.append("Subject and content languages don't match")
        
        # Check for empty content
        for lang, subject in subjects.items():
            if not subject.strip():
                errors.append(f"Empty subject for language: {lang}")
        
        for lang, content in html_content.items():
            if not content.strip():
                errors.append(f"Empty content for language: {lang}")
        
        # TODO: Add more validation (HTML syntax, variable usage, etc.)
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    @staticmethod
    def initialize_system_templates():
        """Initialize default system templates"""
        system_templates = [
            {
                'template_key': 'welcome',
                'name': 'Welcome Email',
                'category': 'auth',
                'description': 'Welcome email for new users',
                'subjects': {
                    'en': 'Welcome to {{site_name}}!',
                    'fa': 'به {{site_name}} خوش آمدید!'
                },
                'html_content': {
                    'en': '''
                    <h1>Welcome to {{site_name}}!</h1>
                    <p>Hello {{user_name}},</p>
                    <p>Thank you for joining {{site_name}}. We're excited to have you on board!</p>
                    <p>Get started by exploring our features and creating your first social media campaign.</p>
                    <p>Best regards,<br>The {{site_name}} Team</p>
                    ''',
                    'fa': '''
                    <h1>به {{site_name}} خوش آمدید!</h1>
                    <p>سلام {{user_name}}،</p>
                    <p>از اینکه به {{site_name}} پیوستید متشکریم. خوشحالیم که شما را در کنار خود داریم!</p>
                    <p>با کاوش در ویژگی‌های ما و ایجاد اولین کمپین رسانه‌های اجتماعی خود شروع کنید.</p>
                    <p>با احترام،<br>تیم {{site_name}}</p>
                    '''
                },
                'variables': ['site_name', 'user_name']
            },
            {
                'template_key': 'password_reset',
                'name': 'Password Reset',
                'category': 'auth',
                'description': 'Password reset email',
                'subjects': {
                    'en': 'Reset Your Password - {{site_name}}',
                    'fa': 'بازنشانی رمز عبور - {{site_name}}'
                },
                'html_content': {
                    'en': '''
                    <h1>Reset Your Password</h1>
                    <p>Hello {{user_name}},</p>
                    <p>You requested to reset your password for your {{site_name}} account.</p>
                    <p><a href="{{reset_link}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                    <p>This link will expire in 24 hours. If you didn't request this, please ignore this email.</p>
                    <p>Best regards,<br>The {{site_name}} Team</p>
                    ''',
                    'fa': '''
                    <h1>بازنشانی رمز عبور</h1>
                    <p>سلام {{user_name}}،</p>
                    <p>شما درخواست بازنشانی رمز عبور حساب {{site_name}} خود را داده‌اید.</p>
                    <p><a href="{{reset_link}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">بازنشانی رمز عبور</a></p>
                    <p>این لینک در 24 ساعت منقضی می‌شود. اگر این درخواست را نداده‌اید، این ایمیل را نادیده بگیرید.</p>
                    <p>با احترام،<br>تیم {{site_name}}</p>
                    '''
                },
                'variables': ['site_name', 'user_name', 'reset_link']
            },
            {
                'template_key': 'email_verification',
                'name': 'Email Verification',
                'category': 'auth',
                'description': 'Email verification for new accounts',
                'subjects': {
                    'en': 'Verify Your Email - {{site_name}}',
                    'fa': 'تأیید ایمیل شما - {{site_name}}'
                },
                'html_content': {
                    'en': '''
                    <h1>Verify Your Email Address</h1>
                    <p>Hello {{user_name}},</p>
                    <p>Thank you for signing up with {{site_name}}. To complete your registration, please verify your email address.</p>
                    <p><a href="{{verification_link}}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a></p>
                    <p>This link will expire in 48 hours. If you didn't create an account, please ignore this email.</p>
                    <p>Best regards,<br>The {{site_name}} Team</p>
                    ''',
                    'fa': '''
                    <h1>تأیید آدرس ایمیل شما</h1>
                    <p>سلام {{user_name}}،</p>
                    <p>از اینکه در {{site_name}} ثبت‌نام کردید متشکریم. برای تکمیل ثبت‌نام، لطفاً آدرس ایمیل خود را تأیید کنید.</p>
                    <p><a href="{{verification_link}}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">تأیید ایمیل</a></p>
                    <p>این لینک در 48 ساعت منقضی می‌شود. اگر حساب کاربری ایجاد نکرده‌اید، این ایمیل را نادیده بگیرید.</p>
                    <p>با احترام،<br>تیم {{site_name}}</p>
                    '''
                },
                'variables': ['site_name', 'user_name', 'verification_link']
            },
            {
                'template_key': 'account_activation',
                'name': 'Account Activation',
                'category': 'auth',
                'description': 'Account activation notification',
                'subjects': {
                    'en': 'Your Account is Now Active - {{site_name}}',
                    'fa': 'حساب شما فعال شد - {{site_name}}'
                },
                'html_content': {
                    'en': '''
                    <h1>Account Activated Successfully!</h1>
                    <p>Hello {{user_name}},</p>
                    <p>Great news! Your {{site_name}} account has been successfully activated.</p>
                    <p>You can now access all features and start managing your social media campaigns.</p>
                    <p><a href="{{login_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Your Account</a></p>
                    <p>Best regards,<br>The {{site_name}} Team</p>
                    ''',
                    'fa': '''
                    <h1>حساب شما با موفقیت فعال شد!</h1>
                    <p>سلام {{user_name}}،</p>
                    <p>خبر خوب! حساب {{site_name}} شما با موفقیت فعال شده است.</p>
                    <p>اکنون می‌توانید به تمام ویژگی‌ها دسترسی داشته باشید و مدیریت کمپین‌های رسانه‌های اجتماعی خود را شروع کنید.</p>
                    <p><a href="{{login_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">ورود به حساب</a></p>
                    <p>با احترام،<br>تیم {{site_name}}</p>
                    '''
                },
                'variables': ['site_name', 'user_name', 'login_url']
            },
            {
                'template_key': 'post_published',
                'name': 'Post Published Notification',
                'category': 'notification',
                'description': 'Notification when a scheduled post is published',
                'subjects': {
                    'en': 'Your Post Has Been Published - {{site_name}}',
                    'fa': 'پست شما منتشر شد - {{site_name}}'
                },
                'html_content': {
                    'en': '''
                    <h1>Post Published Successfully!</h1>
                    <p>Hello {{user_name}},</p>
                    <p>Your scheduled post has been successfully published on {{platform}}.</p>
                    <p><strong>Post Content:</strong></p>
                    <blockquote style="border-left: 4px solid #007bff; padding-left: 15px; margin: 15px 0; color: #666;">
                        {{post_content}}
                    </blockquote>
                    <p><a href="{{post_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Post</a></p>
                    <p>Best regards,<br>The {{site_name}} Team</p>
                    ''',
                    'fa': '''
                    <h1>پست با موفقیت منتشر شد!</h1>
                    <p>سلام {{user_name}}،</p>
                    <p>پست زمان‌بندی شده شما با موفقیت در {{platform}} منتشر شد.</p>
                    <p><strong>محتوای پست:</strong></p>
                    <blockquote style="border-right: 4px solid #007bff; padding-right: 15px; margin: 15px 0; color: #666; direction: rtl;">
                        {{post_content}}
                    </blockquote>
                    <p><a href="{{post_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">مشاهده پست</a></p>
                    <p>با احترام،<br>تیم {{site_name}}</p>
                    '''
                },
                'variables': ['site_name', 'user_name', 'platform', 'post_content', 'post_url']
            },
            {
                'template_key': 'monthly_report',
                'name': 'Monthly Analytics Report',
                'category': 'notification',
                'description': 'Monthly analytics and performance report',
                'subjects': {
                    'en': 'Your Monthly Report is Ready - {{site_name}}',
                    'fa': 'گزارش ماهانه شما آماده است - {{site_name}}'
                },
                'html_content': {
                    'en': '''
                    <h1>Monthly Performance Report</h1>
                    <p>Hello {{user_name}},</p>
                    <p>Your monthly analytics report for {{month}} {{year}} is now available.</p>
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                        <h3>Key Metrics:</h3>
                        <ul>
                            <li>Posts Published: {{posts_count}}</li>
                            <li>Total Engagement: {{total_engagement}}</li>
                            <li>Reach: {{total_reach}}</li>
                            <li>Top Performing Platform: {{top_platform}}</li>
                        </ul>
                    </div>
                    <p><a href="{{report_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Full Report</a></p>
                    <p>Best regards,<br>The {{site_name}} Team</p>
                    ''',
                    'fa': '''
                    <h1>گزارش عملکرد ماهانه</h1>
                    <p>سلام {{user_name}}،</p>
                    <p>گزارش تحلیلی ماهانه شما برای {{month}} {{year}} آماده است.</p>
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; direction: rtl;">
                        <h3>معیارهای کلیدی:</h3>
                        <ul>
                            <li>پست‌های منتشر شده: {{posts_count}}</li>
                            <li>کل تعاملات: {{total_engagement}}</li>
                            <li>دسترسی: {{total_reach}}</li>
                            <li>پلتفرم برتر: {{top_platform}}</li>
                        </ul>
                    </div>
                    <p><a href="{{report_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">مشاهده گزارش کامل</a></p>
                    <p>با احترام،<br>تیم {{site_name}}</p>
                    '''
                },
                'variables': ['site_name', 'user_name', 'month', 'year', 'posts_count', 'total_engagement', 'total_reach', 'top_platform', 'report_url']
            }
        ]
        
        for template_data in system_templates:
            try:
                existing = EmailTemplate.objects(template_key=template_data['template_key']).first()
                if not existing:
                    EmailTemplateService.create_template(
                        is_system_template=True,
                        **template_data
                    )
                    logger.info(f"Created system template: {template_data['template_key']}")
            except Exception as e:
                logger.error(f"Failed to create system template {template_data['template_key']}: {str(e)}")
