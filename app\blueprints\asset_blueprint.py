from flask import Blueprint, request, jsonify, current_app, send_from_directory
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
import logging
import os
from ..services.asset_service import AssetService
from ..models.asset import AssetType

logger = logging.getLogger(__name__)
asset_bp = Blueprint('asset', __name__, url_prefix='/api/assets')

# Validation schemas
class UpdateMetadataSchema(Schema):
    metadata = fields.Dict(required=True)

@asset_bp.route('/', methods=['POST'])
@login_required
def upload_asset():
    """Upload a new asset"""
    try:
        # Check if file is included in the request
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "No file provided"}), 400
            
        file = request.files['file']
        
        # Check if file is empty
        if file.filename == '':
            return jsonify({"success": False, "error": "No file selected"}), 400
            
        # Get file type from form data
        file_type = request.form.get('file_type')
        if not file_type:
            return jsonify({"success": False, "error": "File type is required"}), 400
            
        # Validate file type
        if file_type not in [t.value for t in AssetType]:
            return jsonify({
                "success": False, 
                "error": f"Invalid file type. Must be one of: {', '.join([t.value for t in AssetType])}"
            }), 400
            
        # Get metadata if provided
        metadata = {}
        if 'metadata' in request.form:
            try:
                metadata = json.loads(request.form.get('metadata', '{}'))
            except json.JSONDecodeError:
                return jsonify({"success": False, "error": "Invalid metadata format"}), 400
        
        # Upload asset
        asset = AssetService.upload_asset(
            user_id=str(current_user.id),
            file=file,
            file_type=file_type,
            metadata=metadata
        )
        
        return jsonify({
            "success": True,
            "data": asset.to_mongo()
        }), 201
    except ValueError as e:
        return jsonify({"success": False, "error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error uploading asset: {str(e)}")
        return jsonify({"success": False, "error": "Failed to upload asset"}), 500

@asset_bp.route('/', methods=['GET'])
@login_required
def get_all_assets():
    """Get all assets for the current user"""
    try:
        # Get file type filter if provided
        file_type = request.args.get('file_type')
        
        # Get assets
        assets = AssetService.get_all_assets_for_user(
            user_id=str(current_user.id),
            file_type=file_type
        )
        
        return jsonify({
            "success": True,
            "data": [asset.to_mongo() for asset in assets]
        }), 200
    except ValueError as e:
        return jsonify({"success": False, "error": str(e)}), 400
    except Exception as e:
        logger.error(f"Error retrieving assets: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve assets"}), 500

@asset_bp.route('/<asset_id>', methods=['GET'])
@login_required
def get_asset(asset_id):
    """Get a specific asset by ID"""
    try:
        asset = AssetService.get_asset_by_id(asset_id)
        if not asset:
            return jsonify({"success": False, "error": "Asset not found"}), 404
            
        # Check if asset belongs to current user
        if str(asset.user.id) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
            
        return jsonify({
            "success": True,
            "data": asset.to_mongo()
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving asset: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve asset"}), 500

@asset_bp.route('/<asset_id>', methods=['DELETE'])
@login_required
def delete_asset(asset_id):
    """Delete an asset"""
    try:
        # Check if asset exists and belongs to current user
        asset = AssetService.get_asset_by_id(asset_id)
        if not asset:
            return jsonify({"success": False, "error": "Asset not found"}), 404
            
        if str(asset.user.id) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
        
        # Delete asset
        success = AssetService.delete_asset(asset_id)
        
        if not success:
            return jsonify({
                "success": False, 
                "error": "Failed to delete asset"
            }), 500
        
        return jsonify({
            "success": True,
            "message": "Asset deleted successfully"
        }), 200
    except Exception as e:
        logger.error(f"Error deleting asset: {str(e)}")
        return jsonify({"success": False, "error": "Failed to delete asset"}), 500

@asset_bp.route('/<asset_id>/metadata', methods=['PUT'])
@login_required
def update_metadata(asset_id):
    """Update asset metadata"""
    try:
        # Check if asset exists and belongs to current user
        asset = AssetService.get_asset_by_id(asset_id)
        if not asset:
            return jsonify({"success": False, "error": "Asset not found"}), 404
            
        if str(asset.user.id) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized"}), 403
        
        # Validate request data
        schema = UpdateMetadataSchema()
        data = schema.load(request.json)
        
        # Update metadata
        updated_asset = AssetService.update_metadata(
            asset_id=asset_id,
            metadata=data['metadata']
        )
        
        if not updated_asset:
            return jsonify({
                "success": False, 
                "error": "Failed to update asset metadata"
            }), 500
        
        return jsonify({
            "success": True,
            "data": updated_asset.to_mongo()
        }), 200
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error updating asset metadata: {str(e)}")
        return jsonify({"success": False, "error": "Failed to update asset metadata"}), 500

# Add route to serve asset files
@asset_bp.route('/file/<path:filename>', methods=['GET'])
def serve_asset(filename):
    """Serve an asset file"""
    try:
        return send_from_directory(current_app.config['UPLOAD_FOLDER'], filename)
    except Exception as e:
        logger.error(f"Error serving asset file: {str(e)}")
        return jsonify({"success": False, "error": "Failed to serve asset file"}), 500