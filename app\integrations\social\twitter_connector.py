import requests
from typing import Dict, Any, List
from .base_social_connector import BaseSocialIntegration as BaseConnector

class TwitterConnector(BaseConnector):
    """
    Integration class for Twitter API v2
    """

    def __init__(self, auth_data: dict):
        self.bearer_token = auth_data.get("bearer_token")
        self.api_base_url = "https://api.twitter.com/2"
        self.supported_features = ["post", "edit", "delete", "metrics", "comments"]

    def authenticate(self) -> bool:
        """
        Authenticate using the Bearer token
        """
        url = f"{self.api_base_url}/users/me"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }
        resp = requests.get(url, headers=headers)
        return resp.status_code == 200

    def post_content(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new tweet
        """
        message = content_data.get("message", "")
        url = f"{self.api_base_url}/tweets"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}",
            "Content-Type": "application/json"
        }
        payload = {
            "status": message
        }

        resp = requests.post(url, headers=headers, json=payload)
        resp.raise_for_status()
        return resp.json()

    def edit_content(self, tweet_id: str, updated_data: Dict[str, Any]) -> bool:
        """
        Edit a tweet (currently not supported by Twitter API v2)
        Twitter API v2 currently doesn't support editing tweets.
        """
        raise NotImplementedError("Twitter API v2 does not support editing tweets.")

    def delete_content(self, tweet_id: str) -> bool:
        """
        Delete a tweet by tweet ID
        """
        url = f"{self.api_base_url}/tweets/{tweet_id}"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }

        resp = requests.delete(url, headers=headers)
        return resp.status_code == 200

    def fetch_metrics(self, tweet_id: str) -> Dict[str, Any]:
        """
        Fetch tweet engagement metrics (likes, retweets, replies)
        """
        url = f"{self.api_base_url}/tweets/{tweet_id}/metrics"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }
        resp = requests.get(url, headers=headers)
        resp.raise_for_status()
        return resp.json()

    def fetch_profile_info(self) -> Dict[str, Any]:
        """
        Fetch authenticated user's profile info
        """
        url = f"{self.api_base_url}/users/me"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }
        resp = requests.get(url, headers=headers)
        resp.raise_for_status()
        return resp.json().get("data", {})

    def fetch_recent_tweets(self, user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Fetch recent tweets from a user's timeline
        """
        url = f"{self.api_base_url}/users/{user_id}/tweets"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }
        params = {
            "max_results": limit
        }
        resp = requests.get(url, headers=headers, params=params)
        resp.raise_for_status()
        return resp.json().get("data", [])

    def post_comment(self, tweet_id: str, comment_text: str) -> Dict[str, Any]:
        """
        Post a reply (comment) on a tweet
        """
        url = f"{self.api_base_url}/tweets"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}",
            "Content-Type": "application/json"
        }
        payload = {
            "status": comment_text,
            "in_reply_to_status_id": tweet_id
        }

        resp = requests.post(url, headers=headers, json=payload)
        resp.raise_for_status()
        return resp.json()

    def fetch_comments(self, tweet_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Fetch replies (comments) for a tweet
        """
        url = f"{self.api_base_url}/tweets/{tweet_id}/replies"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }
        params = {
            "max_results": limit
        }
        resp = requests.get(url, headers=headers, params=params)
        resp.raise_for_status()
        return resp.json().get("data", [])

    def verify_connection(self) -> bool:
        return self.authenticate()

    def fetch_recent_posts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Fetch recent tweets from the authenticated user's timeline
        """
        url = f"{self.api_base_url}/users/me/tweets"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }
        params = {
            "max_results": limit
        }
        resp = requests.get(url, headers=headers, params=params)
        resp.raise_for_status()
        return resp.json().get("data", [])

    def schedule_content(self, content_data: Dict[str, Any], scheduled_time: str) -> Dict[str, Any]:
        """
        Schedule a tweet for future posting
        Note: Twitter API v2 doesn't natively support scheduling tweets.
        This would typically be handled by a third-party service or our own scheduling system.
        """
        # For now, we'll raise NotImplementedError as Twitter API doesn't support this directly
        raise NotImplementedError("Twitter API v2 does not support scheduling tweets directly. Use application-level scheduling.")

    def upload_media(self, media_path: str, media_type: str) -> str:
        """
        Upload media to Twitter
        """
        # This is a simplified implementation
        url = f"{self.api_base_url}/media/upload"
        headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }
        
        with open(media_path, 'rb') as media:
            files = {'media': media}
            resp = requests.post(url, headers=headers, files=files)
        
        resp.raise_for_status()
        return resp.json().get("media_id_string", "")
