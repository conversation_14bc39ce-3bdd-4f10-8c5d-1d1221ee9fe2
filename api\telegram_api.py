from flask import Flask, request, jsonify, Blueprint
from functools import wraps
import os
from app.services.account_service import AccountService
from .vault import authenticate_telegram as authenticate  # Updated import path

# Create a Blueprint instead of a Flask app
telegram_bp = Blueprint('telegram', __name__)

# Load environment variables
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
TELEGRAM_API_RATE_LIMIT = int(os.getenv("TELEGRAM_API_RATE_LIMIT", 100))
TELEGRAM_WEBHOOK_SECRET = os.getenv("TELEGRAM_WEBHOOK_SECRET")

# Mock database
connected_channels = {}
verified_users = {}

# Authentication decorator
def authenticate(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get("Authorization")
        if not token or token.split(" ")[1] != TELEGRAM_BOT_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401
        return f(*args, **kwargs)
    return decorated_function

# Rate limiting decorator with improved implementation
def rate_limit(f):
    from datetime import datetime, timedelta
    from functools import wraps
    
    # Simple in-memory rate limiting
    rate_limit_store = {}
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_ip = request.remote_addr
        endpoint = request.path
        key = f"{client_ip}:{endpoint}"
        
        current_time = datetime.utcnow()
        
        # Clean up old entries
        for k in list(rate_limit_store.keys()):
            if rate_limit_store[k]['reset_time'] < current_time:
                del rate_limit_store[k]
        
        # Check if client is rate limited
        if key in rate_limit_store:
            entry = rate_limit_store[key]
            if entry['count'] >= TELEGRAM_API_RATE_LIMIT:
                return jsonify({
                    "error": "Rate limit exceeded",
                    "retry_after": (entry['reset_time'] - current_time).seconds
                }), 429
            
            # Increment count
            entry['count'] += 1
        else:
            # Create new entry
            reset_time = current_time + timedelta(minutes=15)
            rate_limit_store[key] = {
                'count': 1,
                'reset_time': reset_time
            }
        
        return f(*args, **kwargs)
    
    return decorated_function

@telegram_bp.route('/verify-code', methods=['POST'])
@rate_limit
def verify_code():
    data = request.json
    telegram_user_id = data.get("telegram_user_id")
    code = data.get("code")
    channel_id = data.get("channel_id")
    channel_title = data.get("channel_title", "")
    
    if not telegram_user_id or not code:
        return jsonify({"error": "Missing required parameters"}), 400
    
    # Verify the code and connect the Telegram account
    success, result = AccountService.verify_platform_code(
        code=code,
        platform="telegram",
        platform_user_id=telegram_user_id
    )
    
    # Add return statement
    if success:
        return jsonify({"message": "Verification successful", "data": result}), 200
    else:
        return jsonify({"error": "Verification failed", "message": result}), 400

@telegram_bp.route('/connect-channel', methods=['POST'])
@authenticate
@rate_limit
def connect_channel():
    data = request.json
    telegram_user_id = data.get("telegram_user_id")
    channel_id = data.get("channel_id")
    channel_title = data.get("channel_title", "")
    
    if not telegram_user_id or not channel_id:
        return jsonify({"error": "Missing required parameters"}), 400
    
    if telegram_user_id not in verified_users:
        return jsonify({"error": "User not verified"}), 401
    
    try:
        from app.models.account import Account, Platform, AccountStatus
        
        # Find the user account associated with this telegram_user_id
        user_account = Account.objects(
            account_identifier=telegram_user_id,
            platform=Platform.TELEGRAM
        ).first()
        
        if not user_account:
            return jsonify({"error": "No Telegram account found for this user"}), 404
        
        # Check if channel already exists for this user
        existing_channel = Account.objects(
            user=user_account.user,
            platform=Platform.TELEGRAM,
            account_identifier=channel_id
        ).first()
        
        if existing_channel:
            return jsonify({"message": "Channel already connected"}), 200
        
        # Create new account for the channel
        channel_account = Account(
            user=user_account.user,
            platform=Platform.TELEGRAM,
            account_name=channel_title or f"Telegram Channel",
            account_identifier=channel_id,
            status=AccountStatus.CONNECTED
        )
        channel_account.save()
        
        # For backward compatibility
        connected_channels[channel_id] = telegram_user_id
        
        return jsonify({"message": "Channel connected successfully"}), 200
    except Exception as e:
        return jsonify({"error": f"Failed to connect channel: {str(e)}"}), 500

@telegram_bp.route('/get-connected-channels', methods=['GET'])
@authenticate
@rate_limit
def get_connected_channels():
    telegram_user_id = request.args.get("telegram_user_id")
    
    if not telegram_user_id:
        return jsonify({"error": "Missing telegram_user_id parameter"}), 400
    
    try:
        from app.models.account import Account, Platform
        
        # Find the user account associated with this telegram_user_id
        user_account = Account.objects(
            account_identifier=telegram_user_id,
            platform=Platform.TELEGRAM
        ).first()
        
        if not user_account:
            # Fallback to backward compatibility
            user_channels = [
                {"channel_id": k, "channel_title": f"Channel {k}"}
                for k, v in connected_channels.items()
                if v == telegram_user_id
            ]
            return jsonify({"channels": user_channels}), 200
        
        # Get channels from the database
        channels = Account.objects(
            user=user_account.user,
            platform=Platform.TELEGRAM,
            # Filter out the user's own account
            account_identifier__ne=telegram_user_id
        )
        
        user_channels = [
            {
                "channel_id": channel.account_identifier,
                "channel_title": channel.account_name
            }
            for channel in channels
        ]
        
        return jsonify({"channels": user_channels}), 200
    except Exception as e:
        return jsonify({"error": f"Failed to retrieve channels: {str(e)}"}), 500

@telegram_bp.route('/verify-forwarded-message', methods=['POST'])
@authenticate
@rate_limit
def verify_forwarded_message():
    data = request.json
    channel_id = data.get("channel_id")
    if channel_id in connected_channels:
        return jsonify({"message": "Message is from the connected channel"}), 200
    return jsonify({"error": "Message not found or channel mismatch"}), 404

@telegram_bp.route('/set-webhook', methods=['POST'])
@authenticate
@rate_limit
def set_webhook():
    data = request.json
    webhook_url = data.get("webhook_url")
    if not webhook_url.startswith("https://"):
        return jsonify({"error": "Invalid URL format"}), 400
    # Logic to set webhook
    return jsonify({"message": "Webhook set successfully"}), 200

@telegram_bp.route('/remove-webhook', methods=['POST'])
@authenticate
@rate_limit
def remove_webhook():
    # Logic to remove webhook
    return jsonify({"message": "Webhook removed successfully"}), 200

# Function to register the blueprint with the main Flask app
def register_telegram_api(app):
    app.register_blueprint(telegram_bp, url_prefix='/api/telegram')
