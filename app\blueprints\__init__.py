# Import all blueprints
from .vault_blueprint import vault_bp
from .dashboard_blueprint import dashboard_bp
from .post_blueprint import posts_bp, post_bp, content_bp
from .strategy_blueprint import strategy_bp
from .comment_blueprint import comments_bp
from .agent_blueprint import agent_bp
from .insight_blueprint import insight_bp
from .payment_blueprint import payment_bp
from .systemSetting_blueprint import system_setting_bp
from .landing_blueprint import landing_bp
from .scheduling_blueprint import scheduling_bp
from .userSetting_Blueprint import user_settings_bp
from .log_blueprint import log_bp
from .blog_blueprint import blog_bp

def register_blueprints(app):
    """Register all blueprints with the Flask app"""
    app.register_blueprint(landing_bp)  # Register landing page blueprint first
    app.register_blueprint(vault_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(posts_bp)
    app.register_blueprint(content_bp)
    app.register_blueprint(strategy_bp)
    app.register_blueprint(comments_bp)
    app.register_blueprint(agent_bp)
    app.register_blueprint(insight_bp)
    app.register_blueprint(payment_bp)
    app.register_blueprint(system_setting_bp)
    app.register_blueprint(scheduling_bp)
    app.register_blueprint(user_settings_bp)
    app.register_blueprint(blog_bp)

