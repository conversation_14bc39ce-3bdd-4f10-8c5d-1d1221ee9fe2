{% extends "admin/admin_layout.html" %}

{% block title %}مدیریت پست‌ها - Rominext{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center py-3">
          <div>
            <h5 class="mb-0">مدیریت پست‌ها</h5>
            <p class="text-muted mb-0 small">مدیریت محتوای منتشر شده در پلتفرم‌های مختلف</p>
          </div>
          <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPostModal">
            <i class="fas fa-plus me-1"></i> افزودن پست جدید
          </button>
        </div>
        
        <div class="card-body">
          <!-- Filters -->
          <div class="row g-3 mb-4">
            <div class="col-md-3">
              <div class="input-group">
                <input type="text" class="form-control" placeholder="جستجو با نام یا محتوا..." id="searchInput">
                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>
            <div class="col-md-2">
              <select class="form-select" id="filterPlatform">
                <option value="همه">پلتفرم: همه</option>
                <option value="instagram">اینستاگرام</option>
                <option value="telegram">تلگرام</option>
                <option value="twitter">توییتر</option>
                <option value="blog">بلاگ</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" id="filterStatus">
                <option value="همه">وضعیت: همه</option>
                <option value="published">منتشر شده</option>
                <option value="scheduled">زمان‌بندی شده</option>
                <option value="draft">پیش‌نویس</option>
              </select>
            </div>
            <div class="col-md-3">
              <button class="btn btn-primary" id="filterBtn">اعمال فیلتر</button>
              <button class="btn btn-outline-secondary" id="resetFilterBtn">حذف فیلتر</button>
            </div>
          </div>
          
          <!-- Table -->
          <div class="table-responsive">
            <table class="table align-items-center mb-0">
              <thead>
                <tr>
                  <th class="text-end" style="width: 15%;">عنوان</th>
                  <th class="text-end" style="width: 25%;">محتوا</th>
                  <th class="text-end" style="width: 10%;">پلتفرم</th>
                  <th class="text-end" style="width: 10%;">وضعیت</th>
                  <th class="text-end" style="width: 15%;">تاریخ انتشار</th>
                  <th class="text-end" style="width: 15%;">تعامل</th>
                  <th class="text-center" style="width: 10%;">عملیات</th>
                </tr>
              </thead>
              <tbody id="postsTableBody">
                {% for post in posts %}
                <tr>
                  <td style="width: 15%;">{{ post.title|default('بدون عنوان') }}</td>
                  <td style="width: 25%;" class="text-truncate">{{ post.content[:50] ~ '...' if post.content|length > 50 else post.content }}</td>
                  <td style="width: 10%;">
                    {% if post.platform == 'instagram' %}
                    <span><i class="fab fa-instagram text-instagram me-1"></i> اینستاگرام</span>
                    {% elif post.platform == 'telegram' %}
                    <span><i class="fab fa-telegram-plane text-telegram me-1"></i> تلگرام</span>
                    {% elif post.platform == 'twitter' %}
                    <span><i class="fab fa-twitter text-twitter me-1"></i> توییتر</span>
                    {% elif post.platform == 'blog' %}
                    <span><i class="fas fa-rss text-blog me-1"></i> بلاگ</span>
                    {% endif %}
                  </td>
                  <td style="width: 10%;">
                    {% if post.scheduled %}
                    <span class="badge bg-warning text-dark"><i class="far fa-clock me-1"></i> زمان‌بندی شده</span>
                    {% elif post.published %}
                    <span class="badge bg-success"><i class="fas fa-check me-1"></i> منتشر شده</span>
                    {% else %}
                    <span class="badge bg-secondary"><i class="fas fa-pencil-alt me-1"></i> پیش‌نویس</span>
                    {% endif %}
                  </td>
                  <td style="width: 15%;">{{ post.publish_date.strftime('%Y-%m-%d') if post.publish_date else 'نامشخص' }}</td>
                  <td style="width: 15%;">
                    <span class="me-2"><i class="fas fa-heart text-danger me-1"></i> {{ post.likes|default(0) }}</span>
                    <span><i class="fas fa-comment text-primary me-1"></i> {{ post.comments|default(0) }}</span>
                  </td>
                  <td class="text-center" style="width: 10%;">
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        عملیات
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="editPost('{{ post.id }}')"><i class="fas fa-edit me-1"></i> ویرایش</a></li>
                        <li><a class="dropdown-item" href="#" onclick="viewPostDetails('{{ post.id }}')"><i class="fas fa-info-circle me-1"></i> مشاهده جزئیات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="viewAnalytics('{{ post.id }}')"><i class="fas fa-chart-line me-1"></i> آنالیز</a></li>
                        {% if not post.published %}
                        <li><a class="dropdown-item text-success" href="#" onclick="publishPost('{{ post.id }}')"><i class="fas fa-paper-plane me-1"></i> انتشار</a></li>
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeletePost('{{ post.id }}')"><i class="fas fa-trash-alt me-1"></i> حذف</a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
              <span>نمایش {{ posts|length }} از {{ total_posts }} پست</span>
            </div>
            <nav aria-label="Page navigation">
              <ul class="pagination">
                <li class="page-item {% if current_page == 1 %}disabled{% endif %}">
                  <a class="page-link" href="{{ url_for('admin.posts', page=current_page-1) if current_page > 1 else '#' }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
                {% for i in range(1, total_pages + 1) %}
                <li class="page-item {% if current_page == i %}active{% endif %}">
                  <a class="page-link" href="{{ url_for('admin.posts', page=i) }}">{{ i }}</a>
                </li>
                {% endfor %}
                <li class="page-item {% if current_page == total_pages %}disabled{% endif %}">
                  <a class="page-link" href="{{ url_for('admin.posts', page=current_page+1) if current_page < total_pages else '#' }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Post Modal -->
<div class="modal fade" id="addPostModal" tabindex="-1" aria-labelledby="addPostModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addPostModalLabel">افزودن پست جدید</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ url_for('admin.add_post') }}" method="POST" enctype="multipart/form-data">
        <div class="modal-body">
          <div class="mb-3">
            <label for="title" class="form-label">عنوان</label>
            <input type="text" class="form-control" id="title" name="title">
          </div>
          <div class="mb-3">
            <label for="content" class="form-label">محتوا</label>
            <textarea class="form-control" id="content" name="content" rows="4"></textarea>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="platform" class="form-label">پلتفرم</label>
                <select class="form-select" id="platform" name="platform">
                  <option value="instagram">اینستاگرام</option>
                  <option value="telegram">تلگرام</option>
                  <option value="twitter">توییتر</option>
                  <option value="blog">بلاگ</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="status" class="form-label">وضعیت</label>
                <select class="form-select" id="status" name="status">
                  <option value="draft">پیش‌نویس</option>
                  <option value="published">انتشار</option>
                  <option value="scheduled">زمان‌بندی</option>
                </select>
              </div>
            </div>
          </div>
          <div class="mb-3" id="scheduleDateContainer" style="display: none;">
            <label for="scheduleDate" class="form-label">تاریخ انتشار</label>
            <input type="datetime-local" class="form-control" id="scheduleDate" name="schedule_date">
          </div>
          <div class="mb-3">
            <label for="image" class="form-label">تصویر</label>
            <input type="file" class="form-control" id="image" name="image">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
          <button type="submit" class="btn btn-primary">ذخیره</button>
        </div>
      </form>
    </div>
  </div>
</div>

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Show/hide schedule date based on status selection
    const statusSelect = document.getElementById('status');
    const scheduleDateContainer = document.getElementById('scheduleDateContainer');
    
    if (statusSelect) {
      statusSelect.addEventListener('change', function() {
        if (this.value === 'scheduled') {
          scheduleDateContainer.style.display = 'block';
        } else {
          scheduleDateContainer.style.display = 'none';
        }
      });
    }
    
    // Filter functionality
    const searchInput = document.getElementById('searchInput');
    const filterPlatform = document.getElementById('filterPlatform');
    const filterStatus = document.getElementById('filterStatus');
    const filterBtn = document.getElementById('filterBtn');
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    
    if (filterBtn) {
      filterBtn.addEventListener('click', function() {
        applyFilters();
      });
    }
    
    if (resetFilterBtn) {
      resetFilterBtn.addEventListener('click', function() {
        // Reset form fields
        if (searchInput) searchInput.value = '';
        if (filterPlatform) filterPlatform.value = 'همه';
        if (filterStatus) filterStatus.value = 'همه';
        
        // Apply filters (which will now be empty)
        applyFilters();
      });
    }
    
    function applyFilters() {
      // Build query parameters
      const params = new URLSearchParams();
      
      // Add current page
      params.append('page', 1); // Reset to page 1 when filtering
      
      // Add search term if provided
      if (searchInput && searchInput.value) {
        params.append('search', searchInput.value);
      }
      
      // Add platform filter if not "all"
      if (filterPlatform && filterPlatform.value !== 'همه') {
        params.append('platform', filterPlatform.value);
      }
      
      // Add status filter if not "all"
      if (filterStatus && filterStatus.value !== 'همه') {
        params.append('status', filterStatus.value);
      }
      
      // Redirect to the same page with filters
      window.location.href = `${window.location.pathname}?${params.toString()}`;
    }
    
    // Delete confirmation
    window.confirmDeletePost = function(postId) {
      if (confirm('آیا از حذف این پست اطمینان دارید؟')) {
        window.location.href = `/admin/posts/delete/${postId}`;
      }
    };
    
    // Edit post function
    window.editPost = function(postId) {
      window.location.href = `/admin/posts/edit/${postId}`;
    };
    
    // View post details
    window.viewPostDetails = function(postId) {
      window.location.href = `/admin/posts/view/${postId}`;
    };
    
    // View analytics
    window.viewAnalytics = function(postId) {
      window.location.href = `/admin/posts/analytics/${postId}`;
    };
    
    // Publish post
    window.publishPost = function(postId) {
      if (confirm('آیا از انتشار این پست اطمینان دارید؟')) {
        window.location.href = `/admin/posts/publish/${postId}`;
      }
    };
  });
</script>
{% endblock %}
{% endblock %}
