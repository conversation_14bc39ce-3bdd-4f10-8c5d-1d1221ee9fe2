from mongoengine import Document, <PERSON><PERSON>ield, <PERSON>Field, FloatField, DateTimeField, EnumField
from datetime import datetime
from enum import Enum
from .post import Post
from .comment import Comment

class SentimentType(str, Enum):
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"

class CommentAnalytics(Document):
    """Store sentiment analysis results for comments"""
    post = ReferenceField(Post, required=True)
    comment = ReferenceField(Comment, required=True)
    sentiment = EnumField(SentimentType, default=SentimentType.NEUTRAL)
    sentiment_score = FloatField()  # Score between -1.0 and 1.0
    created_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'comment_analytics',
        'indexes': ['post', 'comment', 'sentiment', 'created_at']
    }