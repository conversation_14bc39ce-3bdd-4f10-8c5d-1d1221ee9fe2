# Admin Settings System Documentation

## Overview

The Rominext admin panel now includes a comprehensive settings system that allows administrators to configure various aspects of the application through a user-friendly web interface. The system is organized into multiple sub-pages for different setting categories.

## Features Implemented

### 1. Settings Navigation
- **Dropdown Menu**: Settings section in admin sidebar with collapsible submenu
- **Active State Tracking**: Proper highlighting of current settings page
- **Persian/RTL Support**: All interfaces support Persian language and RTL layout

### 2. Settings Categories

#### General Settings (`/admin/settings/general`)
- **Site Information**: Site name, description, URL, admin email
- **Localization**: Default language and timezone selection
- **System Status**: Maintenance mode and registration toggles
- **Initialize Button**: One-click setup of default settings

#### Email Settings (`/admin/settings/email`)
- **SMTP Configuration**: Host, port, username, password
- **Security Options**: TLS/SSL toggles
- **Email Identity**: From name, address, reply-to
- **Password Toggle**: Show/hide password fields
- **Test Integration**: Direct link to email testing

#### API Settings (`/admin/settings/api`)
- **AI Providers**: OpenAI, Groq, DeepSeek, Grok, etc.
- **Social Media**: Facebook Page tokens, Telegram bot
- **Provider Cards**: Visual organization with logos and descriptions
- **Security**: Password-masked API keys with toggle visibility
- **Default Provider**: Selection of primary AI provider

#### Security Settings (`/admin/settings/security`)
- **Password Policy**: Minimum length, complexity requirements
- **Session Management**: Timeout configuration
- **Login Security**: Max attempts, lockout duration
- **Advanced Features**: 2FA, email verification, CAPTCHA
- **Interactive Controls**: Sliders and switches for better UX

#### System Settings (`/admin/settings/system`)
- **User Limits**: Max posts and accounts per user
- **File Management**: Upload size limits, allowed file types
- **Feature Toggles**: Analytics, scheduling, AI generation
- **Maintenance**: Backup and log retention settings
- **Debug Mode**: Development settings with warnings

#### Database Settings (`/admin/settings/database`)
- **Connection Settings**: Pool size, query timeout
- **Performance**: Database logging options
- **Backup Configuration**: Auto-backup settings and frequency
- **Statistics Display**: Real-time database stats
- **Maintenance Tools**: Optimize, cleanup, backup, repair buttons

### 3. Backend Implementation

#### SystemSetting Model
```python
class SystemSetting(Document):
    key = StringField(required=True, unique=True)
    value = StringField(required=True)
    description = StringField()
    updated_at = DateTimeField(default=datetime.utcnow)
```

#### SystemSettingService
- **CRUD Operations**: Create, read, update, delete settings
- **Bulk Operations**: Get all settings, settings by prefix
- **Default Initialization**: Automatic setup of default values
- **Error Handling**: Comprehensive logging and error management

#### Admin Routes
- **8 Main Routes**: Settings overview and 6 category pages
- **Form Handling**: POST request processing with validation
- **Flash Messages**: User feedback for all operations
- **Security**: Admin authentication required for all routes

### 4. User Interface Features

#### Design Elements
- **Gradient Headers**: Color-coded headers for each category
- **Card Layout**: Organized sections with hover effects
- **Icon Integration**: FontAwesome icons throughout
- **Responsive Design**: Mobile-friendly layouts
- **Persian Typography**: Proper RTL text rendering

#### Interactive Components
- **Range Sliders**: For numeric settings with live updates
- **Toggle Switches**: For boolean settings
- **Password Fields**: With show/hide functionality
- **Select Dropdowns**: For predefined options
- **Action Buttons**: Save, reset, navigation

#### User Experience
- **Form Validation**: Client and server-side validation
- **Loading States**: Visual feedback for long operations
- **Confirmation Dialogs**: For destructive actions
- **Help Text**: Descriptive text for all settings
- **Navigation Flow**: Logical progression between pages

## Usage Instructions

### Accessing Settings
1. Log in to admin panel at `/admin`
2. Click on "تنظیمات" (Settings) in the sidebar
3. Select desired category from dropdown menu

### Configuring Settings
1. Navigate to appropriate settings page
2. Modify desired values using form controls
3. Click "ذخیره تنظیمات" (Save Settings) button
4. Confirm success message appears

### Initializing Defaults
1. Go to General Settings page
2. Click "مقداردهی اولیه" (Initialize) button
3. Confirm the action in dialog
4. System will populate all default values

### Testing Configuration
- **Email Settings**: Use "تست ایمیل" button to verify SMTP
- **Database**: Use maintenance tools to check connectivity
- **API Keys**: Test through respective service integrations

## Technical Details

### File Structure
```
app/
├── blueprints/admin_blueprint.py      # Settings routes
├── models/system_setting.py           # Database model
├── services/system_setting_service.py # Business logic
└── templates/admin/settings/          # UI templates
    ├── general.html
    ├── email.html
    ├── api.html
    ├── security.html
    ├── system.html
    └── database.html
```

### Database Schema
- **Collection**: `system_settings`
- **Indexes**: `key` field for fast lookups
- **Validation**: Unique keys, required values

### Security Considerations
- **Admin Authentication**: All routes protected
- **Input Validation**: Server-side validation for all inputs
- **Sensitive Data**: API keys and passwords properly masked
- **CSRF Protection**: Form tokens for security
- **Audit Trail**: All changes logged with timestamps

## Default Settings

The system initializes with 40+ default settings covering:
- Site configuration
- Email SMTP settings
- Security policies
- System limits
- Database configuration
- Feature toggles

## Future Enhancements

### Planned Features
- **Settings Import/Export**: Backup and restore configurations
- **Environment Sync**: Sync with .env file
- **Setting Groups**: Organize related settings
- **Change History**: Track setting modifications
- **Validation Rules**: Advanced input validation
- **Setting Dependencies**: Conditional setting visibility

### Integration Points
- **Email System**: Direct integration with email service
- **AI Providers**: Dynamic provider configuration
- **Social Media**: Platform-specific settings
- **Analytics**: Configuration for tracking systems
- **Backup System**: Automated backup scheduling

## Troubleshooting

### Common Issues
1. **Settings Not Saving**: Check admin permissions and database connectivity
2. **Default Values Missing**: Run initialization from General Settings
3. **Email Test Failing**: Verify SMTP credentials in Email Settings
4. **UI Not Loading**: Check template file permissions and paths

### Debug Mode
Enable debug mode in System Settings for detailed error information during development.

## Conclusion

The admin settings system provides a comprehensive, user-friendly interface for managing all aspects of the Rominext application. With its modular design, extensive validation, and intuitive UI, administrators can easily configure the system to meet their specific requirements.
