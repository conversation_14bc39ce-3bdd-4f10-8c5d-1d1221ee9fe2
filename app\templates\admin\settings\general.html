{% extends "admin/admin_layout.html" %}

{% block title %}تنظیمات عمومی - Rominext{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.settings-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header settings-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-globe me-2"></i>
                            تنظیمات عمومی سیستم
                        </h5>
                        <div>
                            <button type="button" class="btn btn-light btn-sm" onclick="initializeSettings()">
                                <i class="fas fa-sync me-1"></i>
                                مقداردهی اولیه
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.settings_general') }}">
                        <!-- Site Information Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-info-circle me-2"></i>
                                اطلاعات سایت
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="site_name" class="form-label">نام سایت</label>
                                    <input type="text" class="form-control" id="site_name" name="site_name" 
                                           value="{{ settings.get('site_name', 'Rominext') }}" required>
                                    <div class="form-text">نام سایت که در سراسر سیستم نمایش داده می‌شود</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="site_description" class="form-label">توضیحات سایت</label>
                                    <input type="text" class="form-control" id="site_description" name="site_description" 
                                           value="{{ settings.get('site_description', 'AI-Powered Social Media Management') }}">
                                    <div class="form-text">توضیح کوتاه درباره سایت</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="site_url" class="form-label">آدرس سایت</label>
                                    <input type="url" class="form-control" id="site_url" name="site_url" 
                                           value="{{ settings.get('site_url', 'https://rominext.com') }}">
                                    <div class="form-text">آدرس کامل سایت</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_email" class="form-label">ایمیل مدیر</label>
                                    <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                           value="{{ settings.get('admin_email', '<EMAIL>') }}">
                                    <div class="form-text">ایمیل مدیر اصلی سیستم</div>
                                </div>
                            </div>
                        </div>

                        <!-- Localization Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-language me-2"></i>
                                تنظیمات محلی‌سازی
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="default_language" class="form-label">زبان پیش‌فرض</label>
                                    <select class="form-select" id="default_language" name="default_language">
                                        <option value="en" {% if settings.get('default_language') == 'en' %}selected{% endif %}>English</option>
                                        <option value="fa" {% if settings.get('default_language') == 'fa' %}selected{% endif %}>فارسی</option>
                                        <option value="es" {% if settings.get('default_language') == 'es' %}selected{% endif %}>Español</option>
                                        <option value="fr" {% if settings.get('default_language') == 'fr' %}selected{% endif %}>Français</option>
                                    </select>
                                    <div class="form-text">زبان پیش‌فرض سیستم</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="default_timezone" class="form-label">منطقه زمانی پیش‌فرض</label>
                                    <select class="form-select" id="default_timezone" name="default_timezone">
                                        <option value="UTC" {% if settings.get('default_timezone') == 'UTC' %}selected{% endif %}>UTC</option>
                                        <option value="Asia/Tehran" {% if settings.get('default_timezone') == 'Asia/Tehran' %}selected{% endif %}>Asia/Tehran</option>
                                        <option value="America/New_York" {% if settings.get('default_timezone') == 'America/New_York' %}selected{% endif %}>America/New_York</option>
                                        <option value="Europe/London" {% if settings.get('default_timezone') == 'Europe/London' %}selected{% endif %}>Europe/London</option>
                                    </select>
                                    <div class="form-text">منطقه زمانی پیش‌فرض سیستم</div>
                                </div>
                            </div>
                        </div>

                        <!-- System Status Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-toggle-on me-2"></i>
                                وضعیت سیستم
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                               {% if settings.get('maintenance_mode') == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="maintenance_mode">
                                            حالت تعمیر و نگهداری
                                        </label>
                                        <div class="form-text">فعال‌سازی حالت تعمیر و نگهداری سایت</div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="registration_enabled" name="registration_enabled" 
                                               {% if settings.get('registration_enabled', 'true') == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="registration_enabled">
                                            ثبت‌نام کاربران جدید
                                        </label>
                                        <div class="form-text">اجازه ثبت‌نام کاربران جدید</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    ذخیره تنظیمات
                                </button>
                                <button type="reset" class="btn btn-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    بازنشانی
                                </button>
                            </div>
                            <div>
                                <a href="{{ url_for('admin.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    بازگشت به داشبورد
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function initializeSettings() {
    if (confirm('آیا مطمئن هستید که می‌خواهید تنظیمات پیش‌فرض را مقداردهی کنید؟')) {
        fetch('{{ url_for("admin.settings_initialize") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('خطا در مقداردهی تنظیمات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطا در مقداردهی تنظیمات');
        });
    }
}
</script>
{% endblock %}
