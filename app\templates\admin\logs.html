{% extends "admin/admin_layout.html" %}

{% block title %}گزارش‌های سیستم - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">گزارش‌های سیستم</h5>
          <div>
            <select class="form-select form-select-sm" id="logLevel" onchange="filterLogs()">
              <option value="all" {% if current_level == 'all' %}selected{% endif %}>همه سطوح</option>
              <option value="INFO" {% if current_level == 'INFO' %}selected{% endif %}>اطلاعات</option>
              <option value="WARNING" {% if current_level == 'WARNING' %}selected{% endif %}>هشدار</option>
              <option value="ERROR" {% if current_level == 'ERROR' %}selected{% endif %}>خطا</option>
              <option value="SUCCESS" {% if current_level == 'SUCCESS' %}selected{% endif %}>موفقیت</option>
            </select>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>سطح</th>
                  <th>دسته‌بندی</th>
                  <th>پیام</th>
                  <th>منبع</th>
                  <th>تاریخ</th>
                </tr>
              </thead>
              <tbody>
                {% if logs %}
                  {% for log in logs %}
                    <tr>
                      <td>
                        {% if log.level == 'ERROR' %}
                          <span class="badge bg-danger">خطا</span>
                        {% elif log.level == 'WARNING' %}
                          <span class="badge bg-warning">هشدار</span>
                        {% elif log.level == 'SUCCESS' %}
                          <span class="badge bg-success">موفقیت</span>
                        {% else %}
                          <span class="badge bg-info">اطلاعات</span>
                        {% endif %}
                      </td>
                      <td>{{ log.category }}</td>
                      <td>
                        <span class="d-inline-block text-truncate" style="max-width: 300px;">
                          {{ log.message }}
                        </span>
                        <button class="btn btn-sm btn-link p-0 ms-2" 
                                data-bs-toggle="modal" 
                                data-bs-target="#logModal{{ log.id }}">
                          <i class="fas fa-eye"></i>
                        </button>
                      </td>
                      <td>{{ log.source }}</td>
                      <td>{{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    
                    <!-- Modal for log details -->
                    <div class="modal fade" id="logModal{{ log.id }}" tabindex="-1" aria-hidden="true">
                      <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h5 class="modal-title">جزئیات گزارش</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          </div>
                          <div class="modal-body">
                            <div class="mb-3">
                              <h6>پیام:</h6>
                              <p>{{ log.message }}</p>
                            </div>
                            {% if log.details %}
                            <div class="mb-3">
                              <h6>جزئیات:</h6>
                              <pre class="bg-light p-3 rounded"><code>{{ log.details }}</code></pre>
                            </div>
                            {% endif %}
                            <div class="row">
                              <div class="col-md-6">
                                <p><strong>سطح:</strong> {{ log.level }}</p>
                                <p><strong>دسته‌بندی:</strong> {{ log.category }}</p>
                              </div>
                              <div class="col-md-6">
                                <p><strong>منبع:</strong> {{ log.source }}</p>
                                <p><strong>تاریخ:</strong> {{ log.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                              </div>
                            </div>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="5" class="text-center">هیچ گزارشی یافت نشد.</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          {% if total_pages > 1 %}
          <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
              <li class="page-item {% if current_page == 1 %}disabled{% endif %}">
                <a class="page-link" href="{{ url_for('admin.logs', page=current_page-1, level=current_level) }}" aria-label="Previous">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
              
              {% for i in range(1, total_pages + 1) %}
                {% if i == current_page %}
                  <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% elif i <= 3 or i >= total_pages - 2 or (i >= current_page - 1 and i <= current_page + 1) %}
                  <li class="page-item"><a class="page-link" href="{{ url_for('admin.logs', page=i, level=current_level) }}">{{ i }}</a></li>
                {% elif i == 4 and current_page > 5 or i == total_pages - 3 and current_page < total_pages - 4 %}
                  <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}
              {% endfor %}
              
              <li class="page-item {% if current_page == total_pages %}disabled{% endif %}">
                <a class="page-link" href="{{ url_for('admin.logs', page=current_page+1, level=current_level) }}" aria-label="Next">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
            </ul>
          </nav>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  function filterLogs() {
    const level = document.getElementById('logLevel').value;
    window.location.href = "{{ url_for('admin.logs') }}" + "?level=" + level;
  }
</script>
{% endblock %}