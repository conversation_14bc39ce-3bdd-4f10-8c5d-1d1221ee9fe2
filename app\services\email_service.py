import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.utils import formataddr
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from jinja2 import Template
import logging

from ..models.email_template import EmailTemplate, EmailLog
from ..models.user import User
from ..config import get_settings

logger = logging.getLogger(__name__)

class EmailService:
    """
    Advanced email service with multilingual template support
    """
    
    def __init__(self):
        self.settings = get_settings()
        self._smtp_connection = None
        
    def _get_smtp_connection(self):
        """Get SMTP connection with proper configuration"""
        try:
            if self.settings.SMTP_USE_SSL:
                server = smtplib.SMTP_SSL(self.settings.SMTP_HOST, self.settings.SMTP_PORT)
            else:
                server = smtplib.SMTP(self.settings.SMTP_HOST, self.settings.SMTP_PORT)
                if self.settings.SMTP_USE_TLS:
                    server.starttls()
            
            if self.settings.SMTP_USERNAME and self.settings.SMTP_PASSWORD:
                server.login(self.settings.SMTP_USERNAME, self.settings.SMTP_PASSWORD)
            
            return server
        except Exception as e:
            logger.error(f"Failed to connect to SMTP server: {str(e)}")
            raise
    
    def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str = None,
        text_content: str = None,
        to_name: str = None,
        from_email: str = None,
        from_name: str = None,
        reply_to: str = None,
        attachments: List[Dict] = None,
        template_key: str = None,
        user: User = None
    ) -> Dict[str, Any]:
        """
        Send an email with optional attachments
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML content
            text_content: Plain text content
            to_name: Recipient name
            from_email: Sender email (defaults to config)
            from_name: Sender name (defaults to config)
            reply_to: Reply-to address
            attachments: List of attachment dictionaries
            template_key: Template key for logging
            user: User object for logging
            
        Returns:
            Dictionary with success status and details
        """
        # Generate tracking ID
        tracking_id = str(uuid.uuid4())
        
        # Create email log entry
        email_log = EmailLog(
            template_key=template_key,
            recipient_email=to_email,
            recipient_name=to_name,
            recipient_user=user,
            subject=subject,
            sender_email=from_email or self.settings.EMAIL_FROM_ADDRESS,
            sender_name=from_name or self.settings.EMAIL_FROM_NAME,
            status='pending',
            tracking_id=tracking_id
        )
        email_log.save()
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = formataddr((
                from_name or self.settings.EMAIL_FROM_NAME,
                from_email or self.settings.EMAIL_FROM_ADDRESS
            ))
            msg['To'] = formataddr((to_name or to_email, to_email))
            
            if reply_to:
                msg['Reply-To'] = reply_to
            elif self.settings.EMAIL_REPLY_TO:
                msg['Reply-To'] = self.settings.EMAIL_REPLY_TO
            
            # Add tracking headers
            msg['X-Tracking-ID'] = tracking_id
            
            # Add text content
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # Add HTML content with tracking
            if html_content:
                # Add tracking pixel and links
                tracked_html = self._add_tracking_to_html(html_content, tracking_id)
                html_part = MIMEText(tracked_html, 'html', 'utf-8')
                msg.attach(html_part)
            
            # Add attachments
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # Send email
            server = self._get_smtp_connection()
            server.send_message(msg)
            server.quit()
            
            # Update log
            email_log.status = 'sent'
            email_log.sent_at = datetime.utcnow()
            email_log.save()
            
            logger.info(f"Email sent successfully to {to_email} with tracking ID {tracking_id}")
            
            return {
                'success': True,
                'tracking_id': tracking_id,
                'message': 'Email sent successfully'
            }
            
        except Exception as e:
            # Update log with error
            email_log.status = 'failed'
            email_log.error_message = str(e)
            email_log.save()
            
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            
            return {
                'success': False,
                'error': str(e),
                'tracking_id': tracking_id
            }
    
    def send_template_email(
        self,
        template_key: str,
        to_email: str,
        variables: Dict[str, Any] = None,
        language: str = 'en',
        to_name: str = None,
        user: User = None,
        attachments: List[Dict] = None
    ) -> Dict[str, Any]:
        """
        Send email using a template
        
        Args:
            template_key: Template identifier
            to_email: Recipient email
            variables: Template variables
            language: Language code
            to_name: Recipient name
            user: User object
            attachments: List of attachments
            
        Returns:
            Dictionary with success status and details
        """
        try:
            # Get template
            template = EmailTemplate.objects(
                template_key=template_key,
                is_active=True
            ).first()
            
            if not template:
                raise ValueError(f"Template '{template_key}' not found or inactive")
            
            # Get content for language
            content = template.get_content(language)
            
            # Render template with variables
            variables = variables or {}
            
            # Add common variables
            variables.update({
                'site_name': self.settings.EMAIL_FROM_NAME,
                'current_year': datetime.now().year,
                'unsubscribe_url': self._generate_unsubscribe_url(to_email),
                'tracking_pixel_url': ''  # Will be set later
            })
            
            # Render content
            subject = self._render_template(content['subject'], variables)
            html_content = self._render_template(content['html_content'], variables) if content['html_content'] else None
            text_content = self._render_template(content['text_content'], variables) if content['text_content'] else None
            
            # Send email
            return self.send_email(
                to_email=to_email,
                subject=subject,
                html_content=html_content,
                text_content=text_content,
                to_name=to_name,
                template_key=template_key,
                user=user,
                attachments=attachments
            )
            
        except Exception as e:
            logger.error(f"Failed to send template email '{template_key}' to {to_email}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _render_template(self, template_string: str, variables: Dict[str, Any]) -> str:
        """Render Jinja2 template with variables"""
        if not template_string:
            return ''
        
        try:
            template = Template(template_string)
            return template.render(**variables)
        except Exception as e:
            logger.error(f"Template rendering error: {str(e)}")
            return template_string
    
    def _add_tracking_to_html(self, html_content: str, tracking_id: str) -> str:
        """Add tracking pixel and modify links for tracking"""
        # Add tracking pixel
        tracking_pixel = f'<img src="{self._get_base_url()}/email/track/open/{tracking_id}" width="1" height="1" style="display:none;" />'
        
        # Insert tracking pixel before closing body tag
        if '</body>' in html_content:
            html_content = html_content.replace('</body>', f'{tracking_pixel}</body>')
        else:
            html_content += tracking_pixel
        
        # TODO: Add link tracking by modifying href attributes
        
        return html_content
    
    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict):
        """Add attachment to email message"""
        try:
            with open(attachment['path'], 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
            
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {attachment.get("filename", os.path.basename(attachment["path"]))}'
            )
            msg.attach(part)
        except Exception as e:
            logger.error(f"Failed to add attachment {attachment.get('path')}: {str(e)}")
    
    def _generate_unsubscribe_url(self, email: str) -> str:
        """Generate unsubscribe URL"""
        # TODO: Implement proper unsubscribe URL generation
        return f"{self._get_base_url()}/email/unsubscribe?email={email}"
    
    def _get_base_url(self) -> str:
        """Get base URL for the application"""
        # TODO: Get from configuration
        return "http://localhost:5000"
    
    def test_smtp_connection(self) -> Dict[str, Any]:
        """Test SMTP connection"""
        try:
            server = self._get_smtp_connection()
            server.quit()
            return {'success': True, 'message': 'SMTP connection successful'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
