from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort, current_app
from flask_login import login_required, current_user
from ..services.blog_service import BlogService
from ..models import Blog
from marshmallow import Schema, fields, validate, ValidationError
from werkzeug.utils import secure_filename
import logging
import os
import uuid

# Get translation function
def translate(key, default=None):
    return current_app.translation_service.translate(key, default)

# Helper function to validate image files
def allowed_file(filename):
    """Check if file extension is allowed for images"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

blog_bp = Blueprint('blog', __name__, url_prefix='/blog')
logger = logging.getLogger(__name__)
blog_service = BlogService()

# Request validation schemas
class BlogSchema(Schema):
    title = fields.String(required=True, validate=validate.Length(min=3, max=200))
    content = fields.String(required=True)
    tags = fields.List(fields.String(), missing=[])
    featured_image = fields.String(missing=None)
    is_published = fields.Boolean(missing=False)

@blog_bp.route('/')
def index():
    """List all published blog posts"""
    page = request.args.get('page', 1, type=int)
    limit = 10
    skip = (page - 1) * limit
    
    blogs = blog_service.get_blogs(published_only=True, limit=limit, skip=skip)
    total = Blog.objects(is_published=True).count()
    
    return render_template('admin/blog/index.html',
                          blogs=blogs,
                          page=page,
                          total=total,
                          pages=(total // limit) + (1 if total % limit else 0))

@blog_bp.route('/<slug>')
def view(slug):
    """View a single blog post"""
    try:
        blog = blog_service.get_blog_by_slug(slug)
        
        # Only show published blogs to non-authors
        if not blog.is_published and (not current_user.is_authenticated or current_user.id != blog.user.id):
            abort(404)
            
        return render_template('admin/blog/view.html', blog=blog)
    except Exception as e:
        logger.error(f"Error viewing blog: {str(e)}")
        abort(404)

@blog_bp.route('/dashboard')
@login_required
def dashboard():
    """Blog management dashboard"""
    page = request.args.get('page', 1, type=int)
    limit = 10
    skip = (page - 1) * limit
    
    blogs = blog_service.get_blogs(user_id=current_user.id, limit=limit, skip=skip)
    total = Blog.objects(user=current_user.id).count()
    
    return render_template('admin/blog/dashboard.html',
                          blogs=blogs,
                          page=page,
                          total=total,
                          pages=(total // limit) + (1 if total % limit else 0))

@blog_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """Create a new blog post"""
    if request.method == 'POST':
        try:
            # Validate request data
            schema = BlogSchema()
            if request.is_json:
                data = schema.load(request.json)
            else:
                data = schema.load(request.form.to_dict())
                
                # Handle tags from form
                tags = request.form.get('tags', '')
                data['tags'] = [tag.strip() for tag in tags.split(',') if tag.strip()]
                
                # Handle featured image
                if 'featured_image' in request.files and request.files['featured_image'].filename:
                    image_file = request.files['featured_image']

                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'blog')
                    os.makedirs(upload_dir, exist_ok=True)

                    # Generate secure filename
                    filename = secure_filename(image_file.filename)
                    file_extension = os.path.splitext(filename)[1]
                    unique_filename = f"{uuid.uuid4()}{file_extension}"

                    # Save the file
                    file_path = os.path.join(upload_dir, unique_filename)
                    image_file.save(file_path)

                    # Store relative path for database
                    data['featured_image'] = f"uploads/blog/{unique_filename}"
            
            # Create blog
            blog = blog_service.create_blog(
                user_id=current_user.id,
                title=data['title'],
                content=data['content'],
                tags=data['tags'],
                featured_image=data['featured_image'],
                is_published=data['is_published']
            )
            
            if request.is_json:
                return jsonify({"success": True, "blog_id": str(blog.id), "slug": blog.slug})
            else:
                flash(translate('auth.blog_created_success'), 'success')
                return redirect(url_for('blog.dashboard'))

        except ValidationError as e:
            if request.is_json:
                return jsonify({"success": False, "errors": e.messages}), 400
            flash(translate('auth.blog_create_input_error'), 'danger')
        except Exception as e:
            logger.error(f"Error creating blog: {str(e)}")
            if request.is_json:
                return jsonify({"success": False, "error": str(e)}), 500
            flash(translate('auth.blog_create_general_error'), 'danger')
    
    return render_template('admin/blog/create.html')

@blog_bp.route('/edit/<blog_id>', methods=['GET', 'POST'])
@login_required
def edit(blog_id):
    """Edit a blog post"""
    try:
        blog = Blog.objects.get(id=blog_id)
        
        # Check ownership
        if str(blog.user.id) != str(current_user.id):
            abort(403)
            
        if request.method == 'POST':
            try:
                # Validate request data
                schema = BlogSchema()
                if request.is_json:
                    data = schema.load(request.json)
                else:
                    data = schema.load(request.form.to_dict())
                    
                    # Handle tags from form
                    tags = request.form.get('tags', '')
                    data['tags'] = [tag.strip() for tag in tags.split(',') if tag.strip()]
                    
                    # Handle featured image
                    if 'featured_image' in request.files and request.files['featured_image'].filename:
                        image_file = request.files['featured_image']

                        # Create uploads directory if it doesn't exist
                        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'blog')
                        os.makedirs(upload_dir, exist_ok=True)

                        # Generate secure filename
                        filename = secure_filename(image_file.filename)
                        file_extension = os.path.splitext(filename)[1]
                        unique_filename = f"{uuid.uuid4()}{file_extension}"

                        # Save the file
                        file_path = os.path.join(upload_dir, unique_filename)
                        image_file.save(file_path)

                        # Store relative path for database
                        data['featured_image'] = f"uploads/blog/{unique_filename}"

                        # Optionally delete old image file
                        if blog.featured_image and blog.featured_image.startswith('uploads/'):
                            old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'],
                                                       blog.featured_image.replace('uploads/', ''))
                            if os.path.exists(old_file_path):
                                try:
                                    os.remove(old_file_path)
                                except OSError:
                                    pass  # Ignore if file can't be deleted
                    else:
                        # Keep existing image if no new one uploaded
                        data['featured_image'] = blog.featured_image
                
                # Update blog
                updated_blog = blog_service.update_blog(
                    blog_id=blog_id,
                    **data
                )
                
                if request.is_json:
                    return jsonify({"success": True, "blog_id": str(updated_blog.id), "slug": updated_blog.slug})
                else:
                    flash(translate('auth.blog_updated_success'), 'success')
                    return redirect(url_for('blog.dashboard'))

            except ValidationError as e:
                if request.is_json:
                    return jsonify({"success": False, "errors": e.messages}), 400
                flash(translate('auth.blog_update_input_error'), 'danger')
            except Exception as e:
                logger.error(f"Error updating blog: {str(e)}")
                if request.is_json:
                    return jsonify({"success": False, "error": str(e)}), 500
                flash(translate('auth.blog_update_general_error'), 'danger')
        
        return render_template('admin/blog/edit.html', blog=blog)
    except Exception as e:
        logger.error(f"Error editing blog: {str(e)}")
        abort(404)

@blog_bp.route('/delete/<blog_id>', methods=['POST'])
@login_required
def delete(blog_id):
    """Delete a blog post"""
    try:
        blog = Blog.objects.get(id=blog_id)
        
        # Check ownership
        if str(blog.user.id) != str(current_user.id):
            if request.is_json:
                return jsonify({"success": False, "error": "Unauthorized"}), 403
            abort(403)
            
        # Delete blog
        blog_service.delete_blog(blog_id)
        
        if request.is_json:
            return jsonify({"success": True})
        else:
            flash(translate('auth.blog_deleted_success'), 'success')
            return redirect(url_for('blog.dashboard'))

    except Exception as e:
        logger.error(f"Error deleting blog: {str(e)}")
        if request.is_json:
            return jsonify({"success": False, "error": str(e)}), 500
        flash(translate('auth.blog_delete_general_error'), 'danger')
        return redirect(url_for('blog.dashboard'))




