# Advanced Email System Documentation

## Overview

The Rominext Advanced Email System is a comprehensive, multilingual email management solution designed to handle various email activities including user authentication, notifications, marketing campaigns, and system alerts.

## Features

### Core Features
- **Multilingual Support**: Templates in English and Persian (easily extensible)
- **Template Management**: Dynamic email templates with variable substitution
- **Queue System**: Background email processing with retry mechanisms
- **Analytics & Tracking**: Email delivery, open rates, and click tracking
- **SMTP Configuration**: Flexible SMTP server configuration
- **User Integration**: Seamless integration with user authentication system

### Email Types Supported
- Welcome emails for new users
- Password reset emails with secure tokens
- Email verification for account activation
- Post publication notifications
- Monthly analytics reports
- Marketing campaigns
- System alerts and notifications

## Architecture

### Components

1. **EmailService** (`app/services/email_service.py`)
   - Core email sending functionality
   - Template rendering with Jinja2
   - Attachment support
   - Tracking pixel and link injection

2. **EmailTemplateService** (`app/services/email_template_service.py`)
   - Template CRUD operations
   - Template validation
   - System template initialization

3. **EmailQueueService** (`app/services/email_queue_service.py`)
   - Background email processing
   - Retry mechanisms for failed emails
   - Bulk email handling
   - Priority-based queue management

4. **EmailAnalyticsService** (`app/services/email_analytics_service.py`)
   - Email tracking and analytics
   - Performance metrics calculation
   - Daily/monthly reporting

5. **EmailHelpers** (`app/utils/email_helpers.py`)
   - Convenience functions for common email operations
   - Token generation and validation
   - User authentication integration

### Database Models

1. **EmailTemplate** (`app/models/email_template.py`)
   - Stores multilingual email templates
   - Template metadata and variables
   - System vs user templates

2. **EmailLog** (`app/models/email_template.py`)
   - Email sending history
   - Tracking information
   - Status and error logging

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```bash
# Email Configuration
SMTP_HOST=your-smtp-host.com
SMTP_PORT=587
SMTP_USERNAME=your-username
SMTP_PASSWORD=your-password
SMTP_USE_TLS=true
SMTP_USE_SSL=false
EMAIL_FROM_NAME=Rominext
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>
```

### SMTP Providers

The system supports various SMTP providers:

#### Gmail
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
```

#### SendGrid
```bash
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=your-sendgrid-api-key
SMTP_USE_TLS=true
```

#### Amazon SES
```bash
SMTP_HOST=email-smtp.us-east-1.amazonaws.com
SMTP_PORT=587
SMTP_USERNAME=your-ses-username
SMTP_PASSWORD=your-ses-password
SMTP_USE_TLS=true
```

## Usage

### Basic Email Sending

```python
from app.services.email_service import EmailService

email_service = EmailService()

# Send simple email
result = email_service.send_email(
    to_email="<EMAIL>",
    subject="Welcome to Rominext",
    html_content="<h1>Welcome!</h1><p>Thank you for joining us.</p>",
    text_content="Welcome! Thank you for joining us."
)
```

### Template-Based Emails

```python
from app.services.email_queue_service import email_queue_service

# Send welcome email
queue_id = email_queue_service.send_welcome_email(
    user_email="<EMAIL>",
    user_name="John Doe",
    language="en"
)

# Send password reset email
from app.utils.email_helpers import EmailHelpers

result = EmailHelpers.send_password_reset_email(
    user=user_object,
    language="en"
)
```

### Creating Custom Templates

```python
from app.services.email_template_service import EmailTemplateService

template = EmailTemplateService.create_template(
    template_key="custom_notification",
    name="Custom Notification",
    category="notification",
    subjects={
        "en": "Important Update - {{site_name}}",
        "fa": "بروزرسانی مهم - {{site_name}}"
    },
    html_content={
        "en": "<h1>Update</h1><p>Hello {{user_name}}, {{message}}</p>",
        "fa": "<h1>بروزرسانی</h1><p>سلام {{user_name}}، {{message}}</p>"
    },
    variables=["site_name", "user_name", "message"]
)
```

### Bulk Email Campaigns

```python
from app.utils.email_helpers import EmailHelpers

# Send marketing email to multiple users
users = User.objects(marketing_emails_enabled=True)

queue_ids = EmailHelpers.send_bulk_marketing_email(
    users=users,
    template_key="monthly_newsletter",
    variables={"special_offer": "50% off premium features"},
    language="en"
)
```

## Template Variables

### Common Variables
- `{{site_name}}` - Site name (from configuration)
- `{{user_name}}` - User's name
- `{{current_year}}` - Current year
- `{{unsubscribe_url}}` - Unsubscribe link

### Authentication Variables
- `{{reset_link}}` - Password reset link
- `{{verification_link}}` - Email verification link
- `{{login_url}}` - Login page URL

### Notification Variables
- `{{post_content}}` - Social media post content
- `{{platform}}` - Social media platform name
- `{{post_url}}` - Link to published post

## Admin Interface

### Template Management
- Access: `/email/admin/templates`
- Features:
  - Create/edit/delete templates
  - Preview templates with sample data
  - Multilingual content management
  - Template duplication

### Analytics Dashboard
- Access: `/email/admin/analytics`
- Metrics:
  - Total emails sent
  - Delivery rates
  - Open rates
  - Click rates
  - Template performance comparison

### Email Testing
- Access: `/email/admin/test`
- Features:
  - SMTP connection testing
  - Send test emails
  - Template testing with sample data

## API Endpoints

### Send Email API
```http
POST /email/api/send
Content-Type: application/json

{
    "template_key": "welcome",
    "to_email": "<EMAIL>",
    "variables": {
        "user_name": "John Doe"
    },
    "language": "en"
}
```

### Email Tracking
- Open tracking: `/email/track/open/<tracking_id>`
- Click tracking: `/email/track/click/<tracking_id>?url=<target_url>`

## Security Features

### Token-Based Authentication
- Secure password reset tokens (32-byte URL-safe)
- Email verification tokens with expiration
- Unsubscribe token validation

### Email Validation
- MIME type validation
- Content sanitization
- Rate limiting (configurable)

### Privacy Protection
- Tracking pixel (1x1 transparent GIF)
- Secure unsubscribe mechanism
- GDPR-compliant data handling

## Monitoring and Logging

### Email Logs
All email activities are logged with:
- Recipient information
- Template used
- Delivery status
- Tracking events (opens, clicks)
- Error messages and retry attempts

### Analytics Tracking
- Real-time delivery status
- Open rate tracking with user agent and IP
- Click tracking with URL destinations
- Bounce and failure handling

## Troubleshooting

### Common Issues

1. **SMTP Connection Failed**
   - Check SMTP credentials
   - Verify firewall settings
   - Test with email provider's settings

2. **Templates Not Rendering**
   - Check template syntax
   - Verify variable names
   - Review template validation errors

3. **Emails Not Being Sent**
   - Check email queue status
   - Verify worker thread is running
   - Review error logs

### Testing Commands

```python
# Test SMTP connection
from app.services.email_service import EmailService
service = EmailService()
result = service.test_smtp_connection()

# Initialize system templates
from app.services.email_template_service import EmailTemplateService
EmailTemplateService.initialize_system_templates()

# Check queue status
from app.services.email_queue_service import email_queue_service
stats = email_queue_service.get_queue_stats()
```

## Best Practices

### Template Design
- Keep HTML simple and compatible
- Always provide text alternatives
- Use responsive design principles
- Test across different email clients

### Performance Optimization
- Use email queue for bulk operations
- Implement proper retry mechanisms
- Monitor delivery rates and adjust accordingly
- Regular cleanup of old email logs

### Security Guidelines
- Use secure SMTP connections (TLS/SSL)
- Implement rate limiting
- Validate all user inputs
- Regular security audits

## Future Enhancements

### Planned Features
- Email template editor with WYSIWYG interface
- A/B testing for email campaigns
- Advanced segmentation for bulk emails
- Integration with external email services (SendGrid, Mailgun)
- Email automation workflows
- Advanced analytics with conversion tracking

### Extensibility
The system is designed to be easily extensible:
- Add new template types
- Integrate with additional SMTP providers
- Implement custom tracking mechanisms
- Add new languages and localization
