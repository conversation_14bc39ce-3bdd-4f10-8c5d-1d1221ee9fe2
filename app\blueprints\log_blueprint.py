from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
import logging
from ..services.log_service import LogService
from ..models.log import LogLevel, LogCategory
from ..models.user import UserRole
from datetime import datetime

logger = logging.getLogger(__name__)
log_bp = Blueprint('log', __name__, url_prefix='/api/logs')

# Validation schemas
class LogFilterSchema(Schema):
    level = fields.String(missing=None)
    category = fields.String(missing=None)
    user_id = fields.String(missing=None)
    start_date = fields.DateTime(missing=None)
    end_date = fields.DateTime(missing=None)
    limit = fields.Integer(missing=100)
    skip = fields.Integer(missing=0)

@log_bp.route('/', methods=['GET'])
@login_required
def get_logs():
    """Get logs with optional filtering"""
    # Only admin and support users can access logs
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPPORT]:
        return jsonify({"error": "Unauthorized access"}), 403
        
    try:
        # Validate and parse query parameters
        schema = LogFilterSchema()
        params = schema.load(request.args)
        
        # Convert string values to enum if provided
        level = LogLevel(params['level']) if params['level'] else None
        category = LogCategory(params['category']) if params['category'] else None
        
        # Get logs
        logs = LogService.get_logs(
            level=level,
            category=category,
            user_id=params['user_id'],
            start_date=params['start_date'],
            end_date=params['end_date'],
            limit=params['limit'],
            skip=params['skip']
        )
        
        # Convert to dict for JSON response
        logs_data = []
        for log in logs:
            log_dict = {
                "id": str(log.id),
                "level": log.level,
                "category": log.category,
                "message": log.message,
                "details": log.details,
                "source": log.source,
                "created_at": log.created_at.isoformat()
            }
            
            if log.user:
                log_dict["user_id"] = str(log.user.id)
                
            logs_data.append(log_dict)
        
        return jsonify({
            "success": True,
            "data": logs_data,
            "count": len(logs_data),
            "total": Log.objects.count()
        }), 200
        
    except ValidationError as e:
        return jsonify({"error": "Invalid request data", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Error retrieving logs: {str(e)}")
        return jsonify({"error": "Failed to retrieve logs"}), 500