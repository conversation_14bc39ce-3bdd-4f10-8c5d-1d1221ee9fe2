"""
Unit tests for the File Upload Helper system
"""

import pytest
import os
import tempfile
import shutil
from io import Bytes<PERSON>
from unittest.mock import Mock, patch, MagicMock
from werkzeug.datastructures import FileStorage

from app.utils.file_upload_helper import (
    FileUploadHelper, FileType, StorageBackend, ProcessingStatus,
    FileMetadata, UploadResult
)
from app.utils.upload_config import UploadConfig
from app.utils.storage_backends import LocalStorageBackend


class TestFileUploadHelper:
    """Test cases for FileUploadHelper"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def upload_config(self, temp_dir):
        """Create test upload configuration"""
        return {
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'allowed_extensions': {'.jpg', '.png', '.gif', '.mp4', '.pdf', '.txt'},
            'storage_backend': StorageBackend.LOCAL,
            'upload_folder': temp_dir,
            'create_thumbnails': True,
            'thumbnail_sizes': [(150, 150), (300, 300)],
            'image_quality': 85,
            'scan_for_malware': False
        }
    
    @pytest.fixture
    def upload_helper(self, upload_config):
        """Create FileUploadHelper instance"""
        return FileUploadHelper(upload_config)
    
    @pytest.fixture
    def sample_image_file(self):
        """Create sample image file for testing"""
        # Create a simple 1x1 pixel PNG
        png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        
        file_obj = BytesIO(png_data)
        return FileStorage(
            stream=file_obj,
            filename='test_image.png',
            content_type='image/png'
        )
    
    @pytest.fixture
    def sample_text_file(self):
        """Create sample text file for testing"""
        content = b"This is a test document content."
        file_obj = BytesIO(content)
        return FileStorage(
            stream=file_obj,
            filename='test_document.txt',
            content_type='text/plain'
        )
    
    def test_initialization(self, upload_config):
        """Test FileUploadHelper initialization"""
        helper = FileUploadHelper(upload_config)
        
        assert helper.config['max_file_size'] == 10 * 1024 * 1024
        assert helper.config['storage_backend'] == StorageBackend.LOCAL
        assert helper.storage_backend is not None
    
    def test_file_type_determination(self, upload_helper):
        """Test file type determination from extension and MIME type"""
        # Test image
        file_type = upload_helper._determine_file_type('.png', 'image/png')
        assert file_type == FileType.IMAGE
        
        # Test video
        file_type = upload_helper._determine_file_type('.mp4', 'video/mp4')
        assert file_type == FileType.VIDEO
        
        # Test document
        file_type = upload_helper._determine_file_type('.pdf', 'application/pdf')
        assert file_type == FileType.DOCUMENT
        
        # Test unknown
        file_type = upload_helper._determine_file_type('.xyz', 'application/octet-stream')
        assert file_type == FileType.DOCUMENT  # Default fallback
    
    def test_mime_type_validation(self, upload_helper):
        """Test MIME type validation"""
        # Valid image MIME type
        assert upload_helper._validate_mime_type('image/png', FileType.IMAGE)
        
        # Invalid image MIME type
        assert not upload_helper._validate_mime_type('video/mp4', FileType.IMAGE)
        
        # Valid video MIME type
        assert upload_helper._validate_mime_type('video/mp4', FileType.VIDEO)
    
    def test_metadata_extraction(self, upload_helper, sample_image_file):
        """Test file metadata extraction"""
        metadata = upload_helper._extract_metadata(sample_image_file)
        
        assert metadata.original_filename == 'test_image.png'
        assert metadata.file_extension == '.png'
        assert metadata.file_type == FileType.IMAGE
        assert metadata.mime_type == 'image/png'
        assert metadata.file_size > 0
        assert metadata.checksum is not None
        assert len(metadata.checksum) == 64  # SHA256 hash length
    
    def test_file_validation_success(self, upload_helper, sample_image_file):
        """Test successful file validation"""
        is_valid, error_msg, metadata = upload_helper.validate_file(sample_image_file)
        
        assert is_valid
        assert error_msg == ""
        assert metadata is not None
        assert metadata.file_type == FileType.IMAGE
    
    def test_file_validation_no_file(self, upload_helper):
        """Test validation with no file"""
        is_valid, error_msg, metadata = upload_helper.validate_file(None)
        
        assert not is_valid
        assert "No file provided" in error_msg
        assert metadata is None
    
    def test_file_validation_empty_filename(self, upload_helper):
        """Test validation with empty filename"""
        empty_file = FileStorage(stream=BytesIO(b"test"), filename="")
        is_valid, error_msg, metadata = upload_helper.validate_file(empty_file)
        
        assert not is_valid
        assert "No file provided" in error_msg
    
    def test_file_validation_size_limit(self, upload_helper):
        """Test file size validation"""
        # Create file larger than limit
        large_content = b"x" * (15 * 1024 * 1024)  # 15MB (larger than 10MB limit)
        large_file = FileStorage(
            stream=BytesIO(large_content),
            filename='large_file.txt',
            content_type='text/plain'
        )
        
        is_valid, error_msg, metadata = upload_helper.validate_file(large_file)
        
        assert not is_valid
        assert "exceeds maximum allowed size" in error_msg
    
    def test_file_validation_extension(self, upload_helper):
        """Test file extension validation"""
        # Create file with disallowed extension
        bad_file = FileStorage(
            stream=BytesIO(b"test content"),
            filename='malicious.exe',
            content_type='application/octet-stream'
        )
        
        is_valid, error_msg, metadata = upload_helper.validate_file(bad_file)
        
        assert not is_valid
        assert "not allowed" in error_msg
    
    def test_storage_path_generation(self, upload_helper):
        """Test storage path generation"""
        path = upload_helper._get_storage_path(
            user_id="user123",
            file_type=FileType.IMAGE,
            folder="profile",
            filename="test.jpg"
        )
        
        # Should contain organized structure
        assert "image" in path
        assert "profile" in path
        assert "test.jpg" in path
        assert "user123"[:8] in path  # User ID prefix
    
    @patch('app.utils.file_upload_helper.current_app')
    def test_upload_file_success(self, mock_app, upload_helper, sample_image_file, temp_dir):
        """Test successful file upload"""
        # Mock Flask app
        mock_app.root_path = temp_dir
        
        result = upload_helper.upload_file(
            file=sample_image_file,
            user_id="user123",
            folder="test",
            process_file=False  # Skip processing for this test
        )
        
        assert result.success
        assert result.file_url is not None
        assert result.file_path is not None
        assert result.metadata.file_type == FileType.IMAGE
        assert result.storage_backend == StorageBackend.LOCAL
    
    def test_upload_file_validation_failure(self, upload_helper):
        """Test upload with validation failure"""
        # Create invalid file
        bad_file = FileStorage(
            stream=BytesIO(b"x" * (15 * 1024 * 1024)),  # Too large
            filename='large.txt',
            content_type='text/plain'
        )
        
        result = upload_helper.upload_file(
            file=bad_file,
            user_id="user123"
        )
        
        assert not result.success
        assert "exceeds maximum allowed size" in result.error_message
    
    def test_batch_upload(self, upload_helper, sample_image_file, sample_text_file):
        """Test batch file upload"""
        files = [sample_image_file, sample_text_file]
        
        with patch('app.utils.file_upload_helper.current_app') as mock_app:
            mock_app.root_path = tempfile.mkdtemp()
            
            results = upload_helper.upload_multiple_files(
                files=files,
                user_id="user123",
                process_files=False
            )
        
        assert len(results) == 2
        # Note: In real scenario, we'd need to handle different file types
    
    def test_file_deletion(self, upload_helper, temp_dir):
        """Test file deletion"""
        # Create a test file
        test_file_path = os.path.join(temp_dir, "test_delete.txt")
        with open(test_file_path, 'w') as f:
            f.write("test content")
        
        # Delete the file
        success = upload_helper.delete_file(test_file_path)
        
        assert success
        assert not os.path.exists(test_file_path)
    
    def test_file_info_retrieval(self, upload_helper, temp_dir):
        """Test file information retrieval"""
        # Create a test file
        test_file_path = os.path.join(temp_dir, "test_info.txt")
        test_content = "test content for info"
        with open(test_file_path, 'w') as f:
            f.write(test_content)
        
        info = upload_helper.get_file_info(test_file_path)
        
        assert info is not None
        assert info['size'] == len(test_content)
        assert info['extension'] == '.txt'
        assert 'created' in info
        assert 'modified' in info
    
    def test_social_media_specs(self, upload_helper):
        """Test social media platform specifications"""
        specs = upload_helper.get_social_media_specs()
        
        assert 'instagram' in specs
        assert 'facebook' in specs
        assert 'twitter' in specs
        assert 'linkedin' in specs
        
        # Check Instagram specs
        instagram_specs = specs['instagram']
        assert 'image' in instagram_specs
        assert 'video' in instagram_specs
        
        # Check specific dimensions
        square_spec = instagram_specs['image']['square']
        assert square_spec['width'] == 1080
        assert square_spec['height'] == 1080
    
    @patch('app.utils.file_upload_helper.PILLOW_AVAILABLE', True)
    @patch('app.utils.file_upload_helper.Image')
    def test_image_processing(self, mock_image, upload_helper):
        """Test image processing functionality"""
        # Mock PIL Image
        mock_img = MagicMock()
        mock_img.size = (1920, 1080)
        mock_img.mode = 'RGB'
        mock_img.copy.return_value = mock_img
        mock_image.open.return_value.__enter__.return_value = mock_img
        
        # Create mock upload result
        result = UploadResult(
            success=True,
            file_url="/test/image.jpg",
            file_path="/tmp/test/image.jpg",
            metadata=Mock(file_type=FileType.IMAGE),
            storage_backend=StorageBackend.LOCAL,
            processing_status=ProcessingStatus.PENDING
        )
        
        # Process the image
        processed_result = upload_helper._process_image(result)
        
        assert processed_result.processing_status == ProcessingStatus.COMPLETED
    
    def test_malware_scanning(self, upload_helper):
        """Test basic malware scanning"""
        # Test with safe file
        safe_file = FileStorage(
            stream=BytesIO(b"safe content"),
            filename='safe.txt',
            content_type='text/plain'
        )
        
        is_safe, message = upload_helper._scan_for_malware(safe_file)
        assert is_safe
        
        # Test with dangerous extension
        dangerous_file = FileStorage(
            stream=BytesIO(b"malicious content"),
            filename='malware.exe',
            content_type='application/octet-stream'
        )
        
        is_safe, message = upload_helper._scan_for_malware(dangerous_file)
        assert not is_safe
        assert "dangerous file type" in message.lower()


class TestStorageBackends:
    """Test cases for storage backends"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def local_backend(self, temp_dir):
        """Create local storage backend"""
        return LocalStorageBackend({
            'base_path': temp_dir,
            'base_url': '/static'
        })
    
    @pytest.fixture
    def sample_file(self):
        """Create sample file for testing"""
        content = b"Test file content"
        return FileStorage(
            stream=BytesIO(content),
            filename='test.txt',
            content_type='text/plain'
        )
    
    def test_local_storage_save(self, local_backend, sample_file, temp_dir):
        """Test local storage file saving"""
        storage_path = "test/file.txt"
        
        file_url, file_path = local_backend.save_file(sample_file, storage_path)
        
        assert file_url == "/static/test/file.txt"
        assert os.path.exists(file_path)
        
        # Verify content
        with open(file_path, 'rb') as f:
            assert f.read() == b"Test file content"
    
    def test_local_storage_delete(self, local_backend, temp_dir):
        """Test local storage file deletion"""
        # Create test file
        test_file = os.path.join(temp_dir, "delete_test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        # Delete file
        success = local_backend.delete_file(test_file)
        
        assert success
        assert not os.path.exists(test_file)
    
    def test_local_storage_file_exists(self, local_backend, temp_dir):
        """Test local storage file existence check"""
        # Create test file
        test_file = os.path.join(temp_dir, "exists_test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        assert local_backend.file_exists(test_file)
        assert not local_backend.file_exists(os.path.join(temp_dir, "nonexistent.txt"))
    
    def test_local_storage_get_url(self, local_backend, temp_dir):
        """Test local storage URL generation"""
        test_file = os.path.join(temp_dir, "url_test.txt")
        url = local_backend.get_file_url(test_file)
        
        assert url.startswith("/static/")
        assert "url_test.txt" in url


class TestUploadIntegration:
    """Integration tests for the upload system"""

    @pytest.fixture
    def app(self):
        """Create Flask app for testing"""
        from app import create_app
        app = create_app('testing')
        return app

    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()

    @pytest.fixture
    def auth_headers(self, client):
        """Create authentication headers for API requests"""
        # This would need to be implemented based on your auth system
        return {'Authorization': 'Bearer test-token'}

    def test_upload_api_single_file(self, client, auth_headers):
        """Test single file upload API endpoint"""
        # Create test file
        data = {
            'file': (BytesIO(b'test image content'), 'test.jpg'),
            'file_type': 'image',
            'process_file': 'true'
        }

        with patch('flask_login.current_user') as mock_user:
            mock_user.id = 'test-user-123'
            mock_user.is_authenticated = True

            response = client.post('/api/upload/single',
                                 data=data,
                                 content_type='multipart/form-data')

        # Note: This test would need proper authentication setup
        # assert response.status_code == 200
        # data = response.get_json()
        # assert data['success'] == True

    def test_upload_api_batch_files(self, client, auth_headers):
        """Test batch file upload API endpoint"""
        # Create test files
        data = {
            'files': [
                (BytesIO(b'test image 1'), 'test1.jpg'),
                (BytesIO(b'test image 2'), 'test2.jpg')
            ],
            'file_type': 'image',
            'process_files': 'true'
        }

        with patch('flask_login.current_user') as mock_user:
            mock_user.id = 'test-user-123'
            mock_user.is_authenticated = True

            response = client.post('/api/upload/batch',
                                 data=data,
                                 content_type='multipart/form-data')

        # Note: This test would need proper authentication setup
        # assert response.status_code == 200

    def test_upload_config_api(self, client, auth_headers):
        """Test upload configuration API endpoint"""
        with patch('flask_login.current_user') as mock_user:
            mock_user.is_authenticated = True

            response = client.get('/api/upload/config')

        # Note: This test would need proper authentication setup
        # assert response.status_code == 200

    def test_file_validation_api(self, client, auth_headers):
        """Test file validation API endpoint"""
        data = {
            'file': (BytesIO(b'test content'), 'test.txt')
        }

        with patch('flask_login.current_user') as mock_user:
            mock_user.is_authenticated = True

            response = client.post('/api/upload/validate',
                                 data=data,
                                 content_type='multipart/form-data')

        # Note: This test would need proper authentication setup
        # assert response.status_code == 200


if __name__ == '__main__':
    pytest.main([__file__])
