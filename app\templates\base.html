<!DOCTYPE html>
<html lang="{{ lang|default('en') }}" {% if lang == 'fa' %}dir="rtl"{% endif %}>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rominext Manager</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    {% if lang == 'fa' %}
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Vazir Font for Persian text -->
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazir-font@v30.1.0/dist/font-face.css" rel="stylesheet">
    {% if lang == 'fa' %}
    <!-- RTL overrides -->
    <link href="{{ url_for('static', filename='css/rtl.css') }}" rel="stylesheet">
    <style>
        body, h1, h2, h3, h4, h5, h6, p, a, button, input, textarea, select, .btn {
            font-family: 'Vazir', Tahoma, Arial, sans-serif !important;
        }
    </style>
    {% endif %}
    {% block head_extra %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand text-success {% if lang == 'fa' %}ms-5{% else %}me-5{% endif %}" href="{{ url_for('dashboard.index') }}">Rominext</a>
            {% if current_user.is_authenticated %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav {% if lang == 'fa' %}ms-auto me-4{% else %}me-auto ms-4{% endif %}">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard.index' %}active{% endif %}" href="{{ url_for('dashboard.index') }}">
                            {% if request.path.startswith('/dashboard') %}
                                {{ t('nav.profile') }}
                            {% else %}
                                {{ t('nav.dashboard') }}
                            {% endif %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('strategy.') %}active{% endif %}" href="{{ url_for('strategy.index') }}">{{ t('nav.content_strategy') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('content.create') }}">{{ t('nav.create_post') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('content.schedule') }}">{{ t('nav.scheduled_posts') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('comments.monitor') }}">{{ t('nav.comments') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">{{ t('nav.settings') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.logout') }}">{{ t('nav.logout') }}</a>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </nav>
    
    <div class="content-wrapper">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
    <script>
        // Convert any remaining btn-primary to btn-success for consistency
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.btn-primary').forEach(button => {
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');
            });
        });
    </script>
</body>
</html>
