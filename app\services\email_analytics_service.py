from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import logging

from ..models.email_template import EmailLog, EmailTemplate
from ..models.user import User

logger = logging.getLogger(__name__)

class EmailAnalyticsService:
    """
    Service for email analytics and tracking
    """
    
    @staticmethod
    def track_email_open(tracking_id: str, user_agent: str = None, ip_address: str = None) -> bool:
        """
        Track email open event
        
        Args:
            tracking_id: Email tracking ID
            user_agent: User agent string
            ip_address: IP address
            
        Returns:
            True if tracked successfully
        """
        try:
            email_log = EmailLog.objects(tracking_id=tracking_id).first()
            if not email_log:
                logger.warning(f"Email log not found for tracking ID: {tracking_id}")
                return False
            
            # Update only if not already opened
            if not email_log.opened_at:
                email_log.opened_at = datetime.utcnow()
                email_log.status = 'opened'
                email_log.user_agent = user_agent
                email_log.ip_address = ip_address
                email_log.save()
                
                logger.info(f"Tracked email open for tracking ID: {tracking_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to track email open for {tracking_id}: {str(e)}")
            return False
    
    @staticmethod
    def track_email_click(tracking_id: str, url: str, user_agent: str = None, ip_address: str = None) -> bool:
        """
        Track email click event
        
        Args:
            tracking_id: Email tracking ID
            url: Clicked URL
            user_agent: User agent string
            ip_address: IP address
            
        Returns:
            True if tracked successfully
        """
        try:
            email_log = EmailLog.objects(tracking_id=tracking_id).first()
            if not email_log:
                logger.warning(f"Email log not found for tracking ID: {tracking_id}")
                return False
            
            # Update click information
            if not email_log.clicked_at:
                email_log.clicked_at = datetime.utcnow()
                email_log.status = 'clicked'
            
            email_log.user_agent = user_agent
            email_log.ip_address = ip_address
            email_log.save()
            
            # TODO: Store click details in separate collection if needed
            
            logger.info(f"Tracked email click for tracking ID: {tracking_id}, URL: {url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to track email click for {tracking_id}: {str(e)}")
            return False
    
    @staticmethod
    def track_email_bounce(tracking_id: str, bounce_reason: str = None) -> bool:
        """
        Track email bounce event
        
        Args:
            tracking_id: Email tracking ID
            bounce_reason: Reason for bounce
            
        Returns:
            True if tracked successfully
        """
        try:
            email_log = EmailLog.objects(tracking_id=tracking_id).first()
            if not email_log:
                logger.warning(f"Email log not found for tracking ID: {tracking_id}")
                return False
            
            email_log.status = 'bounced'
            email_log.error_message = bounce_reason
            email_log.save()
            
            logger.info(f"Tracked email bounce for tracking ID: {tracking_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to track email bounce for {tracking_id}: {str(e)}")
            return False
    
    @staticmethod
    def get_email_stats(
        start_date: datetime = None,
        end_date: datetime = None,
        template_key: str = None,
        user: User = None
    ) -> Dict[str, Any]:
        """
        Get email statistics
        
        Args:
            start_date: Start date for stats
            end_date: End date for stats
            template_key: Filter by template key
            user: Filter by user
            
        Returns:
            Dictionary with email statistics
        """
        try:
            # Build query
            query = {}
            
            if start_date or end_date:
                date_query = {}
                if start_date:
                    date_query['$gte'] = start_date
                if end_date:
                    date_query['$lte'] = end_date
                query['created_at'] = date_query
            
            if template_key:
                query['template_key'] = template_key
            
            if user:
                query['recipient_user'] = user
            
            # Get all email logs
            email_logs = EmailLog.objects(**query)
            
            # Calculate statistics
            total_sent = email_logs.count()
            
            if total_sent == 0:
                return {
                    'total_sent': 0,
                    'delivered': 0,
                    'opened': 0,
                    'clicked': 0,
                    'bounced': 0,
                    'failed': 0,
                    'delivery_rate': 0,
                    'open_rate': 0,
                    'click_rate': 0,
                    'bounce_rate': 0
                }
            
            # Count by status
            status_counts = defaultdict(int)
            for log in email_logs:
                status_counts[log.status] += 1
            
            delivered = status_counts['sent'] + status_counts['delivered'] + status_counts['opened'] + status_counts['clicked']
            opened = status_counts['opened'] + status_counts['clicked']
            clicked = status_counts['clicked']
            bounced = status_counts['bounced']
            failed = status_counts['failed']
            
            # Calculate rates
            delivery_rate = (delivered / total_sent) * 100 if total_sent > 0 else 0
            open_rate = (opened / delivered) * 100 if delivered > 0 else 0
            click_rate = (clicked / delivered) * 100 if delivered > 0 else 0
            bounce_rate = (bounced / total_sent) * 100 if total_sent > 0 else 0
            
            return {
                'total_sent': total_sent,
                'delivered': delivered,
                'opened': opened,
                'clicked': clicked,
                'bounced': bounced,
                'failed': failed,
                'delivery_rate': round(delivery_rate, 2),
                'open_rate': round(open_rate, 2),
                'click_rate': round(click_rate, 2),
                'bounce_rate': round(bounce_rate, 2)
            }
            
        except Exception as e:
            logger.error(f"Failed to get email stats: {str(e)}")
            return {}
    
    @staticmethod
    def get_template_performance(
        start_date: datetime = None,
        end_date: datetime = None
    ) -> List[Dict[str, Any]]:
        """
        Get performance statistics by template
        
        Args:
            start_date: Start date for stats
            end_date: End date for stats
            
        Returns:
            List of template performance dictionaries
        """
        try:
            # Get all templates
            templates = EmailTemplate.objects(is_active=True)
            
            performance_data = []
            
            for template in templates:
                stats = EmailAnalyticsService.get_email_stats(
                    start_date=start_date,
                    end_date=end_date,
                    template_key=template.template_key
                )
                
                performance_data.append({
                    'template_key': template.template_key,
                    'template_name': template.name,
                    'category': template.category,
                    **stats
                })
            
            # Sort by total sent (descending)
            performance_data.sort(key=lambda x: x['total_sent'], reverse=True)
            
            return performance_data
            
        except Exception as e:
            logger.error(f"Failed to get template performance: {str(e)}")
            return []
    
    @staticmethod
    def get_daily_email_stats(
        start_date: datetime = None,
        end_date: datetime = None
    ) -> List[Dict[str, Any]]:
        """
        Get daily email statistics
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            List of daily statistics
        """
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            # MongoDB aggregation pipeline
            pipeline = [
                {
                    '$match': {
                        'created_at': {
                            '$gte': start_date,
                            '$lte': end_date
                        }
                    }
                },
                {
                    '$group': {
                        '_id': {
                            'year': {'$year': '$created_at'},
                            'month': {'$month': '$created_at'},
                            'day': {'$dayOfMonth': '$created_at'}
                        },
                        'total_sent': {'$sum': 1},
                        'delivered': {
                            '$sum': {
                                '$cond': [
                                    {'$in': ['$status', ['sent', 'delivered', 'opened', 'clicked']]},
                                    1,
                                    0
                                ]
                            }
                        },
                        'opened': {
                            '$sum': {
                                '$cond': [
                                    {'$in': ['$status', ['opened', 'clicked']]},
                                    1,
                                    0
                                ]
                            }
                        },
                        'clicked': {
                            '$sum': {
                                '$cond': [
                                    {'$eq': ['$status', 'clicked']},
                                    1,
                                    0
                                ]
                            }
                        },
                        'bounced': {
                            '$sum': {
                                '$cond': [
                                    {'$eq': ['$status', 'bounced']},
                                    1,
                                    0
                                ]
                            }
                        },
                        'failed': {
                            '$sum': {
                                '$cond': [
                                    {'$eq': ['$status', 'failed']},
                                    1,
                                    0
                                ]
                            }
                        }
                    }
                },
                {
                    '$sort': {'_id': 1}
                }
            ]
            
            results = list(EmailLog.objects.aggregate(pipeline))
            
            # Format results
            daily_stats = []
            for result in results:
                date_info = result['_id']
                date = datetime(date_info['year'], date_info['month'], date_info['day'])
                
                total_sent = result['total_sent']
                delivered = result['delivered']
                
                daily_stats.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'total_sent': total_sent,
                    'delivered': delivered,
                    'opened': result['opened'],
                    'clicked': result['clicked'],
                    'bounced': result['bounced'],
                    'failed': result['failed'],
                    'delivery_rate': round((delivered / total_sent) * 100, 2) if total_sent > 0 else 0,
                    'open_rate': round((result['opened'] / delivered) * 100, 2) if delivered > 0 else 0,
                    'click_rate': round((result['clicked'] / delivered) * 100, 2) if delivered > 0 else 0
                })
            
            return daily_stats
            
        except Exception as e:
            logger.error(f"Failed to get daily email stats: {str(e)}")
            return []
    
    @staticmethod
    def get_user_email_history(user: User, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get email history for a specific user
        
        Args:
            user: User object
            limit: Maximum number of emails to return
            
        Returns:
            List of email history dictionaries
        """
        try:
            email_logs = EmailLog.objects(
                recipient_user=user
            ).order_by('-created_at').limit(limit)
            
            history = []
            for log in email_logs:
                history.append({
                    'id': str(log.id),
                    'template_key': log.template_key,
                    'subject': log.subject,
                    'status': log.status,
                    'language': log.language,
                    'created_at': log.created_at.isoformat() if log.created_at else None,
                    'sent_at': log.sent_at.isoformat() if log.sent_at else None,
                    'opened_at': log.opened_at.isoformat() if log.opened_at else None,
                    'clicked_at': log.clicked_at.isoformat() if log.clicked_at else None
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Failed to get user email history: {str(e)}")
            return []
