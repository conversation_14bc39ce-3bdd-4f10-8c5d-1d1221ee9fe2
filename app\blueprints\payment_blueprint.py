from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
import logging
from ..services.payment_service import PaymentService
from ..models.payment import PaymentStatus

logger = logging.getLogger(__name__)
payment_bp = Blueprint('payment', __name__, url_prefix='/api/payments')

# Validation schemas
class CreatePaymentSchema(Schema):
    subscription_id = fields.String(required=True)
    amount = fields.Float(required=True)
    currency = fields.String(missing="USD")
    transaction_reference = fields.String(missing=None)

class UpdatePaymentStatusSchema(Schema):
    status = fields.String(required=True)
    transaction_reference = fields.String(missing=None)

@payment_bp.route('/', methods=['POST'])
@login_required
def create_payment():
    """Create a new payment for the current user"""
    try:
        # Validate request data
        schema = CreatePaymentSchema()
        data = schema.load(request.json)
        
        # Create payment
        payment = PaymentService.create_payment(
            user_id=str(current_user.id),
            subscription_id=data['subscription_id'],
            amount=data['amount'],
            currency=data['currency'],
            transaction_reference=data['transaction_reference']
        )
        
        if not payment:
            return jsonify({"success": False, "error": "Failed to create payment"}), 500
            
        return jsonify({
            "success": True,
            "data": payment.to_mongo()
        }), 201
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error creating payment: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500

@payment_bp.route('/<payment_id>', methods=['GET'])
@login_required
def get_payment(payment_id):
    """Get payment details"""
    try:
        payment = PaymentService.get_payment(payment_id)
        
        if not payment:
            return jsonify({"success": False, "error": "Payment not found"}), 404
            
        # Check if payment belongs to current user
        if str(payment.user.id) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized access"}), 403
            
        return jsonify({
            "success": True,
            "data": payment.to_mongo()
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving payment: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500

@payment_bp.route('/<payment_id>/status', methods=['PUT'])
@login_required
def update_payment_status(payment_id):
    """Update payment status"""
    try:
        # Validate request data
        schema = UpdatePaymentStatusSchema()
        data = schema.load(request.json)
        
        # Get payment
        payment = PaymentService.get_payment(payment_id)
        if not payment:
            return jsonify({"success": False, "error": "Payment not found"}), 404
            
        # Check if payment belongs to current user
        if str(payment.user.id) != str(current_user.id):
            return jsonify({"success": False, "error": "Unauthorized access"}), 403
        
        # Validate status
        try:
            status = PaymentStatus(data['status'])
        except ValueError:
            return jsonify({"success": False, "error": "Invalid payment status"}), 400
        
        # Update payment status
        updated_payment = PaymentService.update_payment_status(
            payment_id=payment_id,
            status=status,
            transaction_reference=data['transaction_reference']
        )
        
        if not updated_payment:
            return jsonify({"success": False, "error": "Failed to update payment status"}), 500
            
        return jsonify({
            "success": True,
            "data": updated_payment.to_mongo()
        }), 200
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error updating payment status: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500

@payment_bp.route('/user/<user_id>', methods=['GET'])
@login_required
def get_user_payments(user_id):
    """Get all payments for a user"""
    try:
        # Check if requesting own payments or admin access
        if str(current_user.id) != user_id:
            return jsonify({"success": False, "error": "Unauthorized access"}), 403
            
        payments = PaymentService.get_user_payments(user_id)
        
        return jsonify({
            "success": True,
            "data": [payment.to_mongo() for payment in payments]
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving user payments: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500