from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from ..models import (
    Post, Analytics, Comment, CommentAnalytics, 
    ContentExperiment, UserSetting, User, SentimentType
)

logger = logging.getLogger(__name__)

class InsightService:
    """
    Service for providing content analytics, sentiment analysis, 
    A/B testing management, and personalization learning.
    """
    
    def get_post_performance(self, post_id: str) -> Dict[str, Any]:
        """
        Fetch performance metrics for a specific post.
        
        Args:
            post_id: The ID of the post to analyze
            
        Returns:
            Dictionary containing performance metrics
            
        Raises:
            ValueError: If post not found
        """
        post = Post.objects(id=post_id).first()
        if not post:
            raise ValueError(f"Post with ID {post_id} not found")
            
        analytics = Analytics.objects(post=post).order_by('-collected_at').first()
        if not analytics:
            return {
                "post_id": str(post.id),
                "views_count": 0,
                "likes_count": 0,
                "comments_count": 0,
                "shares_count": 0,
                "engagement_rate": 0.0,
                "collected_at": datetime.utcnow()
            }
            
        return {
            "post_id": str(post.id),
            "views_count": analytics.views_count,
            "likes_count": analytics.likes_count,
            "comments_count": analytics.comments_count,
            "shares_count": analytics.shares_count,
            "engagement_rate": analytics.engagement_rate or 0.0,
            "collected_at": analytics.collected_at
        }
    
    def analyze_comment_sentiment(self, post_id: str) -> List[Dict[str, Any]]:
        """
        Return sentiment analysis of comments for a specific post.
        
        Args:
            post_id: The ID of the post to analyze comments for
            
        Returns:
            List of dictionaries containing comment sentiment data
            
        Raises:
            ValueError: If post not found
        """
        post = Post.objects(id=post_id).first()
        if not post:
            raise ValueError(f"Post with ID {post_id} not found")
            
        # Get all comment analytics for this post
        comment_analytics = CommentAnalytics.objects(post=post)
        
        # Prepare the result
        result = []
        for ca in comment_analytics:
            result.append({
                "comment_id": str(ca.comment.id),
                "comment_text": ca.comment.message if hasattr(ca.comment, 'message') else "",
                "sentiment": ca.sentiment,
                "sentiment_score": ca.sentiment_score,
                "created_at": ca.created_at
            })
            
        # Also calculate summary statistics
        sentiment_counts = {
            "positive": sum(1 for ca in comment_analytics if ca.sentiment == SentimentType.POSITIVE),
            "neutral": sum(1 for ca in comment_analytics if ca.sentiment == SentimentType.NEUTRAL),
            "negative": sum(1 for ca in comment_analytics if ca.sentiment == SentimentType.NEGATIVE)
        }
        
        return {
            "comments": result,
            "summary": {
                "total_comments": len(result),
                "sentiment_distribution": sentiment_counts
            }
        }
    
    def create_ab_test(
        self, 
        user_id: str, 
        post_a_id: str, 
        post_b_id: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> str:
        """
        Create a new A/B test experiment.
        
        Args:
            user_id: The ID of the user creating the test
            post_a_id: The ID of the first post to test
            post_b_id: The ID of the second post to test
            start_date: When the test should start
            end_date: When the test should end
            
        Returns:
            ID of the created experiment
            
        Raises:
            ValueError: If posts not found or dates invalid
        """
        # Validate inputs
        if start_date >= end_date:
            raise ValueError("End date must be after start date")
            
        user = User.objects(id=user_id).first()
        if not user:
            raise ValueError(f"User with ID {user_id} not found")
            
        post_a = Post.objects(id=post_a_id).first()
        if not post_a:
            raise ValueError(f"Post A with ID {post_a_id} not found")
            
        post_b = Post.objects(id=post_b_id).first()
        if not post_b:
            raise ValueError(f"Post B with ID {post_b_id} not found")
        
        # Create the experiment
        experiment = ContentExperiment(
            user=user,
            post_a=post_a,
            post_b=post_b,
            start_date=start_date,
            end_date=end_date,
            analytics_data={}
        )
        experiment.save()
        
        return str(experiment.id)
    
    def get_ab_test_result(self, experiment_id: str) -> Dict[str, Any]:
        """
        Fetch the result of an A/B test experiment.
        
        Args:
            experiment_id: The ID of the experiment
            
        Returns:
            Dictionary containing test results and analytics
            
        Raises:
            ValueError: If experiment not found
        """
        experiment = ContentExperiment.objects(id=experiment_id).first()
        if not experiment:
            raise ValueError(f"Experiment with ID {experiment_id} not found")
            
        # Get analytics for both posts
        post_a_analytics = Analytics.objects(post=experiment.post_a).order_by('-collected_at').first()
        post_b_analytics = Analytics.objects(post=experiment.post_b).order_by('-collected_at').first()
        
        post_a_data = {
            "post_id": str(experiment.post_a.id),
            "views": post_a_analytics.views_count if post_a_analytics else 0,
            "likes": post_a_analytics.likes_count if post_a_analytics else 0,
            "comments": post_a_analytics.comments_count if post_a_analytics else 0,
            "shares": post_a_analytics.shares_count if post_a_analytics else 0,
            "engagement_rate": post_a_analytics.engagement_rate if post_a_analytics and post_a_analytics.engagement_rate else 0.0
        }
        
        post_b_data = {
            "post_id": str(experiment.post_b.id),
            "views": post_b_analytics.views_count if post_b_analytics else 0,
            "likes": post_b_analytics.likes_count if post_b_analytics else 0,
            "comments": post_b_analytics.comments_count if post_b_analytics else 0,
            "shares": post_b_analytics.shares_count if post_b_analytics else 0,
            "engagement_rate": post_b_analytics.engagement_rate if post_b_analytics and post_b_analytics.engagement_rate else 0.0
        }
        
        # Determine winner if experiment has ended and has a winner
        winner = None
        if experiment.winner_post:
            winner = str(experiment.winner_post.id)
        elif datetime.utcnow() > experiment.end_date:
            # If experiment has ended but no winner set, calculate it
            if post_a_data["engagement_rate"] > post_b_data["engagement_rate"]:
                winner = str(experiment.post_a.id)
                experiment.winner_post = experiment.post_a
            elif post_b_data["engagement_rate"] > post_a_data["engagement_rate"]:
                winner = str(experiment.post_b.id)
                experiment.winner_post = experiment.post_b
            
            # Save the winner
            if winner:
                experiment.save()
        
        return {
            "experiment_id": str(experiment.id),
            "status": "completed" if datetime.utcnow() > experiment.end_date else "in_progress",
            "start_date": experiment.start_date,
            "end_date": experiment.end_date,
            "post_a": post_a_data,
            "post_b": post_b_data,
            "winner": winner,
            "analytics_data": experiment.analytics_data
        }
    
    def learn_audience_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        Analyze user's post performance and update personalization profile.
        
        Args:
            user_id: The ID of the user to analyze
            
        Returns:
            Dictionary containing updated personalization profile
            
        Raises:
            ValueError: If user not found
        """
        user = User.objects(id=user_id).first()
        if not user:
            raise ValueError(f"User with ID {user_id} not found")
            
        # Get user settings or create if not exists
        user_setting = UserSetting.objects(user=user).first()
        if not user_setting:
            user_setting = UserSetting(user=user)
            user_setting.save()
            
        # Get all posts by this user
        posts = Post.objects(user=user)
        if not posts:
            return {"personalization_profile": user_setting.personalization_profile or {}}
            
        # Get analytics for all posts
        post_analytics = []
        for post in posts:
            analytics = Analytics.objects(post=post).order_by('-collected_at').first()
            if analytics:
                post_analytics.append({
                    "post": post,
                    "analytics": analytics
                })
        
        if not post_analytics:
            return {"personalization_profile": user_setting.personalization_profile or {}}
            
        # Analyze best performing content
        # Sort by engagement rate
        post_analytics.sort(key=lambda x: x["analytics"].engagement_rate or 0, reverse=True)
        
        # Extract patterns from top performing posts
        top_posts = post_analytics[:min(5, len(post_analytics))]
        
        # Initialize personalization profile if it doesn't exist
        if not user_setting.personalization_profile:
            user_setting.personalization_profile = {}
            
        # Update personalization profile with learned preferences
        personalization_profile = {
            "best_performing_content_types": self._extract_content_types(top_posts),
            "optimal_posting_times": self._extract_posting_times(top_posts),
            "engagement_patterns": self._extract_engagement_patterns(post_analytics),
            "last_updated": datetime.utcnow()
        }
        
        # Update user settings
        user_setting.personalization_profile.update(personalization_profile)
        user_setting.save()
        
        return {"personalization_profile": user_setting.personalization_profile}
    
    def _extract_content_types(self, top_posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract content types from top performing posts"""
        content_types = {}
        for item in top_posts:
            post = item["post"]
            content_type = post.content_type if hasattr(post, 'content_type') else "text"
            content_types[content_type] = content_types.get(content_type, 0) + 1
            
        return content_types
    
    def _extract_posting_times(self, top_posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract optimal posting times from top performing posts"""
        hours = {}
        days = {}
        
        for item in top_posts:
            post = item["post"]
            created_time = post.created_at
            hour = created_time.hour
            day = created_time.strftime('%A')  # Day name
            
            hours[hour] = hours.get(hour, 0) + 1
            days[day] = days.get(day, 0) + 1
            
        return {
            "hours": hours,
            "days": days
        }
    
    def _extract_engagement_patterns(self, post_analytics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract engagement patterns from all posts"""
        total_posts = len(post_analytics)
        if total_posts == 0:
            return {}
            
        total_views = sum(item["analytics"].views_count for item in post_analytics)
        total_likes = sum(item["analytics"].likes_count for item in post_analytics)
        total_comments = sum(item["analytics"].comments_count for item in post_analytics)
        total_shares = sum(item["analytics"].shares_count for item in post_analytics)
        
        return {
            "avg_views_per_post": total_views / total_posts,
            "avg_likes_per_post": total_likes / total_posts,
            "avg_comments_per_post": total_comments / total_posts,
            "avg_shares_per_post": total_shares / total_posts,
            "engagement_ratio": {
                "likes_to_views": total_likes / total_views if total_views > 0 else 0,
                "comments_to_views": total_comments / total_views if total_views > 0 else 0,
                "shares_to_views": total_shares / total_views if total_views > 0 else 0
            }
        }