"""
Advanced File Upload Helper for Rominext Social Media Management System

This module provides comprehensive file upload functionality with support for:
- Multiple file types (images, videos, documents, audio)
- File validation and security
- Multiple storage backends (local, cloud)
- Image and video processing
- Social media platform optimization
- Progress tracking and batch uploads
"""

import os
import uuid
import hashlib
import mimetypes
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import logging

# Import Flask dependencies
from flask import current_app
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

# Import storage backends
from .storage_backends import (
    StorageBackendInterface, LocalStorageBackend, S3StorageBackend,
    GCSStorageBackend, AzureStorageBackend
)

# Import processing libraries (will be optional imports)
try:
    from PIL import Image, ImageOps
    PILLOW_AVAILABLE = True
except ImportError:
    PILLOW_AVAILABLE = False

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

try:
    import ffmpeg
    FFMPEG_AVAILABLE = True
except ImportError:
    FFMPEG_AVAILABLE = False


class FileType(str, Enum):
    """Supported file types"""
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"
    AUDIO = "audio"
    ARCHIVE = "archive"


class StorageBackend(str, Enum):
    """Supported storage backends"""
    LOCAL = "local"
    AWS_S3 = "aws_s3"
    GOOGLE_CLOUD = "google_cloud"
    AZURE_BLOB = "azure_blob"


class ProcessingStatus(str, Enum):
    """File processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class FileMetadata:
    """File metadata container"""
    original_filename: str
    secure_filename: str
    file_size: int
    mime_type: str
    file_type: FileType
    file_extension: str
    checksum: str
    upload_timestamp: datetime
    dimensions: Optional[Tuple[int, int]] = None
    duration: Optional[float] = None
    additional_metadata: Optional[Dict[str, Any]] = None


@dataclass
class UploadResult:
    """Upload operation result"""
    success: bool
    file_url: str
    file_path: str
    metadata: FileMetadata
    storage_backend: StorageBackend
    processing_status: ProcessingStatus
    error_message: Optional[str] = None
    thumbnail_url: Optional[str] = None
    processed_variants: Optional[Dict[str, str]] = None


class FileUploadHelper:
    """
    Advanced file upload helper with comprehensive functionality
    """
    
    # File type mappings
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}
    VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'}
    DOCUMENT_EXTENSIONS = {'.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.xls', '.xlsx', '.ppt', '.pptx'}
    AUDIO_EXTENSIONS = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'}
    ARCHIVE_EXTENSIONS = {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'}
    
    # MIME type mappings
    IMAGE_MIMES = {
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 
        'image/webp', 'image/tiff', 'image/svg+xml'
    }
    VIDEO_MIMES = {
        'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo',
        'video/x-flv', 'video/webm', 'video/x-matroska'
    }
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the file upload helper
        
        Args:
            config: Configuration dictionary with upload settings
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Default configuration
        self.default_config = {
            'max_file_size': 50 * 1024 * 1024,  # 50MB
            'allowed_extensions': self.IMAGE_EXTENSIONS | self.VIDEO_EXTENSIONS | self.DOCUMENT_EXTENSIONS,
            'storage_backend': StorageBackend.LOCAL,
            'upload_folder': 'uploads',
            'create_thumbnails': True,
            'thumbnail_sizes': [(150, 150), (300, 300), (800, 600)],
            'image_quality': 85,
            'video_compression': True,
            'scan_for_malware': True,
            'generate_variants': True,
            'enable_progress_tracking': True
        }
        
        # Merge with provided config
        self.config = {**self.default_config, **self.config}

        # Initialize storage backend
        self.storage_backend = self._initialize_storage_backend()

    def _initialize_storage_backend(self) -> StorageBackendInterface:
        """Initialize the appropriate storage backend"""
        backend_type = StorageBackend(self.config['storage_backend'])

        if backend_type == StorageBackend.LOCAL:
            return LocalStorageBackend({
                'base_path': self.config.get('upload_folder', 'static/uploads'),
                'base_url': '/static/uploads'
            })
        elif backend_type == StorageBackend.AWS_S3:
            return S3StorageBackend({
                'bucket_name': self.config.get('s3_bucket_name'),
                'region': self.config.get('s3_region', 'us-east-1'),
                'access_key': self.config.get('s3_access_key'),
                'secret_key': self.config.get('s3_secret_key'),
                'cdn_domain': self.config.get('s3_cdn_domain')
            })
        elif backend_type == StorageBackend.GOOGLE_CLOUD:
            return GCSStorageBackend({
                'bucket_name': self.config.get('gcs_bucket_name'),
                'project_id': self.config.get('gcs_project_id'),
                'credentials_path': self.config.get('gcs_credentials_path'),
                'cdn_domain': self.config.get('gcs_cdn_domain')
            })
        elif backend_type == StorageBackend.AZURE_BLOB:
            return AzureStorageBackend({
                'account_name': self.config.get('azure_account_name'),
                'account_key': self.config.get('azure_account_key'),
                'container_name': self.config.get('azure_container_name'),
                'cdn_domain': self.config.get('azure_cdn_domain')
            })
        else:
            raise ValueError(f"Unsupported storage backend: {backend_type}")
        
    def validate_file(self, file: FileStorage) -> Tuple[bool, str, FileMetadata]:
        """
        Comprehensive file validation
        
        Args:
            file: The uploaded file object
            
        Returns:
            Tuple of (is_valid, error_message, metadata)
        """
        if not file or not file.filename:
            return False, "No file provided", None
            
        # Get file metadata
        try:
            metadata = self._extract_metadata(file)
        except Exception as e:
            return False, f"Failed to extract metadata: {str(e)}", None
            
        # Check file size
        if metadata.file_size > self.config['max_file_size']:
            max_size_mb = self.config['max_file_size'] / (1024 * 1024)
            return False, f"File size exceeds maximum allowed size of {max_size_mb}MB", metadata
            
        # Check file extension
        if metadata.file_extension.lower() not in self.config['allowed_extensions']:
            return False, f"File type '{metadata.file_extension}' is not allowed", metadata
            
        # MIME type validation
        if not self._validate_mime_type(metadata.mime_type, metadata.file_type):
            return False, f"Invalid MIME type '{metadata.mime_type}' for file type", metadata
            
        # Additional security checks
        if self.config.get('scan_for_malware', False):
            is_safe, malware_msg = self._scan_for_malware(file)
            if not is_safe:
                return False, malware_msg, metadata
                
        return True, "", metadata
        
    def _extract_metadata(self, file: FileStorage) -> FileMetadata:
        """Extract comprehensive file metadata"""
        original_filename = file.filename
        secure_name = secure_filename(original_filename)
        file_extension = Path(original_filename).suffix.lower()
        
        # Read file content for analysis
        file.seek(0)
        file_content = file.read()
        file.seek(0)  # Reset for future reads
        
        file_size = len(file_content)
        checksum = hashlib.sha256(file_content).hexdigest()
        
        # Determine MIME type
        mime_type = self._get_mime_type(file_content, original_filename)
        
        # Determine file type
        file_type = self._determine_file_type(file_extension, mime_type)
        
        # Extract dimensions for images
        dimensions = None
        duration = None
        
        if file_type == FileType.IMAGE and PILLOW_AVAILABLE:
            try:
                with Image.open(file) as img:
                    dimensions = img.size
                file.seek(0)
            except Exception as e:
                self.logger.warning(f"Could not extract image dimensions: {e}")
                
        elif file_type == FileType.VIDEO and FFMPEG_AVAILABLE:
            try:
                # This would require saving temp file for ffmpeg analysis
                # Implementation would go here
                pass
            except Exception as e:
                self.logger.warning(f"Could not extract video metadata: {e}")
        
        return FileMetadata(
            original_filename=original_filename,
            secure_filename=secure_name,
            file_size=file_size,
            mime_type=mime_type,
            file_type=file_type,
            file_extension=file_extension,
            checksum=checksum,
            upload_timestamp=datetime.utcnow(),
            dimensions=dimensions,
            duration=duration
        )
        
    def _get_mime_type(self, file_content: bytes, filename: str) -> str:
        """Get MIME type using multiple methods"""
        # Try python-magic first (most accurate)
        if MAGIC_AVAILABLE:
            try:
                return magic.from_buffer(file_content, mime=True)
            except Exception:
                pass
                
        # Fallback to mimetypes module
        mime_type, _ = mimetypes.guess_type(filename)
        return mime_type or 'application/octet-stream'
        
    def _determine_file_type(self, extension: str, mime_type: str) -> FileType:
        """Determine file type from extension and MIME type"""
        if extension in self.IMAGE_EXTENSIONS or mime_type in self.IMAGE_MIMES:
            return FileType.IMAGE
        elif extension in self.VIDEO_EXTENSIONS or mime_type in self.VIDEO_MIMES:
            return FileType.VIDEO
        elif extension in self.AUDIO_EXTENSIONS:
            return FileType.AUDIO
        elif extension in self.DOCUMENT_EXTENSIONS:
            return FileType.DOCUMENT
        elif extension in self.ARCHIVE_EXTENSIONS:
            return FileType.ARCHIVE
        else:
            return FileType.DOCUMENT  # Default fallback
            
    def _validate_mime_type(self, mime_type: str, file_type: FileType) -> bool:
        """Validate MIME type matches expected file type"""
        if file_type == FileType.IMAGE:
            return mime_type in self.IMAGE_MIMES
        elif file_type == FileType.VIDEO:
            return mime_type in self.VIDEO_MIMES
        # Add more validations as needed
        return True  # Allow other types for now
        
    def _scan_for_malware(self, file: FileStorage) -> Tuple[bool, str]:
        """Basic malware scanning (placeholder for advanced implementation)"""
        # This is a placeholder - in production, integrate with:
        # - ClamAV
        # - VirusTotal API
        # - Cloud-based scanning services
        
        # Basic checks for now
        dangerous_extensions = {'.exe', '.bat', '.cmd', '.scr', '.pif', '.com'}
        file_ext = Path(file.filename).suffix.lower()
        
        if file_ext in dangerous_extensions:
            return False, "Potentially dangerous file type detected"
            
        return True, ""

    def upload_file(
        self,
        file: FileStorage,
        user_id: str,
        folder: Optional[str] = None,
        custom_filename: Optional[str] = None,
        process_file: bool = True
    ) -> UploadResult:
        """
        Upload a file with comprehensive processing

        Args:
            file: The file to upload
            user_id: ID of the user uploading the file
            folder: Optional subfolder for organization
            custom_filename: Optional custom filename (will be made secure)
            process_file: Whether to process the file (thumbnails, compression, etc.)

        Returns:
            UploadResult object with upload details
        """
        try:
            # Validate file
            is_valid, error_msg, metadata = self.validate_file(file)
            if not is_valid:
                return UploadResult(
                    success=False,
                    file_url="",
                    file_path="",
                    metadata=metadata,
                    storage_backend=StorageBackend(self.config['storage_backend']),
                    processing_status=ProcessingStatus.FAILED,
                    error_message=error_msg
                )

            # Generate unique filename
            if custom_filename:
                base_name = secure_filename(custom_filename)
                file_extension = metadata.file_extension
            else:
                base_name = Path(metadata.secure_filename).stem
                file_extension = metadata.file_extension

            unique_filename = f"{uuid.uuid4()}_{base_name}{file_extension}"

            # Determine storage path
            storage_path = self._get_storage_path(
                user_id=user_id,
                file_type=metadata.file_type,
                folder=folder,
                filename=unique_filename
            )

            # Save file using storage backend
            file_url, file_path = self.storage_backend.save_file(file, storage_path)

            # Initialize result
            result = UploadResult(
                success=True,
                file_url=file_url,
                file_path=file_path,
                metadata=metadata,
                storage_backend=StorageBackend(self.config['storage_backend']),
                processing_status=ProcessingStatus.PENDING
            )

            # Process file if requested
            if process_file:
                result = self._process_file(result)

            return result

        except Exception as e:
            self.logger.error(f"File upload failed: {str(e)}")
            return UploadResult(
                success=False,
                file_url="",
                file_path="",
                metadata=metadata if 'metadata' in locals() else None,
                storage_backend=StorageBackend(self.config['storage_backend']),
                processing_status=ProcessingStatus.FAILED,
                error_message=f"Upload failed: {str(e)}"
            )

    def _get_storage_path(
        self,
        user_id: str,
        file_type: FileType,
        folder: Optional[str],
        filename: str
    ) -> str:
        """Generate storage path for file"""
        # Create organized folder structure
        date_folder = datetime.now().strftime("%Y/%m")

        path_parts = [
            self.config['upload_folder'],
            file_type.value,
            date_folder,
            user_id[:8]  # Use first 8 chars of user ID for organization
        ]

        if folder:
            path_parts.append(secure_filename(folder))

        path_parts.append(filename)

        return os.path.join(*path_parts)



    def _process_file(self, result: UploadResult) -> UploadResult:
        """Process uploaded file (thumbnails, compression, etc.)"""
        try:
            result.processing_status = ProcessingStatus.PROCESSING

            if result.metadata.file_type == FileType.IMAGE:
                result = self._process_image(result)
            elif result.metadata.file_type == FileType.VIDEO:
                result = self._process_video(result)

            result.processing_status = ProcessingStatus.COMPLETED
            return result

        except Exception as e:
            self.logger.error(f"File processing failed: {str(e)}")
            result.processing_status = ProcessingStatus.FAILED
            result.error_message = f"Processing failed: {str(e)}"
            return result

    def _process_image(self, result: UploadResult) -> UploadResult:
        """Process image file (thumbnails, optimization)"""
        if not PILLOW_AVAILABLE:
            self.logger.warning("Pillow not available, skipping image processing")
            return result

        try:
            # Generate thumbnails
            if self.config.get('create_thumbnails', True):
                thumbnails = self._generate_thumbnails(result.file_path)
                result.processed_variants = thumbnails

                # Set primary thumbnail
                if thumbnails and 'thumbnail_150x150' in thumbnails:
                    result.thumbnail_url = thumbnails['thumbnail_150x150']

            # Optimize image
            if self.config.get('optimize_images', True):
                self._optimize_image(result.file_path)

            return result

        except Exception as e:
            self.logger.error(f"Image processing failed: {str(e)}")
            raise

    def _generate_thumbnails(self, image_path: str) -> Dict[str, str]:
        """Generate thumbnails for image"""
        thumbnails = {}

        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Generate thumbnails for each configured size
                for width, height in self.config.get('thumbnail_sizes', [(150, 150)]):
                    # Create thumbnail
                    thumbnail = img.copy()
                    thumbnail.thumbnail((width, height), Image.Resampling.LANCZOS)

                    # Generate thumbnail filename
                    base_path = Path(image_path)
                    thumb_filename = f"{base_path.stem}_thumb_{width}x{height}{base_path.suffix}"
                    thumb_path = base_path.parent / thumb_filename

                    # Save thumbnail
                    thumbnail.save(
                        thumb_path,
                        quality=self.config.get('image_quality', 85),
                        optimize=True
                    )

                    # Generate URL
                    thumb_url = str(thumb_path).replace(os.sep, '/').replace(
                        str(Path(current_app.root_path) / 'static'), '/static'
                    )

                    thumbnails[f'thumbnail_{width}x{height}'] = thumb_url

        except Exception as e:
            self.logger.error(f"Thumbnail generation failed: {str(e)}")

        return thumbnails

    def _optimize_image(self, image_path: str):
        """Optimize image file size"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Save optimized version
                img.save(
                    image_path,
                    quality=self.config.get('image_quality', 85),
                    optimize=True
                )

        except Exception as e:
            self.logger.error(f"Image optimization failed: {str(e)}")

    def _process_video(self, result: UploadResult) -> UploadResult:
        """Process video file (thumbnails, compression)"""
        if not FFMPEG_AVAILABLE:
            self.logger.warning("FFmpeg not available, skipping video processing")
            return result

        try:
            # Generate video thumbnail
            if self.config.get('create_thumbnails', True):
                thumbnail_url = self._generate_video_thumbnail(result.file_path)
                if thumbnail_url:
                    result.thumbnail_url = thumbnail_url

            # Compress video if needed
            if self.config.get('video_compression', True):
                compressed_path = self._compress_video(result.file_path)
                if compressed_path:
                    result.processed_variants = {'compressed': compressed_path}

            return result

        except Exception as e:
            self.logger.error(f"Video processing failed: {str(e)}")
            raise

    def _generate_video_thumbnail(self, video_path: str) -> Optional[str]:
        """Generate thumbnail from video"""
        try:
            # Generate thumbnail filename
            base_path = Path(video_path)
            thumb_filename = f"{base_path.stem}_thumb.jpg"
            thumb_path = base_path.parent / thumb_filename

            # Extract frame at 1 second
            (
                ffmpeg
                .input(video_path, ss=1)
                .output(str(thumb_path), vframes=1, format='image2', vcodec='mjpeg')
                .overwrite_output()
                .run(quiet=True)
            )

            # Generate URL
            thumb_url = str(thumb_path).replace(os.sep, '/').replace(
                str(Path(current_app.root_path) / 'static'), '/static'
            )

            return thumb_url

        except Exception as e:
            self.logger.error(f"Video thumbnail generation failed: {str(e)}")
            return None

    def _compress_video(self, video_path: str) -> Optional[str]:
        """Compress video file"""
        try:
            # Generate compressed filename
            base_path = Path(video_path)
            compressed_filename = f"{base_path.stem}_compressed{base_path.suffix}"
            compressed_path = base_path.parent / compressed_filename

            # Compress video
            (
                ffmpeg
                .input(video_path)
                .output(
                    str(compressed_path),
                    vcodec='libx264',
                    acodec='aac',
                    crf=23,
                    preset='medium'
                )
                .overwrite_output()
                .run(quiet=True)
            )

            # Generate URL
            compressed_url = str(compressed_path).replace(os.sep, '/').replace(
                str(Path(current_app.root_path) / 'static'), '/static'
            )

            return compressed_url

        except Exception as e:
            self.logger.error(f"Video compression failed: {str(e)}")
            return None

    def upload_multiple_files(
        self,
        files: List[FileStorage],
        user_id: str,
        folder: Optional[str] = None,
        process_files: bool = True
    ) -> List[UploadResult]:
        """
        Upload multiple files in batch

        Args:
            files: List of files to upload
            user_id: ID of the user uploading files
            folder: Optional subfolder for organization
            process_files: Whether to process files

        Returns:
            List of UploadResult objects
        """
        results = []

        for file in files:
            try:
                result = self.upload_file(
                    file=file,
                    user_id=user_id,
                    folder=folder,
                    process_file=process_files
                )
                results.append(result)

            except Exception as e:
                self.logger.error(f"Batch upload failed for file {file.filename}: {str(e)}")
                results.append(UploadResult(
                    success=False,
                    file_url="",
                    file_path="",
                    metadata=None,
                    storage_backend=StorageBackend(self.config['storage_backend']),
                    processing_status=ProcessingStatus.FAILED,
                    error_message=f"Upload failed: {str(e)}"
                ))

        return results

    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from storage

        Args:
            file_path: Path to the file to delete

        Returns:
            True if deletion was successful
        """
        try:
            # Delete main file
            success = self.storage_backend.delete_file(file_path)

            if success:
                # Also remove thumbnails and variants
                self._cleanup_file_variants(file_path)

            return success

        except Exception as e:
            self.logger.error(f"File deletion failed: {str(e)}")
            return False

    def _cleanup_file_variants(self, original_path: str):
        """Clean up thumbnails and processed variants"""
        try:
            base_path = Path(original_path)
            parent_dir = base_path.parent
            base_name = base_path.stem

            # Find and delete related files
            for file_path in parent_dir.glob(f"{base_name}_*"):
                if file_path.is_file():
                    file_path.unlink()

        except Exception as e:
            self.logger.error(f"Cleanup failed: {str(e)}")

    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get information about an uploaded file

        Args:
            file_path: Path to the file

        Returns:
            Dictionary with file information
        """
        try:
            if not os.path.exists(file_path):
                return None

            stat = os.stat(file_path)
            file_info = {
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'extension': Path(file_path).suffix.lower(),
                'mime_type': mimetypes.guess_type(file_path)[0]
            }

            # Add image-specific info
            if PILLOW_AVAILABLE and file_info['extension'] in self.IMAGE_EXTENSIONS:
                try:
                    with Image.open(file_path) as img:
                        file_info['dimensions'] = img.size
                        file_info['format'] = img.format
                        file_info['mode'] = img.mode
                except Exception:
                    pass

            return file_info

        except Exception as e:
            self.logger.error(f"Failed to get file info: {str(e)}")
            return None

    @staticmethod
    def get_social_media_specs() -> Dict[str, Dict[str, Any]]:
        """
        Get recommended specifications for different social media platforms

        Returns:
            Dictionary with platform specifications
        """
        return {
            'instagram': {
                'image': {
                    'square': {'width': 1080, 'height': 1080},
                    'portrait': {'width': 1080, 'height': 1350},
                    'landscape': {'width': 1080, 'height': 566},
                    'story': {'width': 1080, 'height': 1920}
                },
                'video': {
                    'square': {'width': 1080, 'height': 1080, 'duration': 60},
                    'portrait': {'width': 1080, 'height': 1350, 'duration': 60},
                    'story': {'width': 1080, 'height': 1920, 'duration': 15}
                }
            },
            'facebook': {
                'image': {
                    'post': {'width': 1200, 'height': 630},
                    'cover': {'width': 820, 'height': 312},
                    'profile': {'width': 180, 'height': 180}
                },
                'video': {
                    'post': {'width': 1280, 'height': 720, 'duration': 240}
                }
            },
            'twitter': {
                'image': {
                    'post': {'width': 1200, 'height': 675},
                    'header': {'width': 1500, 'height': 500},
                    'profile': {'width': 400, 'height': 400}
                },
                'video': {
                    'post': {'width': 1280, 'height': 720, 'duration': 140}
                }
            },
            'linkedin': {
                'image': {
                    'post': {'width': 1200, 'height': 627},
                    'cover': {'width': 1584, 'height': 396},
                    'profile': {'width': 400, 'height': 400}
                },
                'video': {
                    'post': {'width': 1280, 'height': 720, 'duration': 600}
                }
            }
        }

    def optimize_for_platform(
        self,
        file_path: str,
        platform: str,
        content_type: str = 'post'
    ) -> Optional[str]:
        """
        Optimize image/video for specific social media platform

        Args:
            file_path: Path to the original file
            platform: Target platform (instagram, facebook, twitter, linkedin)
            content_type: Type of content (post, story, cover, etc.)

        Returns:
            Path to optimized file or None if optimization failed
        """
        try:
            specs = self.get_social_media_specs()

            if platform not in specs:
                self.logger.warning(f"Unknown platform: {platform}")
                return None

            platform_specs = specs[platform]
            file_extension = Path(file_path).suffix.lower()

            if file_extension in self.IMAGE_EXTENSIONS and 'image' in platform_specs:
                return self._optimize_image_for_platform(
                    file_path, platform_specs['image'], content_type
                )
            elif file_extension in self.VIDEO_EXTENSIONS and 'video' in platform_specs:
                return self._optimize_video_for_platform(
                    file_path, platform_specs['video'], content_type
                )

            return None

        except Exception as e:
            self.logger.error(f"Platform optimization failed: {str(e)}")
            return None

    def _optimize_image_for_platform(
        self,
        file_path: str,
        image_specs: Dict[str, Dict[str, int]],
        content_type: str
    ) -> Optional[str]:
        """Optimize image for platform specifications"""
        if not PILLOW_AVAILABLE or content_type not in image_specs:
            return None

        try:
            spec = image_specs[content_type]
            target_width = spec['width']
            target_height = spec['height']

            with Image.open(file_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Resize image
                img_resized = ImageOps.fit(
                    img,
                    (target_width, target_height),
                    Image.Resampling.LANCZOS
                )

                # Generate optimized filename
                base_path = Path(file_path)
                optimized_filename = f"{base_path.stem}_optimized_{content_type}{base_path.suffix}"
                optimized_path = base_path.parent / optimized_filename

                # Save optimized image
                img_resized.save(
                    optimized_path,
                    quality=self.config.get('image_quality', 85),
                    optimize=True
                )

                return str(optimized_path)

        except Exception as e:
            self.logger.error(f"Image platform optimization failed: {str(e)}")
            return None

    def _optimize_video_for_platform(
        self,
        file_path: str,
        video_specs: Dict[str, Dict[str, int]],
        content_type: str
    ) -> Optional[str]:
        """Optimize video for platform specifications"""
        if not FFMPEG_AVAILABLE or content_type not in video_specs:
            return None

        # This would implement video optimization using ffmpeg
        # Placeholder for now
        return None
