import os
import sys

# Add the project root directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.models import User, UserRole

def create_admin_user(email, password, name="Admin"):
    app = create_app()
    with app.app_context():
        # Check if user already exists
        existing_user = User.objects(email=email).first()
        if existing_user:
            print(f"User with email {email} already exists.")
            return
            
        # Create admin user
        user = User.create_user(
            email=email,
            password=password,
            name=name,
            is_admin=True,
            is_active=True
        )
        print(f"Admin user {email} created successfully.")

def update_user_role(email, role_name="admin"):
    app = create_app()
    with app.app_context():
        # Find the user
        user = User.objects(email=email).first()
        if not user:
            print(f"User with email {email} not found.")
            return
            
        # Update role
        if role_name.lower() == "admin":
            user.role = UserRole.ADMIN
        elif role_name.lower() == "support":
            user.role = UserRole.SUPPORT
        elif role_name.lower() == "customer":
            user.role = UserRole.CUSTOMER
        else:
            print(f"Invalid role: {role_name}")
            return
            
        user.save()
        print(f"User {email} updated to role {role_name} successfully.")

def list_users():
    app = create_app()
    with app.app_context():
        users = User.objects.all()
        
        print("\n=== USERS IN DATABASE ===")
        print(f"{'ID':<24} {'NAME':<20} {'EMAIL':<30} {'ROLE':<10} {'STATUS':<8}")
        print("-" * 95)
        
        for user in users:
            user_id = str(user.id)
            name = user.name if hasattr(user, 'name') else 'N/A'
            email = user.email
            role = user.role.value if hasattr(user, 'role') else 'N/A'
            status = user.status.value if hasattr(user, 'status') else ('active' if getattr(user, 'is_active', True) else 'inactive')
            
            print(f"{user_id:<24} {name:<20} {email:<30} {role:<10} {status:<8}")
        
        print(f"\nTotal users: {len(users)}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "create-admin":
            if len(sys.argv) < 4:
                print("Usage: python scripts/user_script.py create-admin <email> <password> [name]")
                sys.exit(1)
            
            email = sys.argv[2]
            password = sys.argv[3]
            name = sys.argv[4] if len(sys.argv) > 4 else "Admin"
            
            create_admin_user(email, password, name)
        elif sys.argv[1] == "update-role":
            if len(sys.argv) < 4:
                print("Usage: python scripts/user_script.py update-role <email> <role>")
                sys.exit(1)
            
            email = sys.argv[2]
            role = sys.argv[3]
            
            update_user_role(email, role)
        elif sys.argv[1] == "list-users":
            list_users()
        else:
            print(f"Unknown command: {sys.argv[1]}")
    else:
        print("Usage: python scripts/user_script.py <command> [args]")
        print("Available commands:")
        print("  create-admin <email> <password> [name]")
        print("  update-role <email> <role>")
        print("  list-users")
