



Scripts

create admin user
$ python scripts/user_script.py create-admin <EMAIL> abc@123 "<PERSON>hs<PERSON>"

make a user as admin
python scripts/user_script.py update-role <EMAIL> admin

list users
python scripts/user_script.py list-users




posts

python scripts/post_script.py create-samples --count 10

python scripts/post_script.py create --title "Test Post" --content "This is a test post content" --platform instagram --status published
python scripts/post_script.py delete <post_id>

python scripts/post_script.py publish <post_id>

python scripts/post_script.py list