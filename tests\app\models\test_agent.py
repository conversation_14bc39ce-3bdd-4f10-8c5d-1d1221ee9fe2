import pytest
from app.models import Agent, User, AgentType

class TestAgentModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    def test_create_agent(self, user):
        """Test creating an agent"""
        agent = Agent(
            user=user,
            name="Content Creator Bot",
            agent_type=AgentType.CONTENT_CREATOR,
            configuration={
                "tone": "professional",
                "topics": ["technology", "AI"]
            }
        )
        agent.save()
        
        assert agent.user == user
        assert agent.name == "Content Creator Bot"
        assert agent.agent_type == AgentType.CONTENT_CREATOR
        assert "tone" in agent.configuration
        assert agent.configuration["topics"] == ["technology", "AI"]
    
    def test_agent_types(self, user):
        """Test different agent types"""
        # Content creator agent
        content_creator = Agent(
            user=user, 
            name="Content Bot", 
            agent_type=AgentType.CONTENT_CREATOR
        )
        content_creator.save()
        
        # Auto poster agent
        auto_poster = Agent(
            user=user, 
            name="Posting Bot", 
            agent_type=AgentType.AUTO_POSTER
        )
        auto_poster.save()
        
        # Comment responder agent
        comment_responder = Agent(
            user=user, 
            name="Comment Bot", 
            agent_type=AgentType.COMMENT_RESPONDER
        )
        comment_responder.save()
        
        # Analytics agent
        analytics_agent = Agent(
            user=user, 
            name="Analytics Bot", 
            agent_type=AgentType.ANALYTICS
        )
        analytics_agent.save()
        
        assert content_creator.agent_type == AgentType.CONTENT_CREATOR
        assert auto_poster.agent_type == AgentType.AUTO_POSTER
        assert comment_responder.agent_type == AgentType.COMMENT_RESPONDER
        assert analytics_agent.agent_type == AgentType.ANALYTICS