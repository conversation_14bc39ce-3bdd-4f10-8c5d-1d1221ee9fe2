# Configuration Guide

This guide covers all configuration options for Rominext, including environment variables, AI providers, social media integrations, and system settings.

## Environment Variables

### Core Application Settings

```bash
# Application Configuration
SECRET_KEY=your-super-secret-key-here-change-in-production
FLASK_ENV=development  # or production
FLASK_DEBUG=1  # 0 for production
DEFAULT_AI_PROVIDER=openai
DEFAULT_SOCIAL_PLATFORM=facebook
```

### Database Configuration

```bash
# MongoDB Settings
MONGODB_URI=mongodb://localhost:27017/rominext
MONGODB_DB=rominext
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_USERNAME=  # Optional for authenticated MongoDB
MONGODB_PASSWORD=  # Optional for authenticated MongoDB
```

### AI Provider Configuration

#### OpenAI
```bash
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1  # Optional, defaults to OpenAI
OPENAI_MODEL=gpt-3.5-turbo  # Default model
```

#### DeepSeek
```bash
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat
```

#### Grok (xAI)
```bash
GROK_API_KEY=your-grok-api-key
GROK_BASE_URL=https://api.x.ai/v1
GROK_MODEL=grok-beta
```

#### HuggingFace
```bash
HUGGINGFACE_API_KEY=your-huggingface-token
HUGGINGFACE_BASE_URL=https://api-inference.huggingface.co
HUGGINGFACE_MODEL=microsoft/DialoGPT-medium
```

#### TogetherAI
```bash
TOGETHER_API_KEY=your-together-api-key
TOGETHER_BASE_URL=https://api.together.xyz/v1
TOGETHER_MODEL=meta-llama/Llama-2-7b-chat-hf
```

#### StabilityAI
```bash
STABILITY_API_KEY=your-stability-api-key
STABILITY_BASE_URL=https://api.stability.ai/v1
STABILITY_MODEL=stable-diffusion-xl-1024-v1-0
```

#### DeepInfra
```bash
DEEPINFRA_API_KEY=your-deepinfra-api-key
DEEPINFRA_BASE_URL=https://api.deepinfra.com/v1
DEEPINFRA_MODEL=meta-llama/Llama-2-7b-chat-hf
```

### Social Media API Configuration

#### Facebook/Meta
```bash
FACEBOOK_PAGE_ACCESS_TOKEN=your-facebook-page-access-token
FACEBOOK_PAGE_ID=your-facebook-page-id
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
```

#### Instagram
```bash
INSTAGRAM_ACCESS_TOKEN=your-instagram-access-token
INSTAGRAM_ACCOUNT_ID=your-instagram-business-account-id
INSTAGRAM_USER_ID=your-instagram-user-id
```

#### Twitter/X
```bash
TWITTER_BEARER_TOKEN=your-twitter-bearer-token
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_ACCESS_TOKEN=your-twitter-access-token
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-access-token-secret
```

#### LinkedIn
```bash
LINKEDIN_ACCESS_TOKEN=your-linkedin-access-token
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
```

### Telegram Bot Configuration

```bash
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook/telegram
TELEGRAM_WEBHOOK_SECRET=your-webhook-secret
```

### Logging Configuration

```bash
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE_PATH=logs/rominext.log
LOG_MAX_BYTES=********  # 10MB
LOG_BACKUP_COUNT=10
```

## AI Provider Configuration File

The system uses a YAML configuration file at `config/ai_providers_config.yaml`:

```yaml
providers:
  openai:
    name: "OpenAI"
    enabled: true
    models:
      - name: "gpt-3.5-turbo"
        type: "chat"
        max_tokens: 4096
        cost_per_1k_tokens: 0.002
      - name: "gpt-4"
        type: "chat"
        max_tokens: 8192
        cost_per_1k_tokens: 0.03
    features:
      - "text_generation"
      - "chat_completion"
      - "content_optimization"

  deepseek:
    name: "DeepSeek"
    enabled: true
    models:
      - name: "deepseek-chat"
        type: "chat"
        max_tokens: 4096
        cost_per_1k_tokens: 0.001
    features:
      - "text_generation"
      - "chat_completion"

  grok:
    name: "Grok AI"
    enabled: true
    models:
      - name: "grok-beta"
        type: "chat"
        max_tokens: 4096
        cost_per_1k_tokens: 0.005
    features:
      - "text_generation"
      - "chat_completion"
      - "real_time_data"

  huggingface:
    name: "HuggingFace"
    enabled: true
    models:
      - name: "microsoft/DialoGPT-medium"
        type: "chat"
        max_tokens: 1024
        cost_per_1k_tokens: 0.0
    features:
      - "text_generation"
      - "open_source"

  togetherai:
    name: "TogetherAI"
    enabled: true
    models:
      - name: "meta-llama/Llama-2-7b-chat-hf"
        type: "chat"
        max_tokens: 4096
        cost_per_1k_tokens: 0.0008
    features:
      - "text_generation"
      - "chat_completion"
      - "open_source"

  stabilityai:
    name: "StabilityAI"
    enabled: true
    models:
      - name: "stable-diffusion-xl-1024-v1-0"
        type: "image"
        max_resolution: "1024x1024"
        cost_per_image: 0.04
    features:
      - "image_generation"
      - "image_editing"

  deepinfra:
    name: "DeepInfra"
    enabled: true
    models:
      - name: "meta-llama/Llama-2-7b-chat-hf"
        type: "chat"
        max_tokens: 4096
        cost_per_1k_tokens: 0.0007
    features:
      - "text_generation"
      - "chat_completion"
      - "serverless"

default_provider: "openai"
fallback_provider: "deepseek"

rate_limits:
  requests_per_minute: 60
  tokens_per_minute: 100000

retry_config:
  max_retries: 3
  backoff_factor: 2
  timeout_seconds: 30
```

## System Settings

### Application Settings

Configure these through the admin panel or directly in the database:

```python
# System settings that can be configured
SYSTEM_SETTINGS = {
    'site_name': 'Rominext',
    'site_description': 'AI-Powered Social Media Management',
    'max_posts_per_user': 1000,
    'max_accounts_per_user': 10,
    'default_timezone': 'UTC',
    'supported_languages': ['en', 'fa', 'es', 'fr'],
    'default_language': 'en',
    'enable_analytics': True,
    'enable_scheduling': True,
    'enable_ai_generation': True,
    'max_file_upload_size': ********,  # 10MB
    'allowed_file_types': ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'mov'],
    'session_timeout': 3600,  # 1 hour
    'password_min_length': 8,
    'enable_two_factor_auth': False,
    'backup_retention_days': 30,
    'log_retention_days': 90
}
```

### User Settings

Default user preferences:

```python
USER_SETTINGS = {
    'timezone': 'UTC',
    'language': 'en',
    'email_notifications': True,
    'push_notifications': True,
    'auto_publish': False,
    'default_ai_provider': 'openai',
    'content_approval_required': True,
    'analytics_dashboard_refresh': 300,  # 5 minutes
    'post_scheduling_buffer': 300,  # 5 minutes
    'max_scheduled_posts': 100
}
```

## Environment-Specific Configuration

### Development Environment

```bash
# .env.development
FLASK_ENV=development
FLASK_DEBUG=1
MONGODB_URI=mongodb://localhost:27017/rominext_dev
LOG_LEVEL=DEBUG
ENABLE_SWAGGER=true
```

### Testing Environment

```bash
# .env.testing
FLASK_ENV=testing
FLASK_DEBUG=0
MONGODB_URI=mongodb://localhost:27017/rominext_test
LOG_LEVEL=WARNING
ENABLE_SWAGGER=false
```

### Production Environment

```bash
# .env.production
FLASK_ENV=production
FLASK_DEBUG=0
MONGODB_URI=mongodb://your-production-mongodb-uri
LOG_LEVEL=INFO
ENABLE_SWAGGER=false
SECRET_KEY=your-very-secure-production-secret-key
```

## Security Configuration

### SSL/TLS Settings

```bash
# SSL Configuration
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/private.key
FORCE_HTTPS=true
SECURE_COOKIES=true
```

### CORS Settings

```bash
# CORS Configuration
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization
```

### Rate Limiting

```bash
# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=1000
RATE_LIMIT_PER_DAY=10000
```

## Monitoring and Health Checks

### Health Check Endpoints

```bash
# Health check configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=60  # seconds
HEALTH_CHECK_TIMEOUT=10   # seconds
```

### Metrics Collection

```bash
# Metrics configuration
METRICS_ENABLED=true
METRICS_ENDPOINT=/metrics
PROMETHEUS_ENABLED=false
```

## Backup Configuration

```bash
# Backup settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups
BACKUP_COMPRESSION=true
```

## Configuration Validation

The application validates configuration on startup. Use the health check command:

```bash
python run.py health-check
```

## Configuration Best Practices

1. **Never commit sensitive data** to version control
2. **Use different configurations** for different environments
3. **Regularly rotate API keys** and secrets
4. **Monitor API usage** and quotas
5. **Set appropriate rate limits** for your use case
6. **Enable logging** for troubleshooting
7. **Use strong secret keys** in production
8. **Configure proper SSL/TLS** for production
9. **Set up monitoring** and alerting
10. **Regular backup** of configuration and data

## Troubleshooting Configuration

### Common Issues

1. **Invalid API Keys**: Check key format and permissions
2. **Database Connection**: Verify MongoDB URI and credentials
3. **Missing Environment Variables**: Use health check to identify missing vars
4. **Rate Limits**: Monitor API usage and adjust limits
5. **SSL Issues**: Verify certificate paths and permissions

### Configuration Testing

```bash
# Test configuration
python -c "
from app.config import get_settings
settings = get_settings()
print('Configuration loaded successfully')
print(f'Database: {settings.MONGODB_DB}')
print(f'Default AI Provider: {settings.DEFAULT_AI_PROVIDER}')
"
```

---

For more detailed configuration examples, see the [Installation Guide](installation.md) and [Deployment Guide](deployment.md).
