from typing import Dict, List, Optional, Any, Union
import logging
from datetime import datetime
from ..models.system_setting import SystemSetting

logger = logging.getLogger(__name__)

class SystemSettingService:
    """Service for managing system-wide settings"""
    
    @staticmethod
    def get_setting(key: str) -> Optional[SystemSetting]:
        """
        Get a system setting by key
        
        Args:
            key: The setting key to retrieve
            
        Returns:
            SystemSetting object if found, None otherwise
        """
        try:
            return SystemSetting.objects(key=key).first()
        except Exception as e:
            logger.error(f"Error retrieving system setting '{key}': {str(e)}")
            return None
    
    @staticmethod
    def get_setting_value(key: str, default: Any = None) -> Any:
        """
        Get a system setting value by key
        
        Args:
            key: The setting key to retrieve
            default: Default value to return if setting not found
            
        Returns:
            The setting value or default if not found
        """
        setting = SystemSettingService.get_setting(key)
        return setting.value if setting else default
    
    @staticmethod
    def set_setting(key: str, value: str, description: Optional[str] = None) -> Optional[SystemSetting]:
        """
        Set a system setting value
        
        Args:
            key: The setting key
            value: The setting value
            description: Optional description of the setting
            
        Returns:
            Updated or created SystemSetting object
        """
        try:
            setting = SystemSetting.objects(key=key).first()
            
            if setting:
                # Update existing setting
                setting.value = value
                if description:
                    setting.description = description
                setting.updated_at = datetime.utcnow()
                setting.save()
                return setting
            else:
                # Create new setting
                setting = SystemSetting(
                    key=key,
                    value=value,
                    description=description
                )
                setting.save()
                return setting
        except Exception as e:
            logger.error(f"Error setting system setting '{key}': {str(e)}")
            return None
    
    @staticmethod
    def delete_setting(key: str) -> bool:
        """
        Delete a system setting
        
        Args:
            key: The setting key to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            setting = SystemSetting.objects(key=key).first()
            if setting:
                setting.delete()
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting system setting '{key}': {str(e)}")
            return False
    
    @staticmethod
    def get_all_settings() -> List[SystemSetting]:
        """
        Get all system settings
        
        Returns:
            List of all SystemSetting objects
        """
        try:
            return list(SystemSetting.objects.all())
        except Exception as e:
            logger.error(f"Error retrieving all system settings: {str(e)}")
            return []
    
    @staticmethod
    def get_settings_by_prefix(prefix: str) -> List[SystemSetting]:
        """
        Get all system settings with keys starting with the given prefix

        Args:
            prefix: The key prefix to filter by

        Returns:
            List of matching SystemSetting objects
        """
        try:
            return list(SystemSetting.objects(key__startswith=prefix))
        except Exception as e:
            logger.error(f"Error retrieving system settings with prefix '{prefix}': {str(e)}")
            return []

    @staticmethod
    def initialize_default_settings() -> int:
        """
        Initialize default system settings if they don't exist

        Returns:
            Number of settings initialized
        """
        default_settings = [
            ('site_name', 'Rominext', 'Site name'),
            ('site_description', 'AI-Powered Social Media Management', 'Site description'),
            ('site_url', 'https://rominext.com', 'Site URL'),
            ('admin_email', '<EMAIL>', 'Administrator email'),
            ('default_language', 'en', 'Default language'),
            ('default_timezone', 'UTC', 'Default timezone'),
            ('maintenance_mode', 'false', 'Maintenance mode'),
            ('registration_enabled', 'true', 'Registration enabled'),

            # Security settings
            ('password_min_length', '8', 'Minimum password length'),
            ('session_timeout', '3600', 'Session timeout in seconds'),
            ('max_login_attempts', '5', 'Maximum login attempts'),
            ('lockout_duration', '900', 'Account lockout duration in seconds'),
            ('enable_two_factor_auth', 'false', 'Enable two-factor authentication'),
            ('require_email_verification', 'true', 'Require email verification'),
            ('password_complexity', 'false', 'Require complex passwords'),
            ('enable_captcha', 'false', 'Enable CAPTCHA'),

            # System settings
            ('max_posts_per_user', '1000', 'Maximum posts per user'),
            ('max_accounts_per_user', '10', 'Maximum accounts per user'),
            ('max_file_upload_size', '********', 'Maximum file upload size in bytes'),
            ('allowed_file_types', 'jpg,jpeg,png,gif,mp4,mov', 'Allowed file types'),
            ('enable_analytics', 'true', 'Enable analytics'),
            ('enable_scheduling', 'true', 'Enable post scheduling'),
            ('enable_ai_generation', 'true', 'Enable AI content generation'),
            ('backup_retention_days', '30', 'Backup retention days'),
            ('log_retention_days', '90', 'Log retention days'),
            ('debug_mode', 'false', 'Debug mode'),

            # Database settings
            ('db_connection_pool_size', '10', 'Database connection pool size'),
            ('db_query_timeout', '30', 'Database query timeout'),
            ('enable_db_logging', 'false', 'Enable database logging'),
            ('auto_backup_enabled', 'false', 'Auto backup enabled'),
            ('backup_frequency', 'daily', 'Backup frequency'),

            # Email settings (from config)
            ('smtp_host', 'mailpanel.bertina.us', 'SMTP host'),
            ('smtp_port', '587', 'SMTP port'),
            ('smtp_username', 'pilardin', 'SMTP username'),
            ('smtp_password', 'vQD437vrc@7F@Y', 'SMTP password'),
            ('smtp_use_tls', 'true', 'Use TLS for SMTP'),
            ('smtp_use_ssl', 'false', 'Use SSL for SMTP'),
            ('email_from_name', 'Rominext', 'Email from name'),
            ('email_from_address', '<EMAIL>', 'Email from address'),
            ('email_reply_to', '', 'Email reply-to address'),
        ]

        initialized_count = 0
        try:
            for key, value, description in default_settings:
                existing = SystemSettingService.get_setting(key)
                if not existing:
                    SystemSettingService.set_setting(key, value, description)
                    initialized_count += 1
                    logger.info(f"Initialized default setting: {key}")

            logger.info(f"Initialized {initialized_count} default settings")
            return initialized_count

        except Exception as e:
            logger.error(f"Error initializing default settings: {str(e)}")
            return 0

    @staticmethod
    def get_settings_dict(keys: List[str] = None) -> Dict[str, str]:
        """
        Get settings as a dictionary

        Args:
            keys: List of specific keys to retrieve. If None, get all settings.

        Returns:
            Dictionary of setting key-value pairs
        """
        try:
            if keys:
                settings = {}
                for key in keys:
                    setting = SystemSettingService.get_setting(key)
                    settings[key] = setting.value if setting else ''
                return settings
            else:
                all_settings = SystemSettingService.get_all_settings()
                return {setting.key: setting.value for setting in all_settings}
        except Exception as e:
            logger.error(f"Error retrieving settings dictionary: {str(e)}")
            return {}