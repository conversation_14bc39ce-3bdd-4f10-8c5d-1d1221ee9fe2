from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BaseSocialIntegration(ABC):
    """
    Abstract Base Class for social media integrations.
    """

    # Common Features - All platforms must implement these:

    @abstractmethod
    def authenticate(self) -> bool:
        """
        Authenticate or validate connection with the social platform.
        Should return True if successful, False otherwise.
        """
        pass

    @abstractmethod
    def post_content(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Publish a new post.
        content_data can include text, media URLs, hashtags, etc.
        Should return post ID or publishing result.
        """
        pass

    @abstractmethod
    def edit_content(self, post_id: str, updated_data: Dict[str, Any]) -> bool:
        """
        Edit an existing post.
        Should return True if update was successful.
        """
        pass

    @abstractmethod
    def delete_content(self, post_id: str) -> bool:
        """
        Delete a published post.
        Should return True if deletion was successful.
        """
        pass

    @abstractmethod
    def fetch_metrics(self, post_id: str) -> Dict[str, Any]:
        """
        Fetch analytics/insights for a specific post (likes, views, shares, etc.)
        """
        pass

    @abstractmethod
    def fetch_profile_info(self) -> Dict[str, Any]:
        """
        Fetch account or page profile details (like username, followers count, bio).
        """
        pass

    @abstractmethod
    def fetch_recent_posts(self, limit: int = 10) -> list:
        """
        Fetch the latest posts from the profile/page.
        Limit defines how many posts to retrieve.
        """
        pass

    @abstractmethod
    def schedule_content(self, content_data: Dict[str, Any], scheduled_time: str) -> Dict[str, Any]:
        """
        Schedule a post to be published at a future time.
        scheduled_time should be in ISO 8601 format.
        """
        pass

    @abstractmethod
    def upload_media(self, media_path: str, media_type: str) -> str:
        """
        Upload a media file (image, video) and return the media ID or URL to be used in posts.
        media_type can be 'image', 'video', 'document', etc.
        """
        pass

    @abstractmethod
    def verify_connection(self) -> bool:
        """
        Ping the platform to check if the token/connection is still valid.
        """
        pass

    # Optional: Provide a default list of supported features
    supported_features: list = []

    def get_supported_features(self) -> list:
        """
        Return a list of features that this integration supports (e.g., reels, stories, polls).
        """
        return self.supported_features
