# Advanced File Upload System Documentation

## Overview

The Rominext Advanced File Upload System provides comprehensive file upload functionality designed specifically for social media content management. It supports multiple file types, storage backends, processing capabilities, and platform optimization.

## Features

### Core Features
- **Multiple File Types**: Images, videos, documents, and audio files
- **Storage Backends**: Local filesystem, AWS S3, Google Cloud Storage, Azure Blob Storage
- **File Processing**: Automatic thumbnail generation, image optimization, video compression
- **Security**: File validation, MIME type checking, malware scanning (configurable)
- **Platform Optimization**: Automatic resizing and optimization for social media platforms
- **Batch Operations**: Upload multiple files simultaneously
- **Progress Tracking**: Real-time upload progress monitoring
- **Drag & Drop UI**: Modern, responsive user interface

### Security Features
- Secure filename generation with UUID
- MIME type validation
- File size limits (configurable per file type)
- Extension whitelist validation
- Optional malware scanning integration
- Checksum generation for file integrity

## Architecture

### Components

1. **FileUploadHelper** (`app/utils/file_upload_helper.py`)
   - Core upload functionality
   - File validation and processing
   - Platform optimization

2. **Storage Backends** (`app/utils/storage_backends.py`)
   - Abstract storage interface
   - Local, S3, GCS, and Azure implementations

3. **Configuration Manager** (`app/utils/upload_config.py`)
   - Environment-based configuration
   - Validation and defaults

4. **Asset Service** (`app/services/asset_service.py`)
   - Database integration
   - Enhanced upload methods

5. **Upload Blueprint** (`app/blueprints/upload_blueprint.py`)
   - REST API endpoints
   - Upload management

6. **UI Components** (`app/static/js/upload-helper.js`, `app/static/css/upload-helper.css`)
   - Drag & drop interface
   - Progress tracking
   - File previews

## Installation

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

Required packages:
- `Pillow` - Image processing
- `python-magic` - MIME type detection
- `ffmpeg-python` - Video processing
- `boto3` - AWS S3 support
- `google-cloud-storage` - Google Cloud Storage
- `azure-storage-blob` - Azure Blob Storage

### 2. Configure Environment Variables

```bash
# Basic Configuration
UPLOAD_MAX_FILE_SIZE=********  # 50MB
UPLOAD_STORAGE_BACKEND=local
UPLOAD_FOLDER=uploads
UPLOAD_CREATE_THUMBNAILS=true
UPLOAD_IMAGE_QUALITY=85

# AWS S3 Configuration
AWS_S3_BUCKET_NAME=your-bucket-name
AWS_S3_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_CDN_DOMAIN=cdn.yourdomain.com

# Google Cloud Storage
GCS_BUCKET_NAME=your-bucket-name
GCS_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials.json

# Azure Blob Storage
AZURE_STORAGE_ACCOUNT_NAME=your-account-name
AZURE_STORAGE_ACCOUNT_KEY=your-account-key
AZURE_STORAGE_CONTAINER_NAME=your-container-name
```

### 3. Initialize Upload Directories

```bash
mkdir -p app/static/uploads/{image,video,document,audio}
```

## Usage

### Basic Upload (Python)

```python
from app.utils.file_upload_helper import FileUploadHelper
from app.utils.upload_config import get_upload_config

# Initialize upload helper
config = get_upload_config()
upload_helper = FileUploadHelper(config.__dict__)

# Upload a file
result = upload_helper.upload_file(
    file=file_object,
    user_id="user123",
    folder="profile_images",
    process_file=True
)

if result.success:
    print(f"File uploaded: {result.file_url}")
    print(f"Thumbnail: {result.thumbnail_url}")
else:
    print(f"Upload failed: {result.error_message}")
```

### Using Asset Service

```python
from app.services.asset_service import AssetService

# Upload single asset
asset = AssetService.upload_asset(
    user_id="user123",
    file=file_object,
    file_type="image",
    metadata={"description": "Profile picture"},
    process_file=True,
    folder="profiles"
)

# Upload multiple assets
assets = AssetService.upload_multiple_assets(
    user_id="user123",
    files=[file1, file2, file3],
    file_type="image",
    process_files=True
)

# Optimize for platform
optimized_url = AssetService.optimize_asset_for_platform(
    asset_id=str(asset.id),
    platform="instagram",
    content_type="post"
)
```

### REST API Usage

#### Upload Single File

```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('file_type', 'image');
formData.append('process_file', 'true');
formData.append('folder', 'gallery');

const response = await fetch('/api/upload/single', {
    method: 'POST',
    body: formData
});

const result = await response.json();
```

#### Upload Multiple Files

```javascript
const formData = new FormData();
for (const file of files) {
    formData.append('files', file);
}
formData.append('file_type', 'image');
formData.append('process_files', 'true');

const response = await fetch('/api/upload/batch', {
    method: 'POST',
    body: formData
});
```

#### Platform Optimization

```javascript
const response = await fetch(`/api/upload/optimize/${assetId}`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        platform: 'instagram',
        content_type: 'story'
    })
});
```

### Frontend Integration

#### HTML Setup

```html
<!-- Include CSS and JS -->
<link rel="stylesheet" href="/static/css/upload-helper.css">
<script src="/static/js/upload-helper.js"></script>

<!-- Upload container -->
<div id="upload-container"></div>
```

#### JavaScript Initialization

```javascript
// Initialize upload helper
const uploadHelper = new UploadHelper({
    container: '#upload-container',
    maxFiles: 10,
    autoUpload: false,
    showPreviews: true
});

// Handle upload completion
document.querySelector('#upload-container').addEventListener('uploadComplete', function(e) {
    const assets = e.detail.assets;
    console.log('Uploaded assets:', assets);
});
```

## Configuration

### File Size Limits

```python
# Default configuration
max_file_size = 50 * 1024 * 1024      # 50MB
max_image_size = 10 * 1024 * 1024     # 10MB
max_video_size = 100 * 1024 * 1024    # 100MB
max_document_size = 25 * 1024 * 1024  # 25MB
```

### Allowed Extensions

```python
# Image files
allowed_image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg']

# Video files
allowed_video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v']

# Document files
allowed_document_extensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.xls', '.xlsx', '.ppt', '.pptx']

# Audio files
allowed_audio_extensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a']
```

### Storage Backend Configuration

#### Local Storage
```python
storage_backend = "local"
upload_folder = "static/uploads"
```

#### AWS S3
```python
storage_backend = "aws_s3"
s3_bucket_name = "your-bucket"
s3_region = "us-east-1"
s3_access_key = "your-access-key"
s3_secret_key = "your-secret-key"
s3_cdn_domain = "cdn.yourdomain.com"  # Optional
```

#### Google Cloud Storage
```python
storage_backend = "google_cloud"
gcs_bucket_name = "your-bucket"
gcs_project_id = "your-project"
gcs_credentials_path = "/path/to/credentials.json"
gcs_cdn_domain = "cdn.yourdomain.com"  # Optional
```

#### Azure Blob Storage
```python
storage_backend = "azure_blob"
azure_account_name = "your-account"
azure_account_key = "your-key"
azure_container_name = "your-container"
azure_cdn_domain = "cdn.yourdomain.com"  # Optional
```

## Social Media Platform Specifications

The system includes built-in optimization for major social media platforms:

### Instagram
- **Square Post**: 1080×1080px
- **Portrait Post**: 1080×1350px
- **Landscape Post**: 1080×566px
- **Story**: 1080×1920px

### Facebook
- **Post**: 1200×630px
- **Cover**: 820×312px
- **Profile**: 180×180px

### Twitter
- **Post**: 1200×675px
- **Header**: 1500×500px
- **Profile**: 400×400px

### LinkedIn
- **Post**: 1200×627px
- **Cover**: 1584×396px
- **Profile**: 400×400px

## API Reference

### Endpoints

#### `POST /api/upload/single`
Upload a single file.

**Parameters:**
- `file` (file): The file to upload
- `file_type` (string): Type of file (image, video, document, audio)
- `folder` (string, optional): Subfolder for organization
- `process_file` (boolean, optional): Whether to process the file (default: true)
- `metadata` (JSON, optional): Additional metadata

#### `POST /api/upload/batch`
Upload multiple files.

**Parameters:**
- `files` (file[]): Array of files to upload
- `file_type` (string): Type of files
- `folder` (string, optional): Subfolder for organization
- `process_files` (boolean, optional): Whether to process files (default: true)
- `metadata` (JSON, optional): Additional metadata for all files

#### `POST /api/upload/optimize/{asset_id}`
Optimize an asset for a specific platform.

**Body:**
```json
{
    "platform": "instagram",
    "content_type": "post"
}
```

#### `GET /api/upload/config`
Get current upload configuration.

#### `POST /api/upload/validate`
Validate a file without uploading.

**Parameters:**
- `file` (file): The file to validate

## Error Handling

The system provides comprehensive error handling:

```python
# Upload result structure
{
    "success": bool,
    "file_url": str,
    "file_path": str,
    "metadata": FileMetadata,
    "storage_backend": StorageBackend,
    "processing_status": ProcessingStatus,
    "error_message": str,  # Only if success=False
    "thumbnail_url": str,  # Optional
    "processed_variants": dict  # Optional
}
```

Common error scenarios:
- File too large
- Invalid file type
- Storage backend errors
- Processing failures
- Network issues

## Performance Considerations

### Optimization Tips

1. **Use appropriate storage backend** for your scale
2. **Configure CDN** for faster file delivery
3. **Enable compression** for images and videos
4. **Use thumbnails** for preview generation
5. **Implement chunked uploads** for large files

### Monitoring

Monitor these metrics:
- Upload success rate
- Processing time
- Storage usage
- Error rates
- User experience metrics

## Security Best Practices

1. **Validate all uploads** with MIME type checking
2. **Use secure filenames** with UUID generation
3. **Implement file size limits** per file type
4. **Scan for malware** in production environments
5. **Use HTTPS** for all upload endpoints
6. **Implement rate limiting** to prevent abuse
7. **Store files outside** web root when possible

## Troubleshooting

### Common Issues

#### Upload Fails with "File too large"
- Check `max_file_size` configuration
- Verify web server limits (nginx/apache)
- Check Flask `MAX_CONTENT_LENGTH`

#### Processing Fails
- Ensure Pillow is installed for image processing
- Verify FFmpeg installation for video processing
- Check file permissions

#### Storage Backend Errors
- Verify credentials and permissions
- Check network connectivity
- Validate bucket/container names

#### UI Not Working
- Ensure CSS and JS files are loaded
- Check browser console for errors
- Verify API endpoints are accessible

### Debug Mode

Enable debug logging:

```python
import logging
logging.getLogger('app.utils.file_upload_helper').setLevel(logging.DEBUG)
```

## Contributing

When contributing to the upload system:

1. **Add tests** for new functionality
2. **Update documentation** for API changes
3. **Follow security guidelines** for file handling
4. **Test with multiple file types** and sizes
5. **Verify cross-browser compatibility** for UI changes

## License

This file upload system is part of the Rominext project and follows the same licensing terms.
