from mongoengine import Document, ReferenceField, StringField, DateTimeField, ListField, BooleanField
from datetime import datetime
from .user import User

class Blog(Document):
    user = ReferenceField(User, required=True)
    title = StringField(required=True)
    content = StringField(required=True)
    slug = StringField(required=True, unique=True)
    tags = ListField(StringField())
    featured_image = StringField()
    is_published = BooleanField(default=False)
    published_at = DateTimeField()
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'blogs',
        'indexes': ['user', 'slug', 'is_published', 'published_at']
    }