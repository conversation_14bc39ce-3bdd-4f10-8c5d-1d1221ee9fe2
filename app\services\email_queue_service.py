import threading
import time
import queue
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

from .email_service import EmailService
from ..models.email_template import EmailLog

logger = logging.getLogger(__name__)

class EmailQueueService:
    """
    Email queue service for handling bulk emails and background processing
    """
    
    def __init__(self):
        self.email_service = EmailService()
        self.email_queue = queue.Queue()
        self.worker_thread = None
        self.is_running = False
        self.max_retries = 3
        self.retry_delay = 300  # 5 minutes
        
    def start_worker(self):
        """Start the email queue worker thread"""
        if self.worker_thread and self.worker_thread.is_alive():
            logger.debug("Email queue worker already running")
            return

        self.is_running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("Email queue worker started")
    
    def stop_worker(self):
        """Stop the email queue worker thread"""
        if not self.is_running:
            logger.debug("Email queue worker already stopped")
            return

        self.is_running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)
            logger.info("Email queue worker stopped")
        else:
            logger.debug("Email queue worker was not running")
    
    def queue_email(
        self,
        template_key: str,
        to_email: str,
        variables: Dict[str, Any] = None,
        language: str = 'en',
        to_name: str = None,
        user_id: str = None,
        priority: int = 1,
        send_at: datetime = None,
        attachments: List[Dict] = None
    ) -> str:
        """
        Queue an email for sending
        
        Args:
            template_key: Email template key
            to_email: Recipient email
            variables: Template variables
            language: Language code
            to_name: Recipient name
            user_id: User ID
            priority: Email priority (1=high, 2=normal, 3=low)
            send_at: Scheduled send time
            attachments: List of attachments
            
        Returns:
            Queue item ID
        """
        email_item = {
            'id': f"email_{int(time.time() * 1000)}",
            'template_key': template_key,
            'to_email': to_email,
            'variables': variables or {},
            'language': language,
            'to_name': to_name,
            'user_id': user_id,
            'priority': priority,
            'send_at': send_at,
            'attachments': attachments,
            'created_at': datetime.utcnow(),
            'retry_count': 0
        }
        
        self.email_queue.put(email_item)
        logger.info(f"Queued email {email_item['id']} for {to_email}")
        return email_item['id']
    
    def queue_bulk_emails(
        self,
        template_key: str,
        recipients: List[Dict[str, Any]],
        variables: Dict[str, Any] = None,
        language: str = 'en',
        priority: int = 2,
        send_at: datetime = None
    ) -> List[str]:
        """
        Queue multiple emails for bulk sending
        
        Args:
            template_key: Email template key
            recipients: List of recipient dictionaries
            variables: Common template variables
            language: Default language
            priority: Email priority
            send_at: Scheduled send time
            
        Returns:
            List of queue item IDs
        """
        queue_ids = []
        
        for recipient in recipients:
            # Merge common variables with recipient-specific variables
            recipient_variables = {**(variables or {}), **recipient.get('variables', {})}
            
            queue_id = self.queue_email(
                template_key=template_key,
                to_email=recipient['email'],
                variables=recipient_variables,
                language=recipient.get('language', language),
                to_name=recipient.get('name'),
                user_id=recipient.get('user_id'),
                priority=priority,
                send_at=send_at,
                attachments=recipient.get('attachments')
            )
            queue_ids.append(queue_id)
        
        logger.info(f"Queued {len(recipients)} bulk emails with template {template_key}")
        return queue_ids
    
    def _worker_loop(self):
        """Main worker loop for processing email queue"""
        while self.is_running:
            try:
                # Get email from queue with timeout
                try:
                    email_item = self.email_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Check if email should be sent now
                if email_item.get('send_at') and email_item['send_at'] > datetime.utcnow():
                    # Put back in queue for later
                    self.email_queue.put(email_item)
                    time.sleep(1)
                    continue
                
                # Process email
                self._process_email(email_item)
                
                # Mark task as done
                self.email_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in email worker loop: {str(e)}")
                time.sleep(1)
    
    def _process_email(self, email_item: Dict[str, Any]):
        """Process a single email item"""
        try:
            # Get user object if user_id provided
            user = None
            if email_item.get('user_id'):
                from ..models.user import User
                user = User.objects(id=email_item['user_id']).first()
            
            # Send email using template
            result = self.email_service.send_template_email(
                template_key=email_item['template_key'],
                to_email=email_item['to_email'],
                variables=email_item['variables'],
                language=email_item['language'],
                to_name=email_item['to_name'],
                user=user,
                attachments=email_item['attachments']
            )
            
            if result['success']:
                logger.info(f"Successfully sent queued email {email_item['id']} to {email_item['to_email']}")
            else:
                # Handle failure
                self._handle_email_failure(email_item, result.get('error', 'Unknown error'))
                
        except Exception as e:
            self._handle_email_failure(email_item, str(e))
    
    def _handle_email_failure(self, email_item: Dict[str, Any], error: str):
        """Handle email sending failure with retry logic"""
        email_item['retry_count'] += 1
        
        if email_item['retry_count'] <= self.max_retries:
            # Schedule retry
            email_item['send_at'] = datetime.utcnow() + timedelta(seconds=self.retry_delay)
            self.email_queue.put(email_item)
            logger.warning(f"Email {email_item['id']} failed, scheduled for retry {email_item['retry_count']}/{self.max_retries}: {error}")
        else:
            # Max retries reached
            logger.error(f"Email {email_item['id']} failed permanently after {self.max_retries} retries: {error}")
    
    def get_queue_size(self) -> int:
        """Get current queue size"""
        return self.email_queue.qsize()
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics"""
        return {
            'queue_size': self.get_queue_size(),
            'worker_running': self.is_running and self.worker_thread and self.worker_thread.is_alive(),
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay
        }
    
    def send_welcome_email(self, user_email: str, user_name: str, language: str = 'en'):
        """Convenience method to send welcome email"""
        return self.queue_email(
            template_key='welcome',
            to_email=user_email,
            variables={'user_name': user_name},
            language=language,
            to_name=user_name,
            priority=1  # High priority for welcome emails
        )
    
    def send_password_reset_email(
        self,
        user_email: str,
        user_name: str,
        reset_link: str,
        language: str = 'en'
    ):
        """Convenience method to send password reset email"""
        return self.queue_email(
            template_key='password_reset',
            to_email=user_email,
            variables={
                'user_name': user_name,
                'reset_link': reset_link
            },
            language=language,
            to_name=user_name,
            priority=1  # High priority for password reset
        )
    
    def send_notification_email(
        self,
        template_key: str,
        user_email: str,
        user_name: str,
        variables: Dict[str, Any] = None,
        language: str = 'en'
    ):
        """Convenience method to send notification emails"""
        return self.queue_email(
            template_key=template_key,
            to_email=user_email,
            variables={**(variables or {}), 'user_name': user_name},
            language=language,
            to_name=user_name,
            priority=2  # Normal priority for notifications
        )
    
    def send_marketing_email(
        self,
        template_key: str,
        recipients: List[Dict[str, Any]],
        variables: Dict[str, Any] = None,
        language: str = 'en',
        send_at: datetime = None
    ):
        """Convenience method to send marketing emails"""
        return self.queue_bulk_emails(
            template_key=template_key,
            recipients=recipients,
            variables=variables,
            language=language,
            priority=3,  # Low priority for marketing
            send_at=send_at
        )


# Global email queue service instance
email_queue_service = EmailQueueService()
