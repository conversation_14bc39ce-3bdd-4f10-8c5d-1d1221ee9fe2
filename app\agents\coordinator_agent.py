"""
Coordinator Agents - High-level agents that orchestrate workflows, plan content,
and ensure brand consistency using the CrewAI framework.
"""
import logging
import asyncio
import json
import yaml
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import random
from pathlib import Path
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from app.services.ai_provider_manager import AIProviderManager
from app.utils.logging_helpers import log_event
from app.models.log import LogLevel, LogCategory
from app.models.post import Post
from app.models.strategy import Strategy

# Configure logging
logger = logging.getLogger(__name__)

# =============== TOOLS ===============

class WorkflowEngineTool(BaseTool):
    """Tool for managing and executing agent workflows"""
    
    name: str = "Workflow Engine"
    description: str = "Manages and executes complex agent workflows with dependencies"
    
    def __init__(self, agent_registry: Dict[str, Agent] = None):
        super().__init__()
        self.agent_registry = agent_registry or {}
        self.workflows = {
            "content_generation": {
                "steps": [
                    {"id": "plan", "agent": "content_planner", "depends_on": []},
                    {"id": "create", "agent": "content_generator", "depends_on": ["plan"]},
                    {"id": "review", "agent": "brand_voice_guard", "depends_on": ["create"]},
                    {"id": "schedule", "agent": "scheduler", "depends_on": ["review"]},
                    {"id": "analyze", "agent": "performance_analyst", "depends_on": ["schedule"]}
                ]
            },
            "trend_analysis": {
                "steps": [
                    {"id": "monitor", "agent": "trend_watcher", "depends_on": []},
                    {"id": "analyze", "agent": "performance_analyst", "depends_on": ["monitor"]},
                    {"id": "recommend", "agent": "content_planner", "depends_on": ["analyze"]}
                ]
            },
            "content_experiment": {
                "steps": [
                    {"id": "design", "agent": "ab_tester", "depends_on": []},
                    {"id": "create_variants", "agent": "content_generator", "depends_on": ["design"]},
                    {"id": "review", "agent": "brand_voice_guard", "depends_on": ["create_variants"]},
                    {"id": "publish", "agent": "scheduler", "depends_on": ["review"]},
                    {"id": "analyze", "agent": "performance_analyst", "depends_on": ["publish"]},
                    {"id": "report", "agent": "ab_tester", "depends_on": ["analyze"]}
                ]
            }
        }
    
    def _run(self, workflow_id: str, input_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a workflow by ID"""
        logger.info(f"Executing workflow: {workflow_id}")
        
        if workflow_id not in self.workflows:
            return {"error": f"Workflow '{workflow_id}' not found"}
        
        workflow = self.workflows[workflow_id]
        results = {}
        input_data = input_data or {}
        
        # Track completed steps
        completed_steps = set()
        
        # Process steps in order of dependencies
        remaining_steps = workflow["steps"].copy()
        
        while remaining_steps:
            # Find steps that can be executed (all dependencies satisfied)
            executable_steps = [
                step for step in remaining_steps
                if all(dep in completed_steps for dep in step["depends_on"])
            ]
            
            if not executable_steps:
                return {"error": "Workflow deadlock detected - circular dependencies"}
            
            # Execute each ready step
            for step in executable_steps:
                step_id = step["id"]
                agent_id = step["agent"]
                
                if agent_id not in self.agent_registry:
                    results[step_id] = {"error": f"Agent '{agent_id}' not registered"}
                    continue
                
                # Prepare input for this step
                step_input = {**input_data}
                for dep in step["depends_on"]:
                    if dep in results:
                        step_input[dep] = results[dep]
                
                # Execute the step using the agent
                agent = self.agent_registry[agent_id]
                logger.info(f"Executing workflow step: {step_id} with agent: {agent_id}")
                
                # Mock execution for now - in real implementation would create and run a Task
                results[step_id] = {"status": "completed", "agent": agent_id, "data": f"Result from {step_id}"}
                
                # Mark step as completed
                completed_steps.add(step_id)
                remaining_steps.remove(step)
        
        log_event(
            level=LogLevel.INFO,
            category=LogCategory.WORKFLOW,
            message=f"Workflow {workflow_id} completed",
            details=f"Executed {len(completed_steps)} steps"
        )
        
        return {
            "workflow_id": workflow_id,
            "status": "completed",
            "steps_executed": list(completed_steps),
            "results": results
        }

class AgentRegistryTool(BaseTool):
    """Tool for managing and querying the agent registry"""
    
    name: str = "Agent Registry"
    description: str = "Manages and provides information about available agents and their capabilities"
    
    def __init__(self):
        super().__init__()
        self.agents = {
            "content_planner": {
                "name": "Content Planner",
                "capabilities": ["editorial_calendar", "content_strategy", "campaign_planning"],
                "input_formats": ["topic_list", "date_range", "platform_goals"],
                "output_formats": ["calendar_json", "content_plan"]
            },
            "content_generator": {
                "name": "Content Generator",
                "capabilities": ["text_generation", "image_generation", "hashtag_generation"],
                "input_formats": ["content_brief", "platform", "tone"],
                "output_formats": ["post_content", "image_url"]
            },
            "brand_voice_guard": {
                "name": "Brand Voice Guard",
                "capabilities": ["tone_analysis", "voice_adjustment", "brand_compliance"],
                "input_formats": ["content_text", "brand_profile"],
                "output_formats": ["adjusted_content", "compliance_report"]
            },
            "scheduler": {
                "name": "Content Scheduler",
                "capabilities": ["optimal_time_analysis", "post_scheduling", "calendar_management"],
                "input_formats": ["content", "platform", "audience"],
                "output_formats": ["schedule_time", "posting_status"]
            },
            "trend_watcher": {
                "name": "Trend Watcher",
                "capabilities": ["trend_monitoring", "hashtag_analysis", "topic_discovery"],
                "input_formats": ["platform", "audience", "region"],
                "output_formats": ["trend_list", "growth_metrics"]
            },
            "performance_analyst": {
                "name": "Performance Analyst",
                "capabilities": ["metrics_analysis", "pattern_recognition", "recommendation_generation"],
                "input_formats": ["post_data", "time_window", "platform"],
                "output_formats": ["performance_report", "optimization_suggestions"]
            },
            "ab_tester": {
                "name": "A/B Tester",
                "capabilities": ["experiment_design", "variant_testing", "statistical_analysis"],
                "input_formats": ["base_content", "variables", "audience"],
                "output_formats": ["test_plan", "results_analysis", "winner_declaration"]
            }
        }
    
    def _run(self, query_type: str, query_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Query the agent registry"""
        logger.info(f"Agent registry query: {query_type}")
        query_params = query_params or {}
        
        if query_type == "list_all":
            return {"agents": list(self.agents.keys())}
        
        elif query_type == "get_agent":
            agent_id = query_params.get("agent_id")
            if not agent_id or agent_id not in self.agents:
                return {"error": f"Agent '{agent_id}' not found"}
            return {"agent": self.agents[agent_id]}
        
        elif query_type == "find_by_capability":
            capability = query_params.get("capability")
            if not capability:
                return {"error": "No capability specified"}
            
            matching_agents = [
                agent_id for agent_id, info in self.agents.items()
                if capability in info["capabilities"]
            ]
            return {"agents": matching_agents}
        
        elif query_type == "get_workflow_agents":
            workflow_id = query_params.get("workflow_id")
            if not workflow_id:
                return {"error": "No workflow_id specified"}
            
            # This would typically query a workflow definition
            # Mock implementation for now
            workflow_agents = {
                "content_generation": ["content_planner", "content_generator", "brand_voice_guard", "scheduler"],
                "trend_analysis": ["trend_watcher", "performance_analyst", "content_planner"],
                "content_experiment": ["ab_tester", "content_generator", "brand_voice_guard", "scheduler", "performance_analyst"]
            }
            
            if workflow_id not in workflow_agents:
                return {"error": f"Workflow '{workflow_id}' not found"}
            
            return {"workflow": workflow_id, "agents": workflow_agents[workflow_id]}
        
        return {"error": f"Unknown query type: {query_type}"}

class CalendarBuilderTool(BaseTool):
    """Tool for building and managing editorial calendars"""
    
    name: str = "Calendar Builder"
    description: str = "Creates and manages editorial calendars for content planning"
    
    def _run(self, 
             start_date: str, 
             end_date: str, 
             platforms: List[str], 
             frequency: Dict[str, int],
             themes: List[str] = None) -> Dict[str, Any]:
        """Build an editorial calendar"""
        logger.info(f"Building editorial calendar from {start_date} to {end_date}")
        
        # Parse dates
        try:
            start = datetime.fromisoformat(start_date)
            end = datetime.fromisoformat(end_date)
        except ValueError:
            return {"error": "Invalid date format. Use ISO format (YYYY-MM-DD)."}
        
        # Validate inputs
        if start > end:
            return {"error": "Start date must be before end date"}
        
        if not platforms:
            return {"error": "At least one platform must be specified"}
        
        themes = themes or ["general", "product", "industry", "educational", "promotional"]
        
        # Calculate total days
        total_days = (end - start).days + 1
        
        # Generate calendar slots
        calendar = []
        current_date = start
        
        while current_date <= end:
            # For each platform, determine if we post today based on frequency
            for platform in platforms:
                # Get posts per week for this platform
                posts_per_week = frequency.get(platform, 3)  # Default to 3 posts per week
                
                # Determine probability of posting today
                post_probability = posts_per_week / 7.0
                
                # Randomly decide if we post today
                if random.random() < post_probability:
                    # Select a random theme
                    theme = random.choice(themes)
                    
                    # Generate a mock content idea
                    content_ideas = {
                        "general": ["Company update", "Industry news", "Team spotlight"],
                        "product": ["Product feature", "How-to guide", "User testimonial"],
                        "industry": ["Industry trend", "Market analysis", "Competitor comparison"],
                        "educational": ["Tutorial", "Tips and tricks", "Best practices"],
                        "promotional": ["Special offer", "New release", "Event announcement"]
                    }
                    
                    content_idea = random.choice(content_ideas.get(theme, content_ideas["general"]))
                    
                    # Add to calendar
                    calendar.append({
                        "date": current_date.isoformat(),
                        "platform": platform,
                        "theme": theme,
                        "content_idea": content_idea,
                        "status": "planned"
                    })
            
            # Move to next day
            current_date += timedelta(days=1)
        
        log_event(
            level=LogLevel.INFO,
            category=LogCategory.PLANNING,
            message=f"Editorial calendar created for {total_days} days",
            details=f"Generated {len(calendar)} content slots across {len(platforms)} platforms"
        )
        
        return {
            "start_date": start_date,
            "end_date": end_date,
            "platforms": platforms,
            "total_days": total_days,
            "total_slots": len(calendar),
            "calendar": calendar
        }

class CampaignInsightTool(BaseTool):
    """Tool for analyzing campaign performance and generating insights"""
    
    name: str = "Campaign Insight Tool"
    description: str = "Analyzes campaign performance and generates insights for future planning"
    
    def _run(self, campaign_id: str = None, time_period: str = "last_30_days") -> Dict[str, Any]:
        """Generate campaign insights"""
        logger.info(f"Generating campaign insights for {campaign_id or 'all campaigns'}")
        
        # Mock implementation - would use real data in production
        campaigns = {
            "summer_sale": {
                "name": "Summer Sale Campaign",
                "platforms": ["instagram", "facebook", "email"],
                "start_date": "2023-06-01",
                "end_date": "2023-06-30",
                "performance": {
                    "reach": 125000,
                    "engagement": 15000,
                    "conversions": 1200,
                    "roi": 3.5
                },
                "top_content": [
                    {"id": "post_123", "platform": "instagram", "engagement_rate": 0.045},
                    {"id": "post_456", "platform": "facebook", "engagement_rate": 0.038}
                ]
            },
            "product_launch": {
                "name": "New Product Launch",
                "platforms": ["instagram", "twitter", "youtube"],
                "start_date": "2023-07-15",
                "end_date": "2023-08-15",
                "performance": {
                    "reach": 250000,
                    "engagement": 35000,
                    "conversions": 2800,
                    "roi": 4.2
                },
                "top_content": [
                    {"id": "post_789", "platform": "youtube", "engagement_rate": 0.062},
                    {"id": "post_101", "platform": "instagram", "engagement_rate": 0.051}
                ]
            }
        }
        
        # If campaign_id is provided, return insights for that campaign
        if campaign_id and campaign_id in campaigns:
            campaign = campaigns[campaign_id]
            insights = self._generate_insights_for_campaign(campaign)
            return {
                "campaign_id": campaign_id,
                "campaign_name": campaign["name"],
                "insights": insights
            }
        
        # Otherwise, return insights for all campaigns
        all_insights = {}
        for c_id, campaign in campaigns.items():
            all_insights[c_id] = {
                "campaign_name": campaign["name"],
                "insights": self._generate_insights_for_campaign(campaign)
            }
        
        return {
            "time_period": time_period,
            "campaigns_analyzed": len(campaigns),
            "campaign_insights": all_insights
        }
    
    def _generate_insights_for_campaign(self, campaign: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate insights for a specific campaign"""
        # This would use AI analysis in production
        # Mock implementation for now
        insights = [
            {
                "type": "platform_performance",
                "finding": f"{campaign['platforms'][0].title()} had the highest engagement rate",
                "recommendation": f"Allocate more resources to {campaign['platforms'][0]} in future campaigns"
            },
            {
                "type": "content_format",
                "finding": "Video content outperformed static images by 35%",
                "recommendation": "Increase video content ratio in upcoming campaigns"
            },
            {
                "type": "timing",
                "finding": "Posts published between 6-8pm had 28% higher engagement",
                "recommendation": "Schedule more content during evening hours"
            },
            {
                "type": "audience",
                "finding": "25-34 age group showed highest conversion rate",
                "recommendation": "Tailor messaging to resonate with millennial audience"
            }
        ]
        
        return insights

class ToneAnalyzerTool(BaseTool):
    """Tool for analyzing the tone of content"""
    
    name: str = "Tone Analyzer"
    description: str = "Analyzes the tone and voice characteristics of content"
    
    def __init__(self, ai_provider_manager: AIProviderManager = None):
        super().__init__()
        self.ai_provider_manager = ai_provider_manager
    
    def _run(self, text: str) -> Dict[str, Any]:
        """Analyze the tone of text"""
        logger.info(f"Analyzing tone of text: {text[:50]}...")
        
        # This would use AI analysis in production
        # Mock implementation for now
        
        # Define tone dimensions
        dimensions = {
            "formality": {"score": 0, "label": ""},  # informal to formal
            "emotion": {"score": 0, "label": ""},    # neutral to emotional
            "confidence": {"score": 0, "label": ""},  # hesitant to confident
            "friendliness": {"score": 0, "label": ""}  # distant to friendly
        }
        
        # Simple keyword-based analysis (would use AI in production)
        formality_markers = {
            "formal": ["therefore", "however", "furthermore", "thus", "consequently", "hereby"],
            "informal": ["hey", "yeah", "cool", "awesome", "btw", "gonna", "wanna"]
        }
        
        emotion_markers = {
            "emotional": ["love", "hate", "excited", "thrilled", "amazing", "terrible", "awesome"],
            "neutral": ["inform", "state", "report", "indicate", "show", "demonstrate"]
        }
        
        confidence_markers = {
            "confident": ["definitely", "certainly", "absolutely",