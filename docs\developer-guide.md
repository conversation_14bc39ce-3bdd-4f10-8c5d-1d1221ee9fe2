# Developer Guide

This guide provides comprehensive information for developers who want to contribute to Rominext, understand the codebase, or extend the platform's functionality.

## Development Environment Setup

### Prerequisites

- Python 3.10+
- MongoDB 5.0+
- Git
- Code editor (VS Code recommended)
- Postman or similar API testing tool

### Local Development Setup

1. **Clone and Setup**
   ```bash
   git clone https://github.com/your-org/rominext.git
   cd rominext
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.development
   # Edit .env.development with your development settings
   ```

3. **Database Setup**
   ```bash
   python run.py init-db
   python run.py create-admin
   ```

4. **Run Development Server**
   ```bash
   python run.py run --debug --auto-kill
   ```

### Development Tools

#### Recommended VS Code Extensions
- Python
- MongoDB for VS Code
- REST Client
- GitLens
- Python Docstring Generator
- Black Formatter

#### Pre-commit Hooks
```bash
pip install pre-commit
pre-commit install
```

## Project Architecture

### Directory Structure

```
rominext/
├── app/                    # Main application package
│   ├── __init__.py        # App factory and configuration
│   ├── config.py          # Configuration management
│   ├── blueprints/        # Flask blueprints (controllers)
│   ├── models/            # Database models (MongoDB/MongoEngine)
│   ├── services/          # Business logic layer
│   ├── integrations/      # External service integrations
│   │   ├── ai/           # AI provider integrations
│   │   └── social/       # Social media connectors
│   ├── templates/         # Jinja2 HTML templates
│   ├── static/           # Static assets (CSS, JS, images)
│   ├── utils/            # Utility functions and helpers
│   └── translations/     # Internationalization files
├── api/                   # Standalone API endpoints
├── bot/                   # Telegram bot implementation
├── config/               # Configuration files
├── docs/                 # Documentation
├── tests/                # Test suite
├── scripts/              # Utility scripts
├── logs/                 # Application logs
├── requirements.txt      # Python dependencies
├── run.py               # Application entry point
└── docker-compose.yml   # Docker configuration
```

### Architecture Patterns

#### Service-Oriented Architecture
- **Controllers (Blueprints)**: Handle HTTP requests and responses
- **Services**: Contain business logic and orchestrate operations
- **Models**: Define data structures and database interactions
- **Integrations**: Handle external API communications

#### Dependency Injection
```python
# Example service with dependency injection
class PostService:
    def __init__(self, ai_provider, social_connector, db_session):
        self.ai_provider = ai_provider
        self.social_connector = social_connector
        self.db_session = db_session
```

#### Provider Pattern
```python
# AI Provider interface
class BaseAIProvider(ABC):
    @abstractmethod
    async def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
        pass

# Concrete implementation
class OpenAIProvider(BaseAIProvider):
    async def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
        # Implementation details
        pass
```

## Code Standards

### Python Style Guide

#### PEP 8 Compliance
- Use 4 spaces for indentation
- Maximum line length: 88 characters (Black formatter)
- Use descriptive variable and function names
- Follow naming conventions:
  - Classes: `PascalCase`
  - Functions/variables: `snake_case`
  - Constants: `UPPER_SNAKE_CASE`

#### Type Hints
```python
from typing import List, Dict, Optional, Union
from datetime import datetime

def create_post(
    content: str,
    user_id: str,
    scheduled_at: Optional[datetime] = None
) -> Dict[str, Any]:
    """Create a new post with the given content."""
    pass
```

#### Docstrings
```python
def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
    """
    Generate content using AI provider.
    
    Args:
        prompt: The input prompt for content generation
        **kwargs: Additional parameters for generation
        
    Returns:
        Dictionary containing generated content and metadata
        
    Raises:
        AIProviderError: If content generation fails
        ValidationError: If prompt is invalid
    """
    pass
```

### Code Organization

#### Import Order
```python
# Standard library imports
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional

# Third-party imports
from flask import Blueprint, request, jsonify
from mongoengine import Document, StringField

# Local imports
from ..models import User, Post
from ..services import PostService
from ..utils import validate_input
```

#### Error Handling
```python
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

def process_request(data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # Process the request
        result = perform_operation(data)
        return {"success": True, "data": result}
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return {"success": False, "error": "Invalid input data"}
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return {"success": False, "error": "Internal server error"}
```

## Database Development

### Model Design

#### Base Model Pattern
```python
from mongoengine import Document, StringField, DateTimeField
from datetime import datetime

class BaseModel(Document):
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'abstract': True,
        'indexes': ['created_at', 'updated_at']
    }
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
```

#### Model Relationships
```python
from mongoengine import ReferenceField, ListField

class Post(BaseModel):
    user = ReferenceField(User, required=True)
    account = ReferenceField(Account, required=True)
    content = StringField(required=True, max_length=5000)
    tags = ListField(StringField(max_length=50))
    
    meta = {
        'collection': 'posts',
        'indexes': [
            'user',
            'account',
            ('user', 'created_at'),
            ('account', 'status')
        ]
    }
```

### Database Migrations

#### Schema Changes
```python
# scripts/migrate_add_field.py
from app.models import Post

def migrate_add_sentiment_field():
    """Add sentiment field to existing posts."""
    posts_updated = 0
    for post in Post.objects(sentiment__exists=False):
        post.sentiment = 'neutral'
        post.save()
        posts_updated += 1
    
    print(f"Updated {posts_updated} posts with sentiment field")

if __name__ == "__main__":
    migrate_add_sentiment_field()
```

## API Development

### Blueprint Structure

```python
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from ..services import PostService
from ..utils import validate_json

posts_bp = Blueprint('posts', __name__, url_prefix='/api/posts')

@posts_bp.route('', methods=['POST'])
@login_required
@validate_json(['content', 'account_id'])
def create_post():
    """Create a new post."""
    data = request.get_json()
    
    try:
        post_service = PostService()
        post = post_service.create_post(
            content=data['content'],
            account_id=data['account_id'],
            user_id=current_user.id
        )
        return jsonify({
            'success': True,
            'data': post.to_dict()
        }), 201
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
```

### API Response Standards

```python
# Success Response
{
    "success": true,
    "data": {
        "id": "post_id",
        "content": "Post content",
        "created_at": "2024-01-01T00:00:00Z"
    },
    "meta": {
        "total": 1,
        "page": 1,
        "per_page": 20
    }
}

# Error Response
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": {
            "field": "content",
            "issue": "Content cannot be empty"
        }
    }
}
```

## Testing

### Test Structure

```
tests/
├── conftest.py           # Test configuration and fixtures
├── test_models/          # Model tests
├── test_services/        # Service layer tests
├── test_api/            # API endpoint tests
├── test_integrations/   # Integration tests
└── test_utils/          # Utility function tests
```

### Writing Tests

#### Unit Tests
```python
import pytest
from app.models import User, Post
from app.services import PostService

class TestPostService:
    def test_create_post_success(self, db_session, sample_user):
        """Test successful post creation."""
        service = PostService(db_session)
        
        post_data = {
            'content': 'Test post content',
            'user_id': sample_user.id
        }
        
        post = service.create_post(**post_data)
        
        assert post.content == 'Test post content'
        assert post.user.id == sample_user.id
        assert post.status == 'draft'
    
    def test_create_post_invalid_user(self, db_session):
        """Test post creation with invalid user."""
        service = PostService(db_session)
        
        with pytest.raises(ValidationError):
            service.create_post(
                content='Test content',
                user_id='invalid_user_id'
            )
```

#### Integration Tests
```python
import pytest
from flask import url_for

class TestPostAPI:
    def test_create_post_authenticated(self, client, auth_headers):
        """Test post creation via API."""
        data = {
            'content': 'Test post via API',
            'account_id': 'test_account_id'
        }
        
        response = client.post(
            url_for('posts.create_post'),
            json=data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        assert response.json['success'] is True
        assert 'data' in response.json
```

### Test Fixtures

```python
# conftest.py
import pytest
from app import create_app
from app.models import User

@pytest.fixture
def app():
    """Create application for testing."""
    app = create_app()
    app.config['TESTING'] = True
    app.config['MONGODB_DB'] = 'rominext_test'
    return app

@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()

@pytest.fixture
def sample_user():
    """Create a sample user for testing."""
    user = User(
        email='<EMAIL>',
        name='Test User'
    )
    user.set_password('testpassword')
    user.save()
    return user
```

## Integration Development

### AI Provider Integration

```python
from abc import ABC, abstractmethod
from typing import Dict, Any
import requests

class BaseAIProvider(ABC):
    def __init__(self, api_key: str, base_url: str):
        self.api_key = api_key
        self.base_url = base_url
    
    @abstractmethod
    async def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate content based on prompt."""
        pass
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request to AI provider."""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            f'{self.base_url}/{endpoint}',
            json=data,
            headers=headers
        )
        
        response.raise_for_status()
        return response.json()

class CustomAIProvider(BaseAIProvider):
    async def generate_content(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Implementation for custom AI provider."""
        data = {
            'prompt': prompt,
            'max_tokens': kwargs.get('max_tokens', 150),
            'temperature': kwargs.get('temperature', 0.7)
        }
        
        result = self._make_request('generate', data)
        
        return {
            'content': result['generated_text'],
            'tokens_used': result['usage']['total_tokens'],
            'model': result['model']
        }
```

### Social Media Connector

```python
from .base_social_connector import BaseSocialConnector
import requests

class CustomSocialConnector(BaseSocialConnector):
    def __init__(self, auth_data: dict):
        self.access_token = auth_data.get('access_token')
        self.api_base_url = 'https://api.customplatform.com/v1'
    
    def authenticate(self) -> bool:
        """Verify authentication with the platform."""
        try:
            response = requests.get(
                f'{self.api_base_url}/me',
                headers={'Authorization': f'Bearer {self.access_token}'}
            )
            return response.status_code == 200
        except:
            return False
    
    def publish_post(self, post_data: dict) -> dict:
        """Publish a post to the platform."""
        data = {
            'text': post_data['content'],
            'media_url': post_data.get('media_url')
        }
        
        response = requests.post(
            f'{self.api_base_url}/posts',
            json=data,
            headers={'Authorization': f'Bearer {self.access_token}'}
        )
        
        response.raise_for_status()
        return response.json()
```

## Contributing Guidelines

### Git Workflow

1. **Fork the Repository**
   ```bash
   git clone https://github.com/your-username/rominext.git
   cd rominext
   git remote add upstream https://github.com/original-org/rominext.git
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Changes**
   - Write code following style guidelines
   - Add tests for new functionality
   - Update documentation as needed

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new AI provider integration"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   # Create pull request on GitHub
   ```

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

### Pull Request Process

1. **Before Submitting**
   - Run tests: `pytest`
   - Check code style: `black . && flake8`
   - Update documentation if needed
   - Add changelog entry

2. **PR Description**
   - Clear description of changes
   - Link to related issues
   - Screenshots for UI changes
   - Testing instructions

3. **Review Process**
   - Address reviewer feedback
   - Keep PR focused and small
   - Maintain clean commit history

## Debugging and Troubleshooting

### Logging

```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def process_data(data):
    logger.info(f"Processing data: {data}")
    try:
        result = complex_operation(data)
        logger.info(f"Operation successful: {result}")
        return result
    except Exception as e:
        logger.error(f"Operation failed: {str(e)}", exc_info=True)
        raise
```

### Performance Monitoring

```python
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.info(f"{func.__name__} took {end_time - start_time:.2f} seconds")
        return result
    return wrapper

@monitor_performance
def expensive_operation():
    # Your code here
    pass
```

### Common Issues

#### Database Connection Issues
```python
from mongoengine import connect, disconnect

def reconnect_database():
    """Reconnect to database if connection is lost."""
    try:
        disconnect()
        connect('rominext', host='mongodb://localhost:27017/')
        logger.info("Database reconnected successfully")
    except Exception as e:
        logger.error(f"Database reconnection failed: {str(e)}")
```

## Deployment for Developers

### Docker Development

```dockerfile
# Dockerfile.dev
FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["python", "run.py", "run", "--debug"]
```

### Environment Management

```bash
# Development
export FLASK_ENV=development
export FLASK_DEBUG=1

# Testing
export FLASK_ENV=testing
export MONGODB_DB=rominext_test

# Production
export FLASK_ENV=production
export FLASK_DEBUG=0
```

---

This developer guide provides the foundation for contributing to Rominext. For specific implementation details, refer to the codebase and existing examples.
