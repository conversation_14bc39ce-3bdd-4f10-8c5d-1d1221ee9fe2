#!/usr/bin/env python3
"""
Rominext Application Runner
"""
import os
import sys
from app import create_app

def main():
    """Run the Rominext application"""
    print("🚀 Starting Rominext Application...")
    print("=" * 50)
    
    # Create Flask app
    app = create_app()
    
    # Print configuration info
    print(f"📧 Email Configuration:")
    print(f"   SMTP Host: {app.config.get('SMTP_HOST', 'Not configured')}")
    print(f"   SMTP Port: {app.config.get('SMTP_PORT', 'Not configured')}")
    print(f"   From Email: {app.config.get('EMAIL_FROM_ADDRESS', 'Not configured')}")
    print(f"   From Name: {app.config.get('EMAIL_FROM_NAME', 'Not configured')}")
    print()
    
    print("🌐 Application URLs:")
    print(f"   Main App: http://localhost:5000")
    print(f"   Admin Panel: http://localhost:5000/admin")
    print(f"   Email Templates: http://localhost:5000/admin/email/templates")
    print(f"   Email Analytics: http://localhost:5000/admin/email/analytics")
    print(f"   Email Test: http://localhost:5000/admin/email/test")
    print()
    
    print("📧 Email System Features:")
    print("   ✅ Multilingual templates (English & Persian)")
    print("   ✅ Background email queue processing")
    print("   ✅ Email tracking and analytics")
    print("   ✅ SMTP configuration with your mail panel")
    print("   ✅ Admin interface for template management")
    print("   ✅ Quick test functionality")
    print()
    
    print("🔧 To test the email system:")
    print("   1. Go to: http://localhost:5000/admin/email/test")
    print("   2. Click 'Quick Test Email' button")
    print("   3. Check <EMAIL> for the test email")
    print()
    
    print("=" * 50)
    print("🎉 Application is ready! Press Ctrl+C to stop.")
    
    # Run the app
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False  # Disable reloader to prevent duplicate workers
        )
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Application error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
