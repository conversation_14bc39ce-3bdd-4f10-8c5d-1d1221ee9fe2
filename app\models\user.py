from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ield, <PERSON><PERSON>anField, En<PERSON><PERSON><PERSON>
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    CUSTOMER = "customer"
    SUPPORT = "support"
    ADMIN = "admin"

class UserStatus(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"

class User(Document, UserMixin):
    email = EmailField(required=True, unique=True)
    password_hash = StringField(required=True)
    name = StringField(required=True)
    role = EnumField(UserRole, default=UserRole.CUSTOMER)
    status = EnumField(UserStatus, default=UserStatus.ACTIVE)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    # Email-related fields
    email_verified = <PERSON>oleanField(default=False)
    email_verification_token = StringField()
    email_verification_expires = DateTimeField()
    password_reset_token = StringField()
    password_reset_expires = DateTimeField()
    preferred_language = StringField(default='en', max_length=5)
    email_notifications_enabled = BooleanField(default=True)
    marketing_emails_enabled = BooleanField(default=True)
    
    meta = {
        'collection': 'users',
        'indexes': ['email'],
        'strict': False  # This allows extra fields in the document
    }

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def get_id(self):
        return str(self.id)

    @property
    def is_authenticated(self):
        return True

    @property
    def is_anonymous(self):
        return False

    @property
    def is_active(self):
        return self.status == UserStatus.ACTIVE

    @staticmethod
    def create_user(email, password, name="", is_admin=False, is_active=True):
        user = User(
            email=email,
            name=name,
            role=UserRole.ADMIN if is_admin else UserRole.CUSTOMER,
            status=UserStatus.ACTIVE if is_active else UserStatus.SUSPENDED
        )
        user.set_password(password)
        user.save()
        return user

    def __str__(self):
        return self.email
