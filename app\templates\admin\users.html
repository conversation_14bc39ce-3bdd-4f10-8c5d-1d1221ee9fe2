{% extends "admin/admin_layout.html" %}

{% block title %}مدیریت کاربران - Rominext{% endblock %}

{% block extra_css %}
<style>
  /* Responsive table improvements */
  .table-responsive {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    background-color: #f8f9fa;
  }

  .table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
  }

  /* Compact button group for mobile */
  @media (max-width: 576px) {
    .btn-group .btn {
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
    }

    .dropdown-menu {
      font-size: 0.875rem;
    }

    .card-body {
      padding: 1rem;
    }
  }

  /* Better spacing for badges on mobile */
  @media (max-width: 992px) {
    .badge {
      font-size: 0.75rem;
      margin-bottom: 0.25rem;
    }
  }

  /* Improved modal responsiveness */
  @media (max-width: 576px) {
    .modal-dialog {
      margin: 0.5rem;
    }

    .modal-body {
      padding: 1rem;
    }
  }

  /* Loading state */
  .loading {
    opacity: 0.6;
    pointer-events: none;
  }

  /* Success/Error states */
  .alert-floating {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    min-width: 300px;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center py-3">
          <div>
            <h5 class="mb-0">مدیریت کاربران</h5>
            <p class="text-muted mb-0 small">مدیریت و دسترسی کاربران سیستم</p>
          </div>
          <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-1"></i> افزودن کاربر
          </button>
        </div>
        
        <!-- Filters in a single line -->
        <div class="card-body pb-0">
          <div class="d-flex flex-wrap align-items-center gap-2">
            <div class="flex-grow-1 me-auto">
              <input type="text" id="searchInput" class="form-control" placeholder="جستجو: نام یا ایمیل...">
            </div>
            <div class="d-flex align-items-center gap-2">
              <select id="roleFilter" class="form-select" style="width: auto; min-width: 120px;">
                <option value="">نقش: همه</option>
                <option value="ADMIN">مدیر</option>
                <option value="CUSTOMER">کاربر</option>
                <option value="SUPPORT">پشتیبان</option>
              </select>
              
              <select id="statusFilter" class="form-select" style="width: auto; min-width: 120px;">
                <option value="">وضعیت: همه</option>
                <option value="ACTIVE">فعال</option>
                <option value="INACTIVE">غیرفعال</option>
              </select>
              
              <button id="applyFilters" class="btn btn-success">اعمال فیلتر</button>
              <button id="resetFilters" class="btn btn-outline-secondary">حذف فیلتر</button>
            </div>
          </div>
        </div>
        
        <!-- Responsive Table -->
        <div class="table-responsive">
          <table class="table align-items-center mb-0" id="usersTable">
            <thead class="table-light">
              <tr>
                <th class="text-end d-none d-md-table-cell">نام</th>
                <th class="text-end">کاربر</th>
                <th class="text-end d-none d-lg-table-cell">نقش</th>
                <th class="text-end d-none d-sm-table-cell">وضعیت</th>
                <th class="text-end d-none d-xl-table-cell">تاریخ ثبت نام</th>
                <th class="text-center" style="width: 120px;">عملیات</th>
              </tr>
            </thead>
            <tbody id="usersTableBody">
              {% for user in users %}
              <tr data-user-id="{{ user.id }}">
                <td class="d-none d-md-table-cell">
                  <div class="fw-bold">{{ user.name }}</div>
                </td>
                <td>
                  <div class="d-flex flex-column">
                    <div class="fw-bold d-md-none">{{ user.name }}</div>
                    <div class="text-muted small">{{ user.email }}</div>
                    <div class="d-lg-none mt-1">
                      <span class="badge bg-{% if user.role.value == 'admin' %}primary{% elif user.role.value == 'support' %}info{% else %}secondary{% endif %} me-1">
                        {% if user.role.value == 'admin' %}مدیر{% elif user.role.value == 'support' %}پشتیبان{% else %}کاربر{% endif %}
                      </span>
                      <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                        {% if user.is_active %}فعال{% else %}غیرفعال{% endif %}
                      </span>
                    </div>
                  </div>
                </td>
                <td class="d-none d-lg-table-cell">
                  <span class="badge bg-{% if user.role.value == 'admin' %}primary{% elif user.role.value == 'support' %}info{% else %}secondary{% endif %}">
                    {% if user.role.value == 'admin' %}مدیر{% elif user.role.value == 'support' %}پشتیبان{% else %}کاربر{% endif %}
                  </span>
                </td>
                <td class="d-none d-sm-table-cell">
                  <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                    {% if user.is_active %}فعال{% else %}غیرفعال{% endif %}
                  </span>
                </td>
                <td class="d-none d-xl-table-cell">
                  <small class="text-muted">{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                </td>
                <td class="text-center">
                  <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editUser('{{ user.id }}')" title="ویرایش">
                      <i class="fas fa-edit"></i>
                    </button>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="عملیات بیشتر">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="viewUserDetails('{{ user.id }}')"><i class="fas fa-info-circle me-1"></i> جزئیات</a></li>
                        <li><a class="dropdown-item" href="#" onclick="resetPassword('{{ user.id }}')"><i class="fas fa-key me-1"></i> تغییر رمز</a></li>
                        <li><hr class="dropdown-divider"></li>
                        {% if user.is_active %}
                        <li><a class="dropdown-item text-warning" href="#" onclick="toggleUserStatus('{{ user.id }}', false)"><i class="fas fa-ban me-1"></i> غیرفعال</a></li>
                        {% else %}
                        <li><a class="dropdown-item text-success" href="#" onclick="toggleUserStatus('{{ user.id }}', true)"><i class="fas fa-check me-1"></i> فعال</a></li>
                        {% endif %}
                      </ul>
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer py-3">
          <div class="d-flex justify-content-between align-items-center">
            <p class="text-sm mb-0">نمایش <span id="currentItems">5</span> از <span id="totalItems">5</span> کاربر</p>
            <nav aria-label="Page navigation">
              <ul class="pagination pagination-sm mb-0">
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addUserModalLabel">افزودن کاربر جدید</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ url_for('admin.add_user') }}" method="POST">
        <div class="modal-body">
          <div class="mb-3">
            <label for="name" class="form-label">نام</label>
            <input type="text" class="form-control" id="name" name="name" required>
          </div>
          <div class="mb-3">
            <label for="email" class="form-label">ایمیل</label>
            <input type="email" class="form-control" id="email" name="email" required>
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">رمز عبور</label>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>
          <div class="mb-3">
            <label for="role" class="form-label">نقش</label>
            <select class="form-select" id="role" name="role">
              <option value="{{ UserRole.CUSTOMER.value }}">کاربر</option>
              <option value="{{ UserRole.ADMIN.value }}">مدیر</option>
              <option value="{{ UserRole.SUPPORT.value }}">پشتیبان</option>
            </select>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
            <label class="form-check-label" for="is_active">فعال</label>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
          <button type="submit" class="btn btn-primary">ذخیره</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editUserModalLabel">ویرایش کاربر</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="editUserForm">
          <input type="hidden" id="editUserId">
          <div class="mb-3">
            <label for="editName" class="form-label">نام</label>
            <input type="text" class="form-control" id="editName" required>
          </div>
          <div class="mb-3">
            <label for="editEmail" class="form-label">ایمیل</label>
            <input type="email" class="form-control" id="editEmail" required>
          </div>
          <div class="mb-3">
            <label for="editRole" class="form-label">نقش</label>
            <select class="form-select" id="editRole">
              <option value="{{ UserRole.CUSTOMER.value }}">کاربر</option>
              <option value="{{ UserRole.ADMIN.value }}">مدیر</option>
              <option value="{{ UserRole.SUPPORT.value }}">پشتیبان</option>
            </select>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="editIsActive">
            <label class="form-check-label" for="editIsActive">فعال</label>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
        <button type="button" class="btn btn-primary" onclick="saveUserChanges()">ذخیره تغییرات</button>
      </div>
    </div>
  </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="resetPasswordModalLabel">بازنشانی رمز عبور</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="resetPasswordForm">
          <input type="hidden" id="resetUserId">
          <div class="mb-3">
            <label for="newPassword" class="form-label">رمز عبور جدید</label>
            <input type="password" class="form-control" id="newPassword" required minlength="6">
            <div class="form-text">رمز عبور باید حداقل ۶ کاراکتر باشد</div>
          </div>
          <div class="mb-3">
            <label for="confirmPassword" class="form-label">تأیید رمز عبور</label>
            <input type="password" class="form-control" id="confirmPassword" required minlength="6">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
        <button type="button" class="btn btn-warning" onclick="savePasswordReset()">تغییر رمز عبور</button>
      </div>
    </div>
  </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="userDetailsModalLabel">جزئیات کاربر</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="userDetailsContent">
          <!-- User details will be loaded here -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Helper function to show notifications
  function showNotification(message, type = 'success') {
    // Remove existing notifications
    const existingAlerts = document.querySelectorAll('.alert-floating');
    existingAlerts.forEach(alert => alert.remove());

    // Create new notification
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-floating`;
    alertDiv.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }

  // Helper function to set loading state
  function setLoadingState(element, loading) {
    if (loading) {
      element.classList.add('loading');
      element.style.pointerEvents = 'none';
    } else {
      element.classList.remove('loading');
      element.style.pointerEvents = '';
    }
  }

  document.addEventListener('DOMContentLoaded', function() {
    // State
    let currentPage = 1;
    let totalPages = 1; // Default value
    let totalItems = 5;
    let itemsPerPage = 10;
    
    // Elements
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const resetFiltersBtn = document.getElementById('resetFilters');
    const usersTableBody = document.getElementById('usersTableBody');
    const currentItemsEl = document.getElementById('currentItems');
    const totalItemsEl = document.getElementById('totalItems');
    
    // Update pagination info
    if (currentItemsEl && totalItemsEl) {
      const rowCount = usersTableBody.querySelectorAll('tr').length;
      currentItemsEl.textContent = rowCount;
      totalItemsEl.textContent = rowCount;
    }
    
    // Event Listeners for filters
    if (applyFiltersBtn) {
      applyFiltersBtn.addEventListener('click', function() {
        // In a real implementation, this would call an API with the filter values
        alert('فیلترها اعمال شدند');
      });
    }
    
    if (resetFiltersBtn) {
      resetFiltersBtn.addEventListener('click', function() {
        if (searchInput) searchInput.value = '';
        if (roleFilter) roleFilter.value = '';
        if (statusFilter) statusFilter.value = '';
        
        // In a real implementation, this would reset the table data
        alert('فیلترها حذف شدند');
      });
    }
  });
  
  // Toggle user status function
  function toggleUserStatus(userId, active) {
    if (confirm(active ? 'آیا از فعال کردن این کاربر اطمینان دارید؟' : 'آیا از غیرفعال کردن این کاربر اطمینان دارید؟')) {
      const userRow = document.querySelector(`tr[data-user-id="${userId}"]`);
      setLoadingState(userRow, true);

      fetch(`/admin/api/admin/users/${userId}/toggle-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ active: active })
      })
      .then(response => response.json())
      .then(data => {
        setLoadingState(userRow, false);
        if (data.success) {
          showNotification(data.message || 'وضعیت کاربر با موفقیت تغییر کرد', 'success');
          // Update the UI
          setTimeout(() => location.reload(), 1000);
        } else {
          showNotification('خطا: ' + data.error, 'danger');
        }
      })
      .catch(error => {
        setLoadingState(userRow, false);
        console.error('Error:', error);
        showNotification('خطا در ارتباط با سرور', 'danger');
      });
    }
  }

  // Edit user function
  function editUser(userId) {
    // Fetch user data and populate the edit modal
    fetch(`/admin/api/admin/users/${userId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const user = data.user;
          document.getElementById('editUserId').value = user.id;
          document.getElementById('editName').value = user.name;
          document.getElementById('editEmail').value = user.email;
          document.getElementById('editRole').value = user.role;
          document.getElementById('editIsActive').checked = user.is_active;

          // Show the modal
          const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
          modal.show();
        } else {
          showNotification('خطا در بارگذاری اطلاعات کاربر: ' + data.error, 'danger');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('خطا در ارتباط با سرور', 'danger');
      });
  }

  // Save user changes
  function saveUserChanges() {
    const userId = document.getElementById('editUserId').value;
    const userData = {
      name: document.getElementById('editName').value,
      email: document.getElementById('editEmail').value,
      role: document.getElementById('editRole').value,
      is_active: document.getElementById('editIsActive').checked
    };

    const saveButton = document.querySelector('#editUserModal .btn-primary');
    setLoadingState(saveButton, true);
    saveButton.textContent = 'در حال ذخیره...';

    fetch(`/admin/api/admin/users/${userId}/edit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
      setLoadingState(saveButton, false);
      saveButton.textContent = 'ذخیره تغییرات';

      if (data.success) {
        // Hide modal and reload page
        const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
        modal.hide();
        showNotification(data.message || 'اطلاعات کاربر با موفقیت به‌روزرسانی شد', 'success');
        setTimeout(() => location.reload(), 1000);
      } else {
        showNotification('خطا: ' + data.error, 'danger');
      }
    })
    .catch(error => {
      setLoadingState(saveButton, false);
      saveButton.textContent = 'ذخیره تغییرات';
      console.error('Error:', error);
      showNotification('خطا در ارتباط با سرور', 'danger');
    });
  }

  // Reset password function
  function resetPassword(userId) {
    document.getElementById('resetUserId').value = userId;
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
  }

  // Save password reset
  function savePasswordReset() {
    const userId = document.getElementById('resetUserId').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (newPassword !== confirmPassword) {
      showNotification('رمزهای عبور مطابقت ندارند', 'warning');
      return;
    }

    if (newPassword.length < 6) {
      showNotification('رمز عبور باید حداقل ۶ کاراکتر باشد', 'warning');
      return;
    }

    const saveButton = document.querySelector('#resetPasswordModal .btn-warning');
    setLoadingState(saveButton, true);
    saveButton.textContent = 'در حال تغییر...';

    fetch(`/admin/api/admin/users/${userId}/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ password: newPassword })
    })
    .then(response => response.json())
    .then(data => {
      setLoadingState(saveButton, false);
      saveButton.textContent = 'تغییر رمز عبور';

      if (data.success) {
        // Hide modal and show success message
        const modal = bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal'));
        modal.hide();
        showNotification(data.message || 'رمز عبور با موفقیت تغییر کرد', 'success');
        // Clear form
        document.getElementById('newPassword').value = '';
        document.getElementById('confirmPassword').value = '';
      } else {
        showNotification('خطا: ' + data.error, 'danger');
      }
    })
    .catch(error => {
      setLoadingState(saveButton, false);
      saveButton.textContent = 'تغییر رمز عبور';
      console.error('Error:', error);
      showNotification('خطا در ارتباط با سرور', 'danger');
    });
  }

  // View user details function
  function viewUserDetails(userId) {
    fetch(`/admin/api/admin/users/${userId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const user = data.user;
          const detailsHtml = `
            <div class="row">
              <div class="col-md-6">
                <h6>اطلاعات کلی</h6>
                <table class="table table-sm">
                  <tr><td><strong>نام:</strong></td><td>${user.name}</td></tr>
                  <tr><td><strong>ایمیل:</strong></td><td>${user.email}</td></tr>
                  <tr><td><strong>نقش:</strong></td><td>${user.role === 'admin' ? 'مدیر' : user.role === 'support' ? 'پشتیبان' : 'کاربر'}</td></tr>
                  <tr><td><strong>وضعیت:</strong></td><td><span class="badge bg-${user.is_active ? 'success' : 'danger'}">${user.is_active ? 'فعال' : 'غیرفعال'}</span></td></tr>
                </table>
              </div>
              <div class="col-md-6">
                <h6>اطلاعات زمانی</h6>
                <table class="table table-sm">
                  <tr><td><strong>تاریخ ثبت نام:</strong></td><td>${user.created_at ? new Date(user.created_at).toLocaleDateString('fa-IR') : 'نامشخص'}</td></tr>
                  <tr><td><strong>آخرین ورود:</strong></td><td>${user.last_login ? new Date(user.last_login).toLocaleDateString('fa-IR') : 'هرگز'}</td></tr>
                </table>
              </div>
            </div>
          `;

          document.getElementById('userDetailsContent').innerHTML = detailsHtml;
          const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
          modal.show();
        } else {
          showNotification('خطا در بارگذاری اطلاعات کاربر: ' + data.error, 'danger');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('خطا در ارتباط با سرور', 'danger');
      });
  }
</script>
{% endblock %}
