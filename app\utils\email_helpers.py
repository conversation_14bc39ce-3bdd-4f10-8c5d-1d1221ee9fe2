"""
Email helper functions for common email operations
"""
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from urllib.parse import urlencode
import logging

from ..services.email_queue_service import email_queue_service
from ..models.user import User
from ..config import get_settings

logger = logging.getLogger(__name__)

class EmailHelpers:
    """Helper functions for common email operations"""
    
    @staticmethod
    def send_welcome_email(user: User, language: str = None) -> str:
        """
        Send welcome email to new user
        
        Args:
            user: User object
            language: Language code (defaults to user's preferred language)
            
        Returns:
            Queue ID for the email
        """
        try:
            # Get user's preferred language
            if not language:
                language = getattr(user, 'preferred_language', 'en')
            
            # Queue welcome email
            queue_id = email_queue_service.send_welcome_email(
                user_email=user.email,
                user_name=user.name or user.email,
                language=language
            )
            
            logger.info(f"Welcome email queued for user {user.email}")
            return queue_id
            
        except Exception as e:
            logger.error(f"Failed to send welcome email to {user.email}: {str(e)}")
            raise
    
    @staticmethod
    def send_password_reset_email(user: User, language: str = None) -> Dict[str, Any]:
        """
        Send password reset email with secure token
        
        Args:
            user: User object
            language: Language code (defaults to user's preferred language)
            
        Returns:
            Dictionary with queue_id and reset_token
        """
        try:
            # Generate secure reset token
            reset_token = secrets.token_urlsafe(32)
            reset_expires = datetime.utcnow() + timedelta(hours=24)
            
            # Store reset token in user document (you may want to create a separate collection)
            user.password_reset_token = reset_token
            user.password_reset_expires = reset_expires
            user.save()
            
            # Generate reset link
            settings = get_settings()
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:5000')
            reset_link = f"{base_url}/auth/reset-password?token={reset_token}"
            
            # Get user's preferred language
            if not language:
                language = getattr(user, 'preferred_language', 'en')
            
            # Queue password reset email
            queue_id = email_queue_service.send_password_reset_email(
                user_email=user.email,
                user_name=user.name or user.email,
                reset_link=reset_link,
                language=language
            )
            
            logger.info(f"Password reset email queued for user {user.email}")
            
            return {
                'queue_id': queue_id,
                'reset_token': reset_token,
                'expires_at': reset_expires
            }
            
        except Exception as e:
            logger.error(f"Failed to send password reset email to {user.email}: {str(e)}")
            raise
    
    @staticmethod
    def send_email_verification(user: User, language: str = None) -> Dict[str, Any]:
        """
        Send email verification email
        
        Args:
            user: User object
            language: Language code (defaults to user's preferred language)
            
        Returns:
            Dictionary with queue_id and verification_token
        """
        try:
            # Generate verification token
            verification_token = secrets.token_urlsafe(32)
            verification_expires = datetime.utcnow() + timedelta(hours=48)
            
            # Store verification token
            user.email_verification_token = verification_token
            user.email_verification_expires = verification_expires
            user.save()
            
            # Generate verification link
            settings = get_settings()
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:5000')
            verification_link = f"{base_url}/auth/verify-email?token={verification_token}"
            
            # Get user's preferred language
            if not language:
                language = getattr(user, 'preferred_language', 'en')
            
            # Queue verification email
            queue_id = email_queue_service.queue_email(
                template_key='email_verification',
                to_email=user.email,
                variables={
                    'user_name': user.name or user.email,
                    'verification_link': verification_link
                },
                language=language,
                to_name=user.name,
                user_id=str(user.id),
                priority=1
            )
            
            logger.info(f"Email verification queued for user {user.email}")
            
            return {
                'queue_id': queue_id,
                'verification_token': verification_token,
                'expires_at': verification_expires
            }
            
        except Exception as e:
            logger.error(f"Failed to send email verification to {user.email}: {str(e)}")
            raise
    
    @staticmethod
    def send_account_activation_email(user: User, language: str = None) -> str:
        """
        Send account activation email
        
        Args:
            user: User object
            language: Language code (defaults to user's preferred language)
            
        Returns:
            Queue ID for the email
        """
        try:
            # Get user's preferred language
            if not language:
                language = getattr(user, 'preferred_language', 'en')
            
            # Queue activation email
            queue_id = email_queue_service.queue_email(
                template_key='account_activation',
                to_email=user.email,
                variables={
                    'user_name': user.name or user.email
                },
                language=language,
                to_name=user.name,
                user_id=str(user.id),
                priority=1
            )
            
            logger.info(f"Account activation email queued for user {user.email}")
            return queue_id
            
        except Exception as e:
            logger.error(f"Failed to send account activation email to {user.email}: {str(e)}")
            raise
    
    @staticmethod
    def send_notification_email(
        user: User,
        template_key: str,
        variables: Dict[str, Any] = None,
        language: str = None,
        priority: int = 2
    ) -> str:
        """
        Send notification email to user
        
        Args:
            user: User object
            template_key: Email template key
            variables: Template variables
            language: Language code (defaults to user's preferred language)
            priority: Email priority (1=high, 2=normal, 3=low)
            
        Returns:
            Queue ID for the email
        """
        try:
            # Get user's preferred language
            if not language:
                language = getattr(user, 'preferred_language', 'en')
            
            # Merge variables with user data
            email_variables = {
                'user_name': user.name or user.email,
                **(variables or {})
            }
            
            # Queue notification email
            queue_id = email_queue_service.queue_email(
                template_key=template_key,
                to_email=user.email,
                variables=email_variables,
                language=language,
                to_name=user.name,
                user_id=str(user.id),
                priority=priority
            )
            
            logger.info(f"Notification email '{template_key}' queued for user {user.email}")
            return queue_id
            
        except Exception as e:
            logger.error(f"Failed to send notification email '{template_key}' to {user.email}: {str(e)}")
            raise
    
    @staticmethod
    def send_bulk_marketing_email(
        users: list,
        template_key: str,
        variables: Dict[str, Any] = None,
        language: str = 'en',
        send_at: datetime = None
    ) -> list:
        """
        Send bulk marketing email to multiple users
        
        Args:
            users: List of User objects
            template_key: Email template key
            variables: Common template variables
            language: Default language
            send_at: Scheduled send time
            
        Returns:
            List of queue IDs
        """
        try:
            # Prepare recipients list
            recipients = []
            for user in users:
                user_language = getattr(user, 'preferred_language', language)
                recipients.append({
                    'email': user.email,
                    'name': user.name,
                    'user_id': str(user.id),
                    'language': user_language,
                    'variables': {
                        'user_name': user.name or user.email
                    }
                })
            
            # Queue bulk emails
            queue_ids = email_queue_service.send_marketing_email(
                template_key=template_key,
                recipients=recipients,
                variables=variables,
                language=language,
                send_at=send_at
            )
            
            logger.info(f"Bulk marketing email '{template_key}' queued for {len(users)} users")
            return queue_ids
            
        except Exception as e:
            logger.error(f"Failed to send bulk marketing email '{template_key}': {str(e)}")
            raise
    
    @staticmethod
    def verify_reset_token(token: str) -> Optional[User]:
        """
        Verify password reset token and return user if valid
        
        Args:
            token: Reset token
            
        Returns:
            User object if token is valid, None otherwise
        """
        try:
            user = User.objects(
                password_reset_token=token,
                password_reset_expires__gte=datetime.utcnow()
            ).first()
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to verify reset token: {str(e)}")
            return None
    
    @staticmethod
    def verify_email_token(token: str) -> Optional[User]:
        """
        Verify email verification token and return user if valid
        
        Args:
            token: Verification token
            
        Returns:
            User object if token is valid, None otherwise
        """
        try:
            user = User.objects(
                email_verification_token=token,
                email_verification_expires__gte=datetime.utcnow()
            ).first()
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to verify email token: {str(e)}")
            return None
    
    @staticmethod
    def clear_reset_token(user: User):
        """Clear password reset token from user"""
        try:
            user.password_reset_token = None
            user.password_reset_expires = None
            user.save()
        except Exception as e:
            logger.error(f"Failed to clear reset token for user {user.email}: {str(e)}")
    
    @staticmethod
    def clear_verification_token(user: User):
        """Clear email verification token from user"""
        try:
            user.email_verification_token = None
            user.email_verification_expires = None
            user.email_verified = True
            user.save()
        except Exception as e:
            logger.error(f"Failed to clear verification token for user {user.email}: {str(e)}")
    
    @staticmethod
    def generate_unsubscribe_token(user_email: str) -> str:
        """
        Generate unsubscribe token for email
        
        Args:
            user_email: User email address
            
        Returns:
            Unsubscribe token
        """
        # Create a hash of email + secret for security
        settings = get_settings()
        secret = getattr(settings, 'SECRET_KEY', 'default-secret')
        
        data = f"{user_email}:{secret}"
        token = hashlib.sha256(data.encode()).hexdigest()[:32]
        
        return token
    
    @staticmethod
    def verify_unsubscribe_token(user_email: str, token: str) -> bool:
        """
        Verify unsubscribe token
        
        Args:
            user_email: User email address
            token: Unsubscribe token
            
        Returns:
            True if token is valid
        """
        expected_token = EmailHelpers.generate_unsubscribe_token(user_email)
        return token == expected_token
