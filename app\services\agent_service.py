from typing import List, Optional, Dict, Any
from datetime import datetime
import logging
from ..models.agent import Agent, AgentType

logger = logging.getLogger(__name__)

class AgentService:
    """Service for managing agent lifecycle and operations."""
    
    @staticmethod
    def create_agent(user_id: str, name: str, agent_type: str, 
                    description: str = "", config: Dict[str, Any] = None) -> Agent:
        """
        Create a new agent for a user with specified configuration.
        
        Args:
            user_id: ID of the user who owns this agent
            name: Name of the agent
            agent_type: Type of agent (from AgentType enum)
            description: Optional description of the agent
            config: Configuration dictionary for the agent
            
        Returns:
            The created Agent object
        """
        try:
            agent = Agent(
                user=user_id,
                name=name,
                description=description,
                agent_type=agent_type,
                config=config or {},
                status=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            agent.save()
            logger.info(f"Created agent {name} for user {user_id}")
            return agent
        except Exception as e:
            logger.error(f"Error creating agent: {str(e)}")
            raise

    @staticmethod
    def activate_agent(agent_id: str) -> bool:
        """
        Activate an agent (set status to active).
        
        Args:
            agent_id: ID of the agent to activate
            
        Returns:
            True if activation was successful, False otherwise
        """
        try:
            agent = Agent.objects.get(id=agent_id)
            agent.status = True
            agent.updated_at = datetime.utcnow()
            agent.save()
            logger.info(f"Activated agent {agent_id}")
            return True
        except Agent.DoesNotExist:
            logger.error(f"Agent {agent_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error activating agent: {str(e)}")
            return False

    @staticmethod
    def deactivate_agent(agent_id: str) -> bool:
        """
        Deactivate an agent (set status to inactive).
        
        Args:
            agent_id: ID of the agent to deactivate
            
        Returns:
            True if deactivation was successful, False otherwise
        """
        try:
            agent = Agent.objects.get(id=agent_id)
            agent.status = False
            agent.updated_at = datetime.utcnow()
            agent.save()
            logger.info(f"Deactivated agent {agent_id}")
            return True
        except Agent.DoesNotExist:
            logger.error(f"Agent {agent_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error deactivating agent: {str(e)}")
            return False

    @staticmethod
    def get_agents(user_id: str) -> List[Agent]:
        """
        Return all agents for a specific user.
        
        Args:
            user_id: ID of the user whose agents to retrieve
            
        Returns:
            List of Agent objects belonging to the user
        """
        try:
            return list(Agent.objects(user=user_id))
        except Exception as e:
            logger.error(f"Error retrieving agents for user {user_id}: {str(e)}")
            return []

    @staticmethod
    def get_agent(agent_id: str) -> Optional[Agent]:
        """
        Fetch a specific agent's details by its ID.
        
        Args:
            agent_id: ID of the agent to retrieve
            
        Returns:
            Agent object if found, None otherwise
        """
        try:
            return Agent.objects.get(id=agent_id)
        except Agent.DoesNotExist:
            logger.error(f"Agent {agent_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error retrieving agent {agent_id}: {str(e)}")
            return None

    @staticmethod
    def update_agent(agent_id: str, **kwargs) -> Optional[Agent]:
        """
        Update an agent's properties.
        
        Args:
            agent_id: ID of the agent to update
            **kwargs: Properties to update (name, description, config, etc.)
            
        Returns:
            Updated Agent object if successful, None otherwise
        """
        try:
            agent = Agent.objects.get(id=agent_id)
            
            # Update provided fields
            for key, value in kwargs.items():
                if hasattr(agent, key):
                    setattr(agent, key, value)
            
            # Always update the updated_at timestamp
            agent.updated_at = datetime.utcnow()
            agent.save()
            
            logger.info(f"Updated agent {agent_id}")
            return agent
        except Agent.DoesNotExist:
            logger.error(f"Agent {agent_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error updating agent {agent_id}: {str(e)}")
            return None