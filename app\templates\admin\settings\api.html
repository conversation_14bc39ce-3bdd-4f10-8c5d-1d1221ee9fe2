{% extends "admin/admin_layout.html" %}

{% block title %}تنظیمات API - Rominext{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.settings-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #fd7e14;
    box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e8690b 0%, #d91a72 100%);
    transform: translateY(-1px);
}

.settings-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.api-key-input {
    position: relative;
}

.api-key-toggle {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

.provider-card {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.provider-card:hover {
    border-color: #fd7e14;
    box-shadow: 0 0.125rem 0.25rem rgba(253, 126, 20, 0.15);
}

.provider-logo {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-right: 1rem;
}

.openai-logo { background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%); }
.groq-logo { background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); }
.facebook-logo { background: linear-gradient(135deg, #1877f2 0%, #166fe5 100%); }
.telegram-logo { background: linear-gradient(135deg, #0088cc 0%, #006699 100%); }
.huggingface-logo { background: linear-gradient(135deg, #ff9d00 0%, #ffb000 100%); }
.stability-logo { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header settings-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-key me-2"></i>
                            تنظیمات API
                        </h5>
                        <div>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-shield-alt me-1"></i>
                                محرمانه
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.settings_api') }}">
                        <!-- AI Providers Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-robot me-2"></i>
                                ارائه‌دهندگان هوش مصنوعی
                            </h6>
                            
                            <!-- OpenAI -->
                            <div class="provider-card">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="provider-logo openai-logo">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">OpenAI</h6>
                                        <small class="text-muted">GPT-4, GPT-3.5, DALL-E</small>
                                    </div>
                                </div>
                                <div class="api-key-input">
                                    <input type="password" class="form-control" id="openai_api_key" name="openai_api_key" 
                                           value="{{ settings.get('openai_api_key', '') }}" placeholder="sk-...">
                                    <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('openai_api_key')"></i>
                                </div>
                            </div>

                            <!-- Groq -->
                            <div class="provider-card">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="provider-logo groq-logo">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Groq</h6>
                                        <small class="text-muted">Llama, Mixtral - High Speed</small>
                                    </div>
                                </div>
                                <div class="api-key-input">
                                    <input type="password" class="form-control" id="groq_api_key" name="groq_api_key" 
                                           value="{{ settings.get('groq_api_key', '') }}" placeholder="gsk_...">
                                    <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('groq_api_key')"></i>
                                </div>
                            </div>

                            <!-- DeepSeek -->
                            <div class="provider-card">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="provider-logo" style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">DeepSeek</h6>
                                        <small class="text-muted">DeepSeek Coder, Chat</small>
                                    </div>
                                </div>
                                <div class="api-key-input">
                                    <input type="password" class="form-control" id="deepseek_api_key" name="deepseek_api_key" 
                                           value="{{ settings.get('deepseek_api_key', '') }}" placeholder="sk-...">
                                    <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('deepseek_api_key')"></i>
                                </div>
                            </div>

                            <!-- Other AI Providers -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="grok_api_key" class="form-label">Grok API Key</label>
                                    <div class="api-key-input">
                                        <input type="password" class="form-control" id="grok_api_key" name="grok_api_key" 
                                               value="{{ settings.get('grok_api_key', '') }}" placeholder="xai-...">
                                        <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('grok_api_key')"></i>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="huggingface_api_key" class="form-label">Hugging Face API Key</label>
                                    <div class="api-key-input">
                                        <input type="password" class="form-control" id="huggingface_api_key" name="huggingface_api_key" 
                                               value="{{ settings.get('huggingface_api_key', '') }}" placeholder="hf_...">
                                        <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('huggingface_api_key')"></i>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="together_api_key" class="form-label">Together AI API Key</label>
                                    <div class="api-key-input">
                                        <input type="password" class="form-control" id="together_api_key" name="together_api_key" 
                                               value="{{ settings.get('together_api_key', '') }}" placeholder="...">
                                        <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('together_api_key')"></i>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="stability_api_key" class="form-label">Stability AI API Key</label>
                                    <div class="api-key-input">
                                        <input type="password" class="form-control" id="stability_api_key" name="stability_api_key" 
                                               value="{{ settings.get('stability_api_key', '') }}" placeholder="sk-...">
                                        <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('stability_api_key')"></i>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="deepinfra_api_key" class="form-label">DeepInfra API Key</label>
                                    <div class="api-key-input">
                                        <input type="password" class="form-control" id="deepinfra_api_key" name="deepinfra_api_key" 
                                               value="{{ settings.get('deepinfra_api_key', '') }}" placeholder="...">
                                        <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('deepinfra_api_key')"></i>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="default_ai_provider" class="form-label">ارائه‌دهنده پیش‌فرض</label>
                                    <select class="form-select" id="default_ai_provider" name="default_ai_provider">
                                        <option value="openai" {% if settings.get('default_ai_provider') == 'openai' %}selected{% endif %}>OpenAI</option>
                                        <option value="groq" {% if settings.get('default_ai_provider') == 'groq' %}selected{% endif %}>Groq</option>
                                        <option value="deepseek" {% if settings.get('default_ai_provider') == 'deepseek' %}selected{% endif %}>DeepSeek</option>
                                        <option value="grok" {% if settings.get('default_ai_provider') == 'grok' %}selected{% endif %}>Grok</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Social Media APIs Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-share-alt me-2"></i>
                                شبکه‌های اجتماعی
                            </h6>
                            
                            <!-- Facebook -->
                            <div class="provider-card">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="provider-logo facebook-logo">
                                        <i class="fab fa-facebook-f"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Facebook</h6>
                                        <small class="text-muted">Page Management & Posting</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="facebook_page_access_token" class="form-label">Page Access Token</label>
                                        <div class="api-key-input">
                                            <input type="password" class="form-control" id="facebook_page_access_token" name="facebook_page_access_token" 
                                                   value="{{ settings.get('facebook_page_access_token', '') }}" placeholder="EAA...">
                                            <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('facebook_page_access_token')"></i>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="facebook_page_id" class="form-label">Page ID</label>
                                        <input type="text" class="form-control" id="facebook_page_id" name="facebook_page_id" 
                                               value="{{ settings.get('facebook_page_id', '') }}" placeholder="123456789...">
                                    </div>
                                </div>
                            </div>

                            <!-- Telegram -->
                            <div class="provider-card">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="provider-logo telegram-logo">
                                        <i class="fab fa-telegram-plane"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Telegram</h6>
                                        <small class="text-muted">Bot Integration</small>
                                    </div>
                                </div>
                                <div class="api-key-input">
                                    <input type="password" class="form-control" id="telegram_bot_token" name="telegram_bot_token" 
                                           value="{{ settings.get('telegram_bot_token', '') }}" placeholder="123456:ABC-DEF...">
                                    <i class="fas fa-eye api-key-toggle" onclick="toggleApiKey('telegram_bot_token')"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    ذخیره تنظیمات
                                </button>
                                <button type="reset" class="btn btn-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    بازنشانی
                                </button>
                            </div>
                            <div>
                                <a href="{{ url_for('admin.settings_security') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    تنظیمات امنیتی
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleApiKey(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = field.parentElement.querySelector('.api-key-toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
