"""
Storage Backend Implementations for File Upload Helper

This module provides different storage backend implementations:
- Local filesystem storage
- AWS S3 storage
- Google Cloud Storage
- Azure Blob Storage
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Tuple, Optional, Dict, Any
from pathlib import Path
from werkzeug.datastructures import FileStorage

# Optional cloud storage imports
try:
    import boto3
    from botocore.exceptions import ClientError
    AWS_AVAILABLE = True
except ImportError:
    AWS_AVAILABLE = False

try:
    from google.cloud import storage as gcs
    from google.cloud.exceptions import GoogleCloudError
    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False

try:
    from azure.storage.blob import BlobServiceClient
    from azure.core.exceptions import AzureError
    AZURE_AVAILABLE = True
except ImportError:
    AZURE_AVAILABLE = False


class StorageBackendInterface(ABC):
    """Abstract base class for storage backends"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def save_file(self, file: FileStorage, storage_path: str) -> Tuple[str, str]:
        """
        Save file to storage backend
        
        Args:
            file: File to save
            storage_path: Path where file should be stored
            
        Returns:
            Tuple of (file_url, file_path)
        """
        pass
    
    @abstractmethod
    def delete_file(self, file_path: str) -> bool:
        """
        Delete file from storage backend
        
        Args:
            file_path: Path to file to delete
            
        Returns:
            True if deletion was successful
        """
        pass
    
    @abstractmethod
    def file_exists(self, file_path: str) -> bool:
        """
        Check if file exists in storage backend
        
        Args:
            file_path: Path to check
            
        Returns:
            True if file exists
        """
        pass
    
    @abstractmethod
    def get_file_url(self, file_path: str) -> str:
        """
        Get public URL for file
        
        Args:
            file_path: Path to file
            
        Returns:
            Public URL for file
        """
        pass


class LocalStorageBackend(StorageBackendInterface):
    """Local filesystem storage backend"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_path = config.get('base_path', 'static')
        self.base_url = config.get('base_url', '/static')
    
    def save_file(self, file: FileStorage, storage_path: str) -> Tuple[str, str]:
        """Save file to local filesystem"""
        try:
            # Get full path
            full_path = os.path.join(self.base_path, storage_path)
            
            # Create directories if they don't exist
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            # Save file
            file.save(full_path)
            
            # Generate URL
            file_url = f"{self.base_url}/{storage_path.replace(os.sep, '/')}"
            
            return file_url, full_path
            
        except Exception as e:
            self.logger.error(f"Local storage save failed: {str(e)}")
            raise
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file from local filesystem"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Local storage delete failed: {str(e)}")
            return False
    
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists locally"""
        return os.path.exists(file_path)
    
    def get_file_url(self, file_path: str) -> str:
        """Get URL for local file"""
        # Convert absolute path to relative URL
        relative_path = os.path.relpath(file_path, self.base_path)
        return f"{self.base_url}/{relative_path.replace(os.sep, '/')}"


class S3StorageBackend(StorageBackendInterface):
    """AWS S3 storage backend"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not AWS_AVAILABLE:
            raise ImportError("boto3 is required for S3 storage backend")
        
        self.bucket_name = config.get('bucket_name')
        self.region = config.get('region', 'us-east-1')
        self.access_key = config.get('access_key')
        self.secret_key = config.get('secret_key')
        self.cdn_domain = config.get('cdn_domain')  # Optional CloudFront domain
        
        if not self.bucket_name:
            raise ValueError("S3 bucket_name is required")
        
        # Initialize S3 client
        session = boto3.Session(
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            region_name=self.region
        )
        self.s3_client = session.client('s3')
    
    def save_file(self, file: FileStorage, storage_path: str) -> Tuple[str, str]:
        """Save file to S3"""
        try:
            # Upload file to S3
            self.s3_client.upload_fileobj(
                file,
                self.bucket_name,
                storage_path,
                ExtraArgs={
                    'ContentType': file.content_type or 'application/octet-stream',
                    'ACL': 'public-read'  # Make file publicly accessible
                }
            )
            
            # Generate URL
            if self.cdn_domain:
                file_url = f"https://{self.cdn_domain}/{storage_path}"
            else:
                file_url = f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{storage_path}"
            
            return file_url, storage_path
            
        except ClientError as e:
            self.logger.error(f"S3 upload failed: {str(e)}")
            raise
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file from S3"""
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=file_path
            )
            return True
            
        except ClientError as e:
            self.logger.error(f"S3 delete failed: {str(e)}")
            return False
    
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists in S3"""
        try:
            self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=file_path
            )
            return True
            
        except ClientError:
            return False
    
    def get_file_url(self, file_path: str) -> str:
        """Get URL for S3 file"""
        if self.cdn_domain:
            return f"https://{self.cdn_domain}/{file_path}"
        else:
            return f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{file_path}"


class GCSStorageBackend(StorageBackendInterface):
    """Google Cloud Storage backend"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not GCS_AVAILABLE:
            raise ImportError("google-cloud-storage is required for GCS storage backend")
        
        self.bucket_name = config.get('bucket_name')
        self.project_id = config.get('project_id')
        self.credentials_path = config.get('credentials_path')
        self.cdn_domain = config.get('cdn_domain')  # Optional CDN domain
        
        if not self.bucket_name:
            raise ValueError("GCS bucket_name is required")
        
        # Initialize GCS client
        if self.credentials_path:
            self.client = gcs.Client.from_service_account_json(
                self.credentials_path,
                project=self.project_id
            )
        else:
            self.client = gcs.Client(project=self.project_id)
        
        self.bucket = self.client.bucket(self.bucket_name)
    
    def save_file(self, file: FileStorage, storage_path: str) -> Tuple[str, str]:
        """Save file to GCS"""
        try:
            blob = self.bucket.blob(storage_path)
            
            # Upload file
            file.seek(0)
            blob.upload_from_file(
                file,
                content_type=file.content_type or 'application/octet-stream'
            )
            
            # Make blob publicly accessible
            blob.make_public()
            
            # Generate URL
            if self.cdn_domain:
                file_url = f"https://{self.cdn_domain}/{storage_path}"
            else:
                file_url = blob.public_url
            
            return file_url, storage_path
            
        except GoogleCloudError as e:
            self.logger.error(f"GCS upload failed: {str(e)}")
            raise
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file from GCS"""
        try:
            blob = self.bucket.blob(file_path)
            blob.delete()
            return True
            
        except GoogleCloudError as e:
            self.logger.error(f"GCS delete failed: {str(e)}")
            return False
    
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists in GCS"""
        try:
            blob = self.bucket.blob(file_path)
            return blob.exists()
            
        except GoogleCloudError:
            return False
    
    def get_file_url(self, file_path: str) -> str:
        """Get URL for GCS file"""
        if self.cdn_domain:
            return f"https://{self.cdn_domain}/{file_path}"
        else:
            blob = self.bucket.blob(file_path)
            return blob.public_url


class AzureStorageBackend(StorageBackendInterface):
    """Azure Blob Storage backend"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not AZURE_AVAILABLE:
            raise ImportError("azure-storage-blob is required for Azure storage backend")
        
        self.account_name = config.get('account_name')
        self.account_key = config.get('account_key')
        self.container_name = config.get('container_name')
        self.cdn_domain = config.get('cdn_domain')  # Optional CDN domain
        
        if not all([self.account_name, self.account_key, self.container_name]):
            raise ValueError("Azure account_name, account_key, and container_name are required")
        
        # Initialize Azure client
        self.blob_service_client = BlobServiceClient(
            account_url=f"https://{self.account_name}.blob.core.windows.net",
            credential=self.account_key
        )
    
    def save_file(self, file: FileStorage, storage_path: str) -> Tuple[str, str]:
        """Save file to Azure Blob Storage"""
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=storage_path
            )
            
            # Upload file
            file.seek(0)
            blob_client.upload_blob(
                file,
                content_type=file.content_type or 'application/octet-stream',
                overwrite=True
            )
            
            # Generate URL
            if self.cdn_domain:
                file_url = f"https://{self.cdn_domain}/{storage_path}"
            else:
                file_url = f"https://{self.account_name}.blob.core.windows.net/{self.container_name}/{storage_path}"
            
            return file_url, storage_path
            
        except AzureError as e:
            self.logger.error(f"Azure upload failed: {str(e)}")
            raise
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file from Azure Blob Storage"""
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=file_path
            )
            blob_client.delete_blob()
            return True
            
        except AzureError as e:
            self.logger.error(f"Azure delete failed: {str(e)}")
            return False
    
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists in Azure Blob Storage"""
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=file_path
            )
            return blob_client.exists()
            
        except AzureError:
            return False
    
    def get_file_url(self, file_path: str) -> str:
        """Get URL for Azure blob"""
        if self.cdn_domain:
            return f"https://{self.cdn_domain}/{file_path}"
        else:
            return f"https://{self.account_name}.blob.core.windows.net/{self.container_name}/{file_path}"
