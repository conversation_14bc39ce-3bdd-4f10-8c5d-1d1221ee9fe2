import requests
from typing import Optional
import logging
import os
from .base_ai_provider import BaseAIProvider

class GrokAIProvider(BaseAIProvider):
    """
    Concrete implementation of BaseAIProvider for Grok AI services.
    """

    def __init__(self, api_key=None, base_url="https://api.grok.ai/v1", cache=None):
        """
        Initialize Grok AI provider with API key and base URL.
        
        :param api_key: The Grok AI API key (optional, will use env var if not provided)
        :param base_url: The base URL for Grok AI API
        :param cache: Optional cache mechanism
        """
        self.api_key = api_key or os.getenv("GROK_API_KEY")
        super().__init__(base_url=base_url, cache=cache)

    def authenticate(self):
        """
        Authenticate with Grok AI API.
        For Grok AI, we just need to store the API key.
        """
        if not self.api_key:
            import logging
            logging.warning("Grok AI API key is not set. Some functionality may be limited.")
        self.auth_token = self.api_key

    def get_headers(self):
        """
        Get headers for Grok AI API requests.
        """
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

    def make_request(self, endpoint: str, payload: dict) -> dict:
        """
        Make a request to the Grok AI API.

        :param endpoint: The specific endpoint to call
        :param payload: The payload to send in the request
        :return: The raw response from the API
        """
        self.log_request(endpoint, payload)

        headers = self.get_headers()

        response = requests.post(
            url=self.base_url.rstrip("/") + "/" + endpoint.lstrip("/"),
            headers=headers,
            json=payload
        )

        if response.ok:
            response_data = response.json()
            self.cache_response(endpoint, response_data)
            return response_data
        else:
            self.handle_error(f"Error {response.status_code}: {response.text}")
            response.raise_for_status()

    def process_response(self, response: dict) -> dict:
        """
        Process the response from Grok AI API to extract generated content.
        """
        if "choices" in response and response["choices"]:
            choice = response["choices"][0]
            if "message" in choice:
                return {"text": choice["message"].get("content", "")}
            return {"text": choice.get("text", "")}
        return {"error": "No valid response from Grok AI."}

    def generate_text(self, prompt: str, model: str = "grok-3.5-turbo", max_tokens: int = 100) -> dict:
        """
        Generate text using Grok AI's completion model.
        """
        endpoint = "completions"
        payload = {
            "model": model,
            "prompt": prompt,
            "max_tokens": max_tokens,
            "temperature": 0.7
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}

    def chat_with_grok(self, messages: list, model: str = "grok-3.5-turbo", max_tokens: int = 150) -> dict:
        """
        Chat with Grok AI's GPT model (chat completion).
        """
        endpoint = "chat/completions"
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
