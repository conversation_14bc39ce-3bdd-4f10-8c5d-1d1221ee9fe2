{% extends "main_layout.html" %}

{% block title %}{{ t('contact.title') }} - Rominext{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold">{{ t('contact.title') }}</h1>
            <p class="lead">{{ t('contact.subtitle') }}</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm p-4">
                <h3 class="mb-4">{{ t('contact.info_title') }}</h3>
                <ul class="list-unstyled">
                    <li class="mb-3">
                        <i class="fas fa-envelope me-2 text-success"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </li>
                    <li class="mb-3">
                        <i class="fas fa-phone-alt me-2 text-success"></i>
                        <a href="tel:02187635210">۰۲۱-۸۷۶۳۵۲۱</a>
                    </li>
                    <li class="mb-3">
                        <i class="fas fa-building me-2 text-success"></i>
                        <a href="https://niravin.com">{{ t('contact.company') }}</a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow-sm p-4">
                <h3 class="mb-4">{{ t('contact.form_title') }}</h3>
                <form>
                    <div class="mb-3">
                        <label for="name" class="form-label">{{ t('contact.name_label') }}</label>
                        <input type="text" class="form-control" id="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">{{ t('contact.email_label') }}</label>
                        <input type="email" class="form-control" id="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">{{ t('contact.message_label') }}</label>
                        <textarea class="form-control" id="message" rows="4" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-success">{{ t('contact.submit_button') }}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}