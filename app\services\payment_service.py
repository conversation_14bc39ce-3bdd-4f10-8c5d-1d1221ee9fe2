from typing import List, Optional
from datetime import datetime
import logging
from ..models.payment import Payment, PaymentStatus
from ..models.subscription import Subscription, SubscriptionStatus
from ..models.user import User
from ..models.notification import Notification, NotificationType

logger = logging.getLogger(__name__)

class PaymentService:
    """Service for managing user payments"""
    
    @staticmethod
    def create_payment(
        user_id: str,
        subscription_id: str,
        amount: float,
        currency: str = "USD",
        transaction_reference: str = None
    ) -> Optional[Payment]:
        """
        Create a new payment record
        
        Args:
            user_id: The ID of the user
            subscription_id: The ID of the subscription
            amount: Payment amount
            currency: Payment currency (default: USD)
            transaction_reference: External transaction ID
            
        Returns:
            The created Payment object or None
        """
        try:
            user = User.objects.get(id=user_id)
            subscription = Subscription.objects.get(id=subscription_id)
            
            payment = Payment(
                user=user,
                subscription=subscription,
                amount=amount,
                currency=currency,
                payment_date=datetime.utcnow(),
                status=PaymentStatus.PENDING,
                transaction_reference=transaction_reference
            )
            
            return payment.save()
        except Exception as e:
            logger.error(f"Error creating payment: {str(e)}")
            return None
    
    @staticmethod
    def get_payment(payment_id: str) -> Optional[Payment]:
        """
        Get a payment by ID
        
        Args:
            payment_id: The ID of the payment
            
        Returns:
            The Payment object or None
        """
        try:
            return Payment.objects.get(id=payment_id)
        except Exception as e:
            logger.error(f"Error fetching payment: {str(e)}")
            return None
    
    @staticmethod
    def update_payment_status(
        payment_id: str, 
        status: PaymentStatus,
        transaction_reference: str = None
    ) -> Optional[Payment]:
        """
        Update payment status and handle subscription updates
        
        Args:
            payment_id: The ID of the payment
            status: New payment status
            transaction_reference: External transaction ID (optional)
            
        Returns:
            The updated Payment object or None
        """
        try:
            payment = Payment.objects.get(id=payment_id)
            payment.status = status
            
            if transaction_reference:
                payment.transaction_reference = transaction_reference
                
            # Handle subscription status updates based on payment status
            if status == PaymentStatus.SUCCESSFUL:
                subscription = payment.subscription
                subscription.status = SubscriptionStatus.ACTIVE
                subscription.updated_at = datetime.utcnow()
                subscription.save()
                
                # Create success notification
                Notification(
                    user=payment.user,
                    title="Payment Successful",
                    body=f"Your payment of {payment.amount} {payment.currency} was successful.",
                    notification_type=NotificationType.SUBSCRIPTION
                ).save()
                
            elif status == PaymentStatus.FAILED:
                subscription = payment.subscription
                subscription.status = SubscriptionStatus.EXPIRED
                subscription.updated_at = datetime.utcnow()
                subscription.save()
                
                # Create failure notification
                Notification(
                    user=payment.user,
                    title="Payment Failed",
                    body=f"Your payment of {payment.amount} {payment.currency} has failed.",
                    notification_type=NotificationType.SUBSCRIPTION
                ).save()
            
            return payment.save()
        except Exception as e:
            logger.error(f"Error updating payment status: {str(e)}")
            return None
    
    @staticmethod
    def get_user_payments(user_id: str) -> List[Payment]:
        """
        Get all payments for a user
        
        Args:
            user_id: The ID of the user
            
        Returns:
            List of Payment objects
        """
        try:
            user = User.objects.get(id=user_id)
            return Payment.objects(user=user).order_by('-payment_date')
        except Exception as e:
            logger.error(f"Error fetching user payments: {str(e)}")
            return []