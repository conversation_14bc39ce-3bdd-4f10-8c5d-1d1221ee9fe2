{% extends "main_layout.html" %} {% block head %} {{ moment.include_moment() }}
{% endblock %} {% block content %}
<div class="dashboard-wrapper">
  <div class="container-fluid px-4">
    <div class="row">
      <!-- Sidebar -->
      <div class="col-md-3 col-lg-2 sidebar-wrapper">
        <div class="sidebar">
          <div class="pt-3">
            <div class="sidebar-search mb-3 px-3">
              <div class="input-group">
                <input
                  type="text"
                  class="form-control"
                  id="sidebarSearch"
                  placeholder="جستجو..."
                />
                <button class="btn btn-outline-secondary" type="button">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>
            <ul class="nav flex-column">
              <li class="nav-item">
                <a
                  class="nav-link main-menu-item {% if request.endpoint == 'dashboard.index' %}active{% endif %}"
                  href="{{ url_for('dashboard.index') }}"
                >
                  <i class="fas fa-tachometer-alt ms-2"></i>
                  {{ t('nav.dashboard') }}
                </a>
              </li>

              <!-- Account Management -->
              <li class="nav-item">
                <a
                  class="nav-link main-menu-item {% if request.endpoint.startswith('account.') %}active{% endif %}"
                  data-bs-toggle="collapse"
                  href="#accountMenu"
                  role="button"
                  aria-expanded="false"
                >
                  <i class="fas fa-user-circle ms-2"></i>
                  مدیریت صفحات
                </a>
                <div class="collapse" id="accountMenu">
                  <ul class="nav flex-column submenu">
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('account.index') }}"
                      >
                        <i class="fas fa-list ms-2"></i>
                        مشاهده صفحات
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('account.connect') }}"
                      >
                        <i class="fas fa-plug ms-2"></i>
                        افزودن صفحه جدید
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- Content Creation (removed number) -->
              <li class="nav-item">
                <a
                  class="nav-link main-menu-item"
                  data-bs-toggle="collapse"
                  href="#contentCreationMenu"
                  role="button"
                  aria-expanded="false"
                >
                  <i class="fas fa-pen-fancy ms-2"></i>
                  مدیریت محتوا
                </a>
                <div class="collapse" id="contentCreationMenu">
                  <ul class="nav flex-column submenu">
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('dashboard.posts_list') }}"
                      >
                        <i class="fas fa-list ms-2"></i>
                        لیست پست‌ها
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('dashboard.quick_post') }}"
                      >
                        <i class="fas fa-bolt ms-2"></i>
                        تولید فوری محتوا
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('dashboard.create_post') }}"
                      >
                        <i class="fas fa-edit ms-2"></i>
                        تولید پیشرفته محتوا
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('dashboard.create_post') }}"
                      >
                        <i class="fas fa-edit ms-2"></i>
                        تنظیمات هوشمند محتوا
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item {% if request.endpoint.startswith('strategy.') %}active{% endif %}"
                        href="{{ url_for('strategy.index') }}"
                      >
                        <i class="fas fa-chess ms-2"></i>
                        استراتژی محتوا
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-lightbulb ms-2"></i>
                        پیشنهاد محتوا
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-list-ol ms-2"></i>
                        محتوای سریالی
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- Publishing and Planning (removed number) -->
              <li class="nav-item">
                <a
                  class="nav-link main-menu-item"
                  data-bs-toggle="collapse"
                  href="#publishingMenu"
                  role="button"
                  aria-expanded="false"
                >
                  <i class="fas fa-calendar-alt ms-2"></i>
                  تحلیل
                </a>
                <div class="collapse" id="publishingMenu">
                  <ul class="nav flex-column submenu">
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-chart-bar ms-2"></i>
                        بررسی عملکرد پست‌ها
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-smile ms-2"></i>
                        تحلیل احساسات کامنت‌ها
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-vial ms-2"></i>
                        A/B تست محتوا
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-user-check ms-2"></i>
                        یادگیری از مخاطب
                      </a>
                    </li>

                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('scheduling.schedule_post') }}"
                      >
                        <i class="fas fa-clock ms-2"></i>
                        زمان‌ بندی انتشار
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-hourglass-half ms-2"></i>
                        ساعات طلایی پست
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-hourglass-half ms-2"></i>
                        تحلیل رقبا
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- Interaction Management (removed number) -->
              <li class="nav-item">
                <a
                  class="nav-link main-menu-item"
                  data-bs-toggle="collapse"
                  href="#interactionMenu"
                  role="button"
                  aria-expanded="false"
                >
                  <i class="fas fa-comments ms-2"></i>
                  مدیریت کامنت
                </a>
                <div class="collapse" id="interactionMenu">
                  <ul class="nav flex-column submenu">
                    <li class="nav-item">
                      <a
                        class="nav-link submenu-item"
                        href="{{ url_for('dashboard.index') }}"
                      >
                        <i class="fas fa-comments me-2"></i> مانیتور
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-reply ms-2"></i>
                        پاسخ خودکار
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- Agents and Plugins (removed number) -->
              <li class="nav-item">
                <a
                  class="nav-link main-menu-item"
                  data-bs-toggle="collapse"
                  href="#agentsMenu"
                  role="button"
                  aria-expanded="false"
                >
                  <i class="fas fa-robot ms-2"></i>
                  ایجنت (َAgent)
                </a>
                <div class="collapse" id="agentsMenu">
                  <ul class="nav flex-column submenu">
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-toggle-on ms-2"></i>
                        فعال‌سازی یا غیرفعال‌سازی
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-plus-circle ms-2"></i>
                        ساخت Agent جدید
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-store ms-2"></i>
                        مارکت‌پلیس Agentها
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- Brand Settings (removed number) -->
              <li class="nav-item">
                <a
                  class="nav-link main-menu-item"
                  data-bs-toggle="collapse"
                  href="#brandSettingsMenu"
                  role="button"
                  aria-expanded="false"
                >
                  <i class="fas fa-cog ms-2"></i>
                  تنظیمات 
                </a>
                <div class="collapse" id="brandSettingsMenu">
                  <ul class="nav flex-column submenu">
                    <!-- Remove the profile link -->
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-tags ms-2"></i>
                        پروفایل
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link submenu-item" href="#">
                        <i class="fas fa-users ms-2"></i>
                        سیستم
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Notification Center -->
      <div
        class="notification-center position-fixed"
        style="top: 70px; right: 20px; z-index: 1000"
      >
        <div id="notificationContainer"></div>
      </div>

      <!-- Main content -->
      <div class="col-md-9 col-lg-9 main-content">
        <div class="px-md-4 py-4">
          {% block dashboard_content %}
          <!-- This block will be overridden by child templates -->
          {% endblock %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Dashboard Layout */
  .dashboard-wrapper {
    min-height: calc(100vh - 56px - 300px);
    margin-top: 2rem;
    margin-bottom: 2rem;
    position: relative;
  }

  .container-fluid {
    max-width: 1600px;
    margin: 0 auto;
  }

  /* Sidebar */
  .sidebar-wrapper {
    padding: 0;
  }

  .sidebar {
    background-color: #f8f9fa;
    border-left: 1px solid #dee2e6;
    height: 100%;
    padding: 0;
  }

  .nav-link {
    text-align: right;
    padding: 0.8rem 1.5rem;
    color: #333;
    transition: all 0.3s ease;
  }

  .nav-link:hover {
    background-color: #e9ecef;
  }

  .nav-link.active {
    background-color: #e3e6e9;
    color: #000;
  }

  .nav-link i {
    float: right;
    margin-left: 10px;
    margin-right: 0;
    width: 20px;
    text-align: center;
  }

  /* Main content area */
  .main-content {
    padding-right: 0;
    padding-left: 15px;
  }

  /* RTL specific styles */
  .card {
    text-align: right;
  }

  .card-header {
    text-align: right;
  }

  .card-body {
    text-align: right;
  }

  .list-group-item {
    text-align: right;
  }

  /* Form controls RTL */
  .form-control {
    text-align: right;
  }

  .input-group .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
  }

  .input-group .input-group-text {
    border-radius: 0.25rem 0 0 0.25rem;
  }

  /* Tables RTL */
  .table th,
  .table td {
    text-align: right;
  }

  /* Alerts and notifications RTL */
  .alert {
    text-align: right;
  }

  /* Dropdown menus RTL */
  .dropdown-menu {
    text-align: right;
  }

  .dropdown-item {
    text-align: right;
  }

  /* Responsive styles */
  @media (max-width: 767.98px) {
    .dashboard-wrapper {
      min-height: auto;
      margin-top: 1rem;
      margin-bottom: 1rem;
    }

    .sidebar {
      border-left: none;
      border-bottom: 1px solid #dee2e6;
      margin-bottom: 1rem;
    }

    .main-content {
      padding-right: 15px;
    }

    .container-fluid {
      padding-left: 10px;
      padding-right: 10px;
    }
  }
</style>
{% endblock %}

<script>
  function showDashboardNotification(message, type = "info") {
    const notification = document.createElement("div");
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    document.getElementById("notificationContainer").appendChild(notification);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
      const alert = bootstrap.Alert.getOrCreateInstance(notification);
      alert.close();
    }, 5000);
  }
</script>
