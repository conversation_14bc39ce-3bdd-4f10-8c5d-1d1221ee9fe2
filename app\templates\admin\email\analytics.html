{% extends "admin/admin_layout.html" %}

{% block title %}Email Analytics - Rominext{% endblock %}

{% block extra_css %}
<style>
.analytics-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.analytics-card .card-header {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    height: 100%;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-sent { color: #007bff; }
.stat-delivered { color: #28a745; }
.stat-opened { color: #ffc107; }
.stat-clicked { color: #dc3545; }

.chart-container {
    position: relative;
    height: 300px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-chart-line me-2"></i>
                    Email Analytics
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-secondary" onclick="changePeriod(7)">7 Days</button>
                    <button type="button" class="btn btn-outline-secondary active" onclick="changePeriod(30)">30 Days</button>
                    <button type="button" class="btn btn-outline-secondary" onclick="changePeriod(90)">90 Days</button>
                </div>
            </div>

            <!-- Overall Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number stat-sent">{{ overall_stats.total_sent or 0 }}</div>
                        <div class="stat-label">Total Sent</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number stat-delivered">{{ overall_stats.delivered or 0 }}</div>
                        <div class="stat-label">Delivered</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number stat-opened">{{ overall_stats.opened or 0 }}</div>
                        <div class="stat-label">Opened</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number stat-clicked">{{ overall_stats.clicked or 0 }}</div>
                        <div class="stat-label">Clicked</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Template Performance -->
                <div class="col-lg-8 mb-4">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>
                                Template Performance
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if template_performance %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Template Name</th>
                                            <th>Sent</th>
                                            <th>Delivered</th>
                                            <th>Opened</th>
                                            <th>Clicked</th>
                                            <th>Open Rate</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for template in template_performance %}
                                        <tr>
                                            <td>
                                                <strong>{{ template.template_name }}</strong>
                                                <br><small class="text-muted">{{ template.template_key }}</small>
                                            </td>
                                            <td>{{ template.total_sent }}</td>
                                            <td>{{ template.delivered }}</td>
                                            <td>{{ template.opened }}</td>
                                            <td>{{ template.clicked }}</td>
                                            <td>
                                                <span class="badge {% if template.open_rate >= 20 %}bg-success{% elif template.open_rate >= 10 %}bg-warning{% else %}bg-danger{% endif %}">
                                                    {{ template.open_rate }}%
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5>No Analytics Data</h5>
                                <p class="text-muted">Send some emails to see analytics data here.</p>
                                <a href="{{ url_for('admin.email_test') }}" class="btn btn-primary">
                                    <i class="fas fa-vial me-2"></i>Test Email System
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Queue Statistics -->
                <div class="col-lg-4 mb-4">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                Email Queue
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if queue_stats %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Pending</span>
                                    <strong class="text-warning">{{ queue_stats.pending or 0 }}</strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Processing</span>
                                    <strong class="text-info">{{ queue_stats.processing or 0 }}</strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Completed</span>
                                    <strong class="text-success">{{ queue_stats.completed or 0 }}</strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Failed</span>
                                    <strong class="text-danger">{{ queue_stats.failed or 0 }}</strong>
                                </div>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <span><strong>Total</strong></span>
                                <strong>{{ (queue_stats.pending or 0) + (queue_stats.processing or 0) + (queue_stats.completed or 0) + (queue_stats.failed or 0) }}</strong>
                            </div>
                            {% else %}
                            <div class="text-center py-3">
                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">No queue data</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="analytics-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center py-3">
                                <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">No recent activity</p>
                                <small class="text-muted">Email activity will appear here</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Statistics Chart -->
            {% if daily_stats %}
            <div class="row">
                <div class="col-12">
                    <div class="analytics-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-area me-2"></i>
                                Daily Email Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="dailyStatsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function changePeriod(days) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Reload page with new period
    const url = new URL(window.location);
    url.searchParams.set('days', days);
    window.location.href = url.toString();
}

{% if daily_stats %}
// Daily Statistics Chart
const ctx = document.getElementById('dailyStatsChart').getContext('2d');
const dailyStatsChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [{% for stat in daily_stats %}'{{ stat.date.strftime("%m/%d") }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'Sent',
            data: [{% for stat in daily_stats %}{{ stat.sent }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }, {
            label: 'Delivered',
            data: [{% for stat in daily_stats %}{{ stat.delivered }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
        }, {
            label: 'Opened',
            data: [{% for stat in daily_stats %}{{ stat.opened }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: '#ffc107',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
