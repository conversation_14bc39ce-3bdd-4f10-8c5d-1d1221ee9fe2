{"swagger": "2.0", "info": {"title": "Rominext API", "description": "API documentation for Rominext", "version": "1.0.0"}, "basePath": "/api", "schemes": ["http", "https"], "paths": {"/telegram/verify-code": {"post": {"tags": ["Telegram"], "summary": "Verify a Telegram code", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Verification data", "required": true, "schema": {"type": "object", "properties": {"telegram_user_id": {"type": "string"}, "code": {"type": "string"}}}}], "responses": {"200": {"description": "User verified successfully"}, "400": {"description": "Invalid input"}, "429": {"description": "Rate limit exceeded"}}}}, "/telegram/connect-channel": {"post": {"tags": ["Telegram"], "summary": "Connect a Telegram channel", "security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Channel data", "required": true, "schema": {"type": "object", "properties": {"telegram_user_id": {"type": "string"}, "channel_id": {"type": "string"}, "channel_title": {"type": "string"}}}}], "responses": {"200": {"description": "Channel connected successfully"}, "401": {"description": "Unauthorized"}, "429": {"description": "Rate limit exceeded"}}}}, "/telegram/get-connected-channels": {"get": {"tags": ["Telegram"], "summary": "Get connected Telegram channels", "security": [{"BearerAuth": []}], "produces": ["application/json"], "parameters": [{"in": "query", "name": "telegram_user_id", "description": "Telegram user ID", "required": true, "type": "string"}], "responses": {"200": {"description": "List of connected channels"}, "400": {"description": "Missing parameters"}, "401": {"description": "Unauthorized"}, "429": {"description": "Rate limit exceeded"}}}}, "/telegram/verify-forwarded-message": {"post": {"tags": ["Telegram"], "summary": "Verify a forwarded message", "security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Message data", "required": true, "schema": {"type": "object", "properties": {"channel_id": {"type": "string"}}}}], "responses": {"200": {"description": "Message verified successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Message not found or channel mismatch"}, "429": {"description": "Rate limit exceeded"}}}}, "/telegram/set-webhook": {"post": {"tags": ["Telegram"], "summary": "Set a webhook for Telegram updates", "security": [{"BearerAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Webhook data", "required": true, "schema": {"type": "object", "properties": {"webhook_url": {"type": "string"}}}}], "responses": {"200": {"description": "Webhook set successfully"}, "400": {"description": "Invalid URL format"}, "401": {"description": "Unauthorized"}, "429": {"description": "Rate limit exceeded"}}}}, "/telegram/remove-webhook": {"post": {"tags": ["Telegram"], "summary": "Remove a Telegram webhook", "security": [{"BearerAuth": []}], "produces": ["application/json"], "responses": {"200": {"description": "Webhook removed successfully"}, "401": {"description": "Unauthorized"}, "429": {"description": "Rate limit exceeded"}}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}