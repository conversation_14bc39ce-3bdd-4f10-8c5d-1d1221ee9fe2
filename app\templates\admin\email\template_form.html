{% extends "admin/admin_layout.html" %}

{% block title %}{{ 'Edit' if template else 'Create' }} Email Template - Rominext{% endblock %}

{% block extra_css %}
<style>
.template-form-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.template-form-card .card-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.language-tabs .nav-link {
    border: 1px solid #dee2e6;
    color: #495057;
}

.language-tabs .nav-link.active {
    background-color: #fd7e14;
    border-color: #fd7e14;
    color: white;
}

.form-control:focus {
    border-color: #fd7e14;
    box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e8690b 0%, #d91a72 100%);
    transform: translateY(-1px);
}

.variable-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
}

.variable-tag:hover {
    background-color: #fd7e14;
    color: white;
}

.preview-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
    max-height: 400px;
    overflow-y: auto;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-{{ 'edit' if template else 'plus' }} me-2"></i>
                    {{ 'Edit' if template else 'Create' }} Email Template
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Templates
                    </a>
                    {% if template %}
                    <button type="button" class="btn btn-outline-info" onclick="previewTemplate()">
                        <i class="fas fa-eye me-2"></i>Preview
                    </button>
                    {% endif %}
                </div>
            </div>

            <form id="templateForm" method="POST">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Basic Information -->
                        <div class="template-form-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Basic Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="template_key" class="form-label">Template Key *</label>
                                        <input type="text" class="form-control" id="template_key" name="template_key" 
                                               value="{{ template.template_key if template else '' }}" 
                                               {% if template %}readonly{% endif %} required>
                                        <div class="form-text">Unique identifier for the template (e.g., welcome, password_reset)</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Template Name *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="{{ template.name if template else '' }}" required>
                                        <div class="form-text">Human-readable name for the template</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="category" class="form-label">Category *</label>
                                        <select class="form-select" id="category" name="category" required>
                                            <option value="">Select Category</option>
                                            <option value="authentication" {% if template and template.category == 'authentication' %}selected{% endif %}>Authentication</option>
                                            <option value="notification" {% if template and template.category == 'notification' %}selected{% endif %}>Notification</option>
                                            <option value="marketing" {% if template and template.category == 'marketing' %}selected{% endif %}>Marketing</option>
                                            <option value="system" {% if template and template.category == 'system' %}selected{% endif %}>System</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="default_language" class="form-label">Default Language</label>
                                        <select class="form-select" id="default_language" name="default_language">
                                            <option value="en" {% if not template or template.default_language == 'en' %}selected{% endif %}>English</option>
                                            <option value="fa" {% if template and template.default_language == 'fa' %}selected{% endif %}>Persian</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="2">{{ template.description if template else '' }}</textarea>
                                        <div class="form-text">Brief description of when this template is used</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Template Content -->
                        <div class="template-form-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-alt me-2"></i>
                                    Template Content
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Language Tabs -->
                                <ul class="nav nav-tabs language-tabs mb-3" id="languageTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="en-tab" data-bs-toggle="tab" data-bs-target="#en-content" type="button" role="tab">
                                            <i class="fas fa-flag me-2"></i>English
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="fa-tab" data-bs-toggle="tab" data-bs-target="#fa-content" type="button" role="tab">
                                            <i class="fas fa-flag me-2"></i>Persian
                                        </button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="languageTabContent">
                                    <!-- English Content -->
                                    <div class="tab-pane fade show active" id="en-content" role="tabpanel">
                                        <div class="mb-3">
                                            <label for="subject_en" class="form-label">Subject (English) *</label>
                                            <input type="text" class="form-control" id="subject_en" name="subjects[en]" 
                                                   value="{{ template.subjects.en if template and template.subjects else '' }}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="html_content_en" class="form-label">HTML Content (English) *</label>
                                            <textarea class="form-control" id="html_content_en" name="html_content[en]" rows="15" required>{{ template.html_content.en if template and template.html_content else '' }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="text_content_en" class="form-label">Text Content (English)</label>
                                            <textarea class="form-control" id="text_content_en" name="text_content[en]" rows="10">{{ template.text_content.en if template and template.text_content else '' }}</textarea>
                                        </div>
                                    </div>

                                    <!-- Persian Content -->
                                    <div class="tab-pane fade" id="fa-content" role="tabpanel">
                                        <div class="mb-3">
                                            <label for="subject_fa" class="form-label">Subject (Persian)</label>
                                            <input type="text" class="form-control" id="subject_fa" name="subjects[fa]" 
                                                   value="{{ template.subjects.fa if template and template.subjects else '' }}" dir="rtl">
                                        </div>
                                        <div class="mb-3">
                                            <label for="html_content_fa" class="form-label">HTML Content (Persian)</label>
                                            <textarea class="form-control" id="html_content_fa" name="html_content[fa]" rows="15" dir="rtl">{{ template.html_content.fa if template and template.html_content else '' }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="text_content_fa" class="form-label">Text Content (Persian)</label>
                                            <textarea class="form-control" id="text_content_fa" name="text_content[fa]" rows="10" dir="rtl">{{ template.text_content.fa if template and template.text_content else '' }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Variables -->
                        <div class="template-form-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-code me-2"></i>
                                    Template Variables
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="variables" class="form-label">Variables</label>
                                    <input type="text" class="form-control" id="variables" name="variables" 
                                           value="{{ template.variables|join(', ') if template and template.variables else '' }}" 
                                           placeholder="user_name, reset_link, verification_code">
                                    <div class="form-text">Comma-separated list of variables</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Common Variables</label>
                                    <div>
                                        <span class="variable-tag" onclick="addVariable('user_name')">user_name</span>
                                        <span class="variable-tag" onclick="addVariable('user_email')">user_email</span>
                                        <span class="variable-tag" onclick="addVariable('site_name')">site_name</span>
                                        <span class="variable-tag" onclick="addVariable('site_url')">site_url</span>
                                        <span class="variable-tag" onclick="addVariable('reset_link')">reset_link</span>
                                        <span class="variable-tag" onclick="addVariable('verification_link')">verification_link</span>
                                        <span class="variable-tag" onclick="addVariable('verification_code')">verification_code</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Usage</label>
                                    <div class="small text-muted">
                                        <p>Use variables in your template like this:</p>
                                        <code>Hello {{ user_name }}!</code><br>
                                        <code>Click here: {{ reset_link }}</code>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="template-form-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>
                                    Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               {% if not template or template.is_active %}checked{% endif %}>
                                        <label class="form-check-label" for="is_active">
                                            Active Template
                                        </label>
                                        <div class="form-text">Only active templates can be used</div>
                                    </div>
                                </div>
                                
                                {% if template and template.is_system_template %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    This is a system template. Some fields may be restricted.
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="template-form-card">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {{ 'Update' if template else 'Create' }} Template
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="previewTemplate()">
                                        <i class="fas fa-eye me-2"></i>
                                        Preview Template
                                    </button>
                                    <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-danger">
                                        <i class="fas fa-times me-2"></i>
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('templateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {};
    
    // Convert FormData to object
    for (let [key, value] of formData.entries()) {
        if (key.includes('[') && key.includes(']')) {
            // Handle nested objects like subjects[en]
            const match = key.match(/(\w+)\[(\w+)\]/);
            if (match) {
                const [, parentKey, childKey] = match;
                if (!data[parentKey]) data[parentKey] = {};
                data[parentKey][childKey] = value;
            }
        } else {
            data[key] = value;
        }
    }
    
    // Convert variables string to array
    if (data.variables) {
        data.variables = data.variables.split(',').map(v => v.trim()).filter(v => v);
    }
    
    // Submit form
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = "{{ url_for('admin.email_templates') }}";
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to save template');
    });
});

function addVariable(variable) {
    const input = document.getElementById('variables');
    const current = input.value.trim();
    const variables = current ? current.split(',').map(v => v.trim()) : [];
    
    if (!variables.includes(variable)) {
        variables.push(variable);
        input.value = variables.join(', ');
    }
}

function previewTemplate() {
    // Get current form data and show preview
    const subjects = {
        en: document.getElementById('subject_en').value,
        fa: document.getElementById('subject_fa').value
    };
    const htmlContent = {
        en: document.getElementById('html_content_en').value,
        fa: document.getElementById('html_content_fa').value
    };
    
    // Open preview window
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <html>
        <head>
            <title>Template Preview</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .preview-header { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .preview-content { border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="preview-header">
                <h3>Email Template Preview</h3>
                <p><strong>Subject (EN):</strong> ${subjects.en}</p>
                <p><strong>Subject (FA):</strong> ${subjects.fa}</p>
            </div>
            <div class="preview-content">
                <h4>English Version:</h4>
                ${htmlContent.en}
                <hr>
                <h4>Persian Version:</h4>
                <div dir="rtl">${htmlContent.fa}</div>
            </div>
        </body>
        </html>
    `);
}
</script>
{% endblock %}
