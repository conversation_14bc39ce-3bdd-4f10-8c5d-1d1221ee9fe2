import os
import sys
import argparse
from datetime import datetime, timedelta

# Add the project root directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.models import Post

def create_post(title, content, platform, status="draft", schedule_date=None, image=None):
    """Create a new post"""
    app = create_app()
    with app.app_context():
        # Determine status
        is_published = (status == "published")
        is_scheduled = (status == "scheduled")
        
        # Parse schedule date if provided
        scheduled_at = None
        if schedule_date:
            try:
                scheduled_at = datetime.fromisoformat(schedule_date)
            except ValueError:
                print(f"Invalid date format: {schedule_date}. Use ISO format (YYYY-MM-DDTHH:MM:SS)")
                return
        
        # Get a user for the post (first user in the database)
        from app.models import User
        user = User.objects.first()
        if not user:
            print("No users found in the database. Please create a user first.")
            return
        
        # Create post based on the actual model structure
        post = Post(
            user=user,  # Add user field
            content=content,
            status=status,  # Using status as a string field
            account=None,   # Set to None or get from user input if needed
            media_url=image,
            scheduled_at=scheduled_at,
            published_at=datetime.utcnow() if is_published else None,
            analytics_data={
                "likes": 0,
                "comments": 0,
                "shares": 0,
                "views": 0
            },
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Add title if your model supports it (as a custom field or in content)
        # If title is not in your model, we can prepend it to content
        if not hasattr(post, 'title'):
            post.content = f"{title}\n\n{content}"
        else:
            post.title = title
            
        # Add platform if your model supports it
        if hasattr(post, 'platform'):
            post.platform = platform
        elif hasattr(post, 'account'):
            # If platform is stored in account, you might need to fetch the account first
            # This is just a placeholder - adjust according to your model
            from app.models import Account
            account = Account.objects(platform=platform).first()
            if account:
                post.account = account
        
        post.save()
        
        print(f"Post created successfully with ID: {post.id}")
        return post.id

def update_post(post_id, title=None, content=None, platform=None, status=None, schedule_date=None, image=None):
    """Update an existing post"""
    app = create_app()
    with app.app_context():
        # Find post
        post = Post.objects(id=post_id).first()
        if not post:
            print(f"Post with ID {post_id} not found")
            return
        
        # Update fields if provided
        if content:
            post.content = content
            
        # Update title if provided and if model has title field
        if title:
            if hasattr(post, 'title'):
                post.title = title
            else:
                # If no title field, prepend to content
                current_content = post.content
                if '\n\n' in current_content:
                    current_content = current_content.split('\n\n', 1)[1]
                post.content = f"{title}\n\n{current_content}"
        
        # Update platform if provided and if model has platform field
        if platform:
            if hasattr(post, 'platform'):
                post.platform = platform
            elif hasattr(post, 'account'):
                # If platform is stored in account, you might need to fetch the account first
                from app.models import Account
                account = Account.objects(platform=platform).first()
                if account:
                    post.account = account
        
        # Update status if provided
        if status:
            if hasattr(post, 'status'):
                post.status = status
            else:
                # If using separate fields for status
                post.published = (status == "published")
                if hasattr(post, 'scheduled'):
                    post.scheduled = (status == "scheduled")
                
                # Update published_at if publishing
                if status == "published":
                    post.published_at = datetime.utcnow()
        
        # Update schedule date if provided
        if schedule_date:
            try:
                scheduled_time = datetime.fromisoformat(schedule_date)
                if hasattr(post, 'scheduled_at'):
                    post.scheduled_at = scheduled_time
                elif hasattr(post, 'publish_date'):
                    post.publish_date = scheduled_time
            except ValueError:
                print(f"Invalid date format: {schedule_date}. Use ISO format (YYYY-MM-DDTHH:MM:SS)")
                return
        
        # Update image if provided
        if image:
            if hasattr(post, 'image'):
                post.image = image
            elif hasattr(post, 'media_url'):
                post.media_url = image
        
        # Update timestamp
        post.updated_at = datetime.utcnow()
        
        # Save changes
        post.save()
        
        print(f"Post {post_id} updated successfully")

def delete_post(post_id):
    """Delete a post"""
    app = create_app()
    with app.app_context():
        # Find post
        post = Post.objects(id=post_id).first()
        if not post:
            print(f"Post with ID {post_id} not found")
            return
        
        # Delete post
        post.delete()
        
        print(f"Post {post_id} deleted successfully")

def list_posts(platform=None, status=None, limit=10):
    """List posts with optional filtering"""
    app = create_app()
    with app.app_context():
        # Build query
        query = {}
        
        # Add platform filter if provided
        if platform:
            if Post._fields.get('platform'):
                query['platform'] = platform
            elif Post._fields.get('account'):
                # If platform is stored in account, we need a different approach
                # This is just a placeholder - adjust according to your model
                from app.models import Account
                accounts = Account.objects(platform=platform)
                if accounts:
                    account_ids = [account.id for account in accounts]
                    query['account__in'] = account_ids
        
        # Add status filter if provided
        if status:
            if Post._fields.get('status'):
                query['status'] = status
            else:
                # If using separate fields for status
                if status == "published":
                    query['published'] = True
                    if Post._fields.get('scheduled'):
                        query['scheduled'] = False
                elif status == "scheduled":
                    if Post._fields.get('scheduled'):
                        query['scheduled'] = True
                elif status == "draft":
                    query['published'] = False
                    if Post._fields.get('scheduled'):
                        query['scheduled'] = False
        
        # Get posts
        posts = Post.objects(**query).order_by('-created_at').limit(limit)
        
        # Print posts
        if not posts:
            print("No posts found")
            return
        
        print(f"Found {len(posts)} posts:")
        print("-" * 80)
        for post in posts:
            # Determine status string
            if hasattr(post, 'status'):
                status_str = post.status
            else:
                status_str = "Published" if getattr(post, 'published', False) else "Scheduled" if getattr(post, 'scheduled', False) else "Draft"
            
            # Get title (either from title field or first line of content)
            if hasattr(post, 'title'):
                title = post.title
            else:
                title = post.content.split('\n')[0] if post.content else 'No title'
            
            # Get platform (either from platform field or account)
            if hasattr(post, 'platform'):
                platform = post.platform
            elif hasattr(post, 'account') and post.account:
                platform = post.account.platform if hasattr(post.account, 'platform') else 'Unknown'
            else:
                platform = 'Unknown'
            
            print(f"ID: {post.id}")
            print(f"Title: {title}")
            print(f"Platform: {platform}")
            print(f"Status: {status_str}")
            print(f"Created: {post.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Content: {post.content[:50]}..." if len(post.content) > 50 else post.content)
            print("-" * 80)

def publish_post(post_id):
    """Publish a draft or scheduled post immediately"""
    app = create_app()
    with app.app_context():
        # Find post
        post = Post.objects(id=post_id).first()
        if not post:
            print(f"Post with ID {post_id} not found")
            return
        
        # Update status
        if hasattr(post, 'status'):
            post.status = "published"
        else:
            post.published = True
            if hasattr(post, 'scheduled'):
                post.scheduled = False
        
        # Update publish date
        if hasattr(post, 'published_at'):
            post.published_at = datetime.utcnow()
        elif hasattr(post, 'publish_date'):
            post.publish_date = datetime.utcnow()
        
        post.updated_at = datetime.utcnow()
        
        # Save changes
        post.save()
        
        print(f"Post {post_id} published successfully")

def create_sample_posts(count=5):
    """Create sample posts for testing"""
    platforms = ["instagram", "telegram", "twitter", "blog"]
    statuses = ["published", "scheduled", "draft"]
    
    # Get a user for the posts
    app = create_app()
    with app.app_context():
        from app.models import User
        user = User.objects.first()
        if not user:
            print("No users found in the database. Please create a user first.")
            return
    
    for i in range(count):
        platform = platforms[i % len(platforms)]
        status = statuses[i % len(statuses)]
        
        title = f"Sample Post {i+1}"
        content = f"This is sample content for post {i+1}. Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        
        # For scheduled posts, set a future date
        schedule_date = None
        if status == "scheduled":
            schedule_date = (datetime.utcnow() + timedelta(days=i+1)).isoformat()
        
        create_post(
            title=title,
            content=content,
            platform=platform,
            status=status,
            schedule_date=schedule_date
        )
    
    print(f"Created {count} sample posts")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Post management script")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Create post command
    create_parser = subparsers.add_parser("create", help="Create a new post")
    create_parser.add_argument("--title", required=True, help="Post title")
    create_parser.add_argument("--content", required=True, help="Post content")
    create_parser.add_argument("--platform", required=True, choices=["instagram", "telegram", "twitter", "blog"], help="Platform")
    create_parser.add_argument("--status", default="draft", choices=["published", "scheduled", "draft"], help="Post status")
    create_parser.add_argument("--schedule", help="Schedule date (ISO format: YYYY-MM-DDTHH:MM:SS)")
    create_parser.add_argument("--image", help="Image URL or path")
    
    # Update post command
    update_parser = subparsers.add_parser("update", help="Update an existing post")
    update_parser.add_argument("post_id", help="Post ID to update")
    update_parser.add_argument("--title", help="New post title")
    update_parser.add_argument("--content", help="New post content")
    update_parser.add_argument("--platform", choices=["instagram", "telegram", "twitter", "blog"], help="New platform")
    update_parser.add_argument("--status", choices=["published", "scheduled", "draft"], help="New post status")
    update_parser.add_argument("--schedule", help="New schedule date (ISO format: YYYY-MM-DDTHH:MM:SS)")
    update_parser.add_argument("--image", help="New image URL or path")
    
    # Delete post command
    delete_parser = subparsers.add_parser("delete", help="Delete a post")
    delete_parser.add_argument("post_id", help="Post ID to delete")
    
    # List posts command
    list_parser = subparsers.add_parser("list", help="List posts")
    list_parser.add_argument("--platform", choices=["instagram", "telegram", "twitter", "blog"], help="Filter by platform")
    list_parser.add_argument("--status", choices=["published", "scheduled", "draft"], help="Filter by status")
    list_parser.add_argument("--limit", type=int, default=10, help="Maximum number of posts to list")
    
    # Publish post command
    publish_parser = subparsers.add_parser("publish", help="Publish a post")
    publish_parser.add_argument("post_id", help="Post ID to publish")
    
    # Create sample posts command
    sample_parser = subparsers.add_parser("create-samples", help="Create sample posts for testing")
    sample_parser.add_argument("--count", type=int, default=5, help="Number of sample posts to create")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Execute command
    if args.command == "create":
        create_post(
            title=args.title,
            content=args.content,
            platform=args.platform,
            status=args.status,
            schedule_date=args.schedule,
            image=args.image
        )
    elif args.command == "update":
        update_post(
            post_id=args.post_id,
            title=args.title,
            content=args.content,
            platform=args.platform,
            status=args.status,
            schedule_date=args.schedule,
            image=args.image
        )
    elif args.command == "delete":
        delete_post(args.post_id)
    elif args.command == "list":
        list_posts(
            platform=args.platform,
            status=args.status,
            limit=args.limit
        )
    elif args.command == "publish":
        publish_post(args.post_id)
    elif args.command == "create-samples":
        create_sample_posts(args.count)
    else:
        parser.print_help()






