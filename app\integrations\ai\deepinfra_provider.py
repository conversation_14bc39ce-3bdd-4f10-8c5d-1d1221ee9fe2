import os
import requests
from .base_ai_provider import BaseAIProvider

class DeepInfraProvider(BaseAIProvider):
    """
    Concrete implementation of BaseAIProvider for DeepInfra services.
    """

    def __init__(self, api_key=None, base_url="https://api.deepinfra.com/v1", cache=None):
        """
        Initialize DeepInfra provider with API key and base URL.
        
        :param api_key: The DeepInfra API key (optional, will use env var if not provided)
        :param base_url: The base URL for DeepInfra API
        :param cache: Optional cache mechanism
        """
        self.api_key = api_key or os.getenv("DEEPINFRA_API_KEY")
        super().__init__(base_url=base_url, cache=cache)
        self.supported_tasks = ["text_generation", "chat", "embeddings", "image_generation"]

    def authenticate(self):
        """
        Authenticate with DeepInfra API.
        For DeepInfra, we just need to store the API key.
        """
        if not self.api_key:
            import logging
            logging.warning("DeepInfra API key is not set. Some functionality may be limited.")
        self.auth_token = self.api_key

    def get_headers(self):
        """
        Get headers for API requests.
        """
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        return headers

    def make_request(self, endpoint: str, payload: dict) -> dict:
        """
        Make a request to the DeepInfra API.
        
        :param endpoint: The specific endpoint to call
        :param payload: The payload to send in the request
        :return: The raw response from the API
        """
        self.log_request(endpoint, payload)

        headers = self.get_headers()

        response = requests.post(
            url=self.base_url.rstrip("/") + "/" + endpoint.lstrip("/"),
            headers=headers,
            json=payload
        )

        if response.ok:
            response_data = response.json()
            self.cache_response(endpoint, response_data)
            return response_data
        else:
            self.handle_error(f"Error {response.status_code}: {response.text}")
            response.raise_for_status()

    def process_response(self, response: dict) -> dict:
        """
        Process the response from DeepInfra API to extract generated content.
        """
        # DeepInfra follows OpenAI-like response format for most endpoints
        if "choices" in response and len(response["choices"]) > 0:
            choice = response["choices"][0]
            
            # Handle chat completion response
            if "message" in choice and "content" in choice["message"]:
                return {"text": choice["message"]["content"], "raw_response": response}
            
            # Handle completion response
            elif "text" in choice:
                return {"text": choice["text"], "raw_response": response}
        
        # Handle embedding response
        elif "data" in response and len(response["data"]) > 0 and "embedding" in response["data"][0]:
            return {"embedding": response["data"][0]["embedding"], "raw_response": response}
        
        # Handle image generation response
        elif "images" in response and len(response["images"]) > 0:
            return {"images": response["images"], "raw_response": response}
        
        # Return the raw response if we can't extract in a standard way
        return {"text": str(response), "raw_response": response}

    def generate_text(self, prompt: str, model: str = "meta-llama/Llama-2-70b-chat-hf", max_tokens: int = 100) -> dict:
        """
        Generate text using DeepInfra's completion models.
        """
        endpoint = "completions"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "max_tokens": max_tokens,
            "temperature": 0.7
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}

    def chat_completion(self, messages: list, model: str = "meta-llama/Llama-2-70b-chat-hf", max_tokens: int = 150) -> dict:
        """
        Chat with DeepInfra's chat models.
        """
        endpoint = "chat/completions"
        
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": 0.7
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
    
    def embeddings(self, text: str, model: str = "thenlper/gte-large") -> dict:
        """
        Generate embeddings for text using DeepInfra's embedding models.
        """
        endpoint = "embeddings"
        
        payload = {
            "model": model,
            "input": text
        }
        
        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
    
    def generate_image(self, prompt: str, model: str = "stability-ai/sdxl", 
                      width: int = 1024, height: int = 1024, 
                      num_images: int = 1) -> dict:
        """
        Generate images using DeepInfra's image generation models.
        
        :param prompt: Text prompt describing the desired image
        :param model: Model ID to use for generation
        :param width: Width of the generated image
        :param height: Height of the generated image
        :param num_images: Number of images to generate
        :return: Dictionary containing generated images
        """
        endpoint = "inference/" + model
        
        payload = {
            "input": {
                "prompt": prompt,
                "width": width,
                "height": height,
                "num_outputs": num_images
            }
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}


