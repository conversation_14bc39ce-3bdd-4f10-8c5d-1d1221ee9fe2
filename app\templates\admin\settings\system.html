{% extends "admin/admin_layout.html" %}

{% block title %}تنظیمات سیستم - Rominext{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.settings-header {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #6f42c1;
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a32a3 0%, #4e2a8e 100%);
    transform: translateY(-1px);
}

.settings-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.system-feature {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.system-feature:hover {
    border-color: #6f42c1;
    box-shadow: 0 0.125rem 0.25rem rgba(111, 66, 193, 0.15);
}

.feature-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 1rem;
}

.limits-icon { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
.files-icon { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
.features-icon { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
.maintenance-icon { background: linear-gradient(135deg, #fd7e14 0%, #e8690b 100%); }
.debug-icon { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }

.file-size-display {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.form-range::-webkit-slider-thumb {
    background: #6f42c1;
}

.form-range::-moz-range-thumb {
    background: #6f42c1;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header settings-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-server me-2"></i>
                            تنظیمات سیستم
                        </h5>
                        <div>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-cogs me-1"></i>
                                پیکربندی
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.settings_system') }}">
                        <!-- User Limits Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-users me-2"></i>
                                محدودیت‌های کاربر
                            </h6>
                            
                            <div class="system-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon limits-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">محدودیت‌های کاربری</h6>
                                        <small class="text-muted">تعیین حداکثر تعداد پست‌ها و اکانت‌ها</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="max_posts_per_user" class="form-label">حداکثر پست هر کاربر: <span id="max_posts_value">{{ settings.get('max_posts_per_user', '1000') }}</span></label>
                                        <input type="range" class="form-range" id="max_posts_per_user" name="max_posts_per_user" 
                                               min="100" max="10000" step="100" value="{{ settings.get('max_posts_per_user', '1000') }}" 
                                               oninput="document.getElementById('max_posts_value').textContent = this.value">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="max_accounts_per_user" class="form-label">حداکثر اکانت هر کاربر: <span id="max_accounts_value">{{ settings.get('max_accounts_per_user', '10') }}</span></label>
                                        <input type="range" class="form-range" id="max_accounts_per_user" name="max_accounts_per_user" 
                                               min="1" max="50" value="{{ settings.get('max_accounts_per_user', '10') }}" 
                                               oninput="document.getElementById('max_accounts_value').textContent = this.value">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- File Management Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-file-upload me-2"></i>
                                مدیریت فایل
                            </h6>
                            
                            <div class="system-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon files-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">تنظیمات آپلود فایل</h6>
                                        <small class="text-muted">حداکثر اندازه و نوع فایل‌های مجاز</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="max_file_upload_size" class="form-label">حداکثر اندازه فایل</label>
                                        <select class="form-select" id="max_file_upload_size" name="max_file_upload_size" onchange="updateFileSizeDisplay()">
                                            <option value="1048576" {% if settings.get('max_file_upload_size') == '1048576' %}selected{% endif %}>1 MB</option>
                                            <option value="5242880" {% if settings.get('max_file_upload_size') == '5242880' %}selected{% endif %}>5 MB</option>
                                            <option value="10485760" {% if settings.get('max_file_upload_size', '10485760') == '10485760' %}selected{% endif %}>10 MB</option>
                                            <option value="20971520" {% if settings.get('max_file_upload_size') == '20971520' %}selected{% endif %}>20 MB</option>
                                            <option value="52428800" {% if settings.get('max_file_upload_size') == '52428800' %}selected{% endif %}>50 MB</option>
                                            <option value="104857600" {% if settings.get('max_file_upload_size') == '104857600' %}selected{% endif %}>100 MB</option>
                                        </select>
                                        <div class="file-size-display" id="file_size_display">
                                            {{ (settings.get('max_file_upload_size', '10485760') | int / 1048576) | round(1) }} MB
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="allowed_file_types" class="form-label">نوع فایل‌های مجاز</label>
                                        <input type="text" class="form-control" id="allowed_file_types" name="allowed_file_types" 
                                               value="{{ settings.get('allowed_file_types', 'jpg,jpeg,png,gif,mp4,mov') }}" 
                                               placeholder="jpg,jpeg,png,gif,mp4,mov">
                                        <div class="form-text">فرمت‌ها را با کاما جدا کنید</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Features Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-toggle-on me-2"></i>
                                ویژگی‌های سیستم
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="system-feature">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="feature-icon features-icon">
                                                <i class="fas fa-chart-bar"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">آنالیتیکس</h6>
                                                <small class="text-muted">تحلیل و گزارش‌گیری</small>
                                            </div>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_analytics" name="enable_analytics" 
                                                   {% if settings.get('enable_analytics', 'true') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="enable_analytics">
                                                فعال‌سازی آنالیتیکس
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="system-feature">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="feature-icon features-icon">
                                                <i class="fas fa-calendar-alt"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">زمان‌بندی</h6>
                                                <small class="text-muted">برنامه‌ریزی پست‌ها</small>
                                            </div>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_scheduling" name="enable_scheduling" 
                                                   {% if settings.get('enable_scheduling', 'true') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="enable_scheduling">
                                                فعال‌سازی زمان‌بندی
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="system-feature">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="feature-icon features-icon">
                                                <i class="fas fa-robot"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">تولید محتوا با AI</h6>
                                                <small class="text-muted">هوش مصنوعی</small>
                                            </div>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_ai_generation" name="enable_ai_generation" 
                                                   {% if settings.get('enable_ai_generation', 'true') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="enable_ai_generation">
                                                فعال‌سازی AI
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Maintenance & Backup Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-tools me-2"></i>
                                نگهداری و پشتیبان‌گیری
                            </h6>
                            
                            <div class="system-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon maintenance-icon">
                                        <i class="fas fa-archive"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">تنظیمات نگهداری</h6>
                                        <small class="text-muted">مدت زمان نگهداری فایل‌ها و لاگ‌ها</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="backup_retention_days" class="form-label">نگهداری پشتیبان (روز)</label>
                                        <select class="form-select" id="backup_retention_days" name="backup_retention_days">
                                            <option value="7" {% if settings.get('backup_retention_days') == '7' %}selected{% endif %}>7 روز</option>
                                            <option value="15" {% if settings.get('backup_retention_days') == '15' %}selected{% endif %}>15 روز</option>
                                            <option value="30" {% if settings.get('backup_retention_days', '30') == '30' %}selected{% endif %}>30 روز</option>
                                            <option value="60" {% if settings.get('backup_retention_days') == '60' %}selected{% endif %}>60 روز</option>
                                            <option value="90" {% if settings.get('backup_retention_days') == '90' %}selected{% endif %}>90 روز</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="log_retention_days" class="form-label">نگهداری لاگ (روز)</label>
                                        <select class="form-select" id="log_retention_days" name="log_retention_days">
                                            <option value="30" {% if settings.get('log_retention_days') == '30' %}selected{% endif %}>30 روز</option>
                                            <option value="60" {% if settings.get('log_retention_days') == '60' %}selected{% endif %}>60 روز</option>
                                            <option value="90" {% if settings.get('log_retention_days', '90') == '90' %}selected{% endif %}>90 روز</option>
                                            <option value="180" {% if settings.get('log_retention_days') == '180' %}selected{% endif %}>180 روز</option>
                                            <option value="365" {% if settings.get('log_retention_days') == '365' %}selected{% endif %}>365 روز</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Debug Settings Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-bug me-2"></i>
                                تنظیمات توسعه
                            </h6>
                            
                            <div class="system-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon debug-icon">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">حالت توسعه</h6>
                                        <small class="text-muted">فعال‌سازی حالت دیباگ (فقط برای توسعه)</small>
                                    </div>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="debug_mode" name="debug_mode" 
                                           {% if settings.get('debug_mode') == 'true' %}checked{% endif %}>
                                    <label class="form-check-label" for="debug_mode">
                                        فعال‌سازی حالت دیباگ
                                    </label>
                                    <div class="form-text text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        توجه: این گزینه را در محیط تولید فعال نکنید
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    ذخیره تنظیمات
                                </button>
                                <button type="reset" class="btn btn-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    بازنشانی
                                </button>
                            </div>
                            <div>
                                <a href="{{ url_for('admin.settings_database') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    تنظیمات پایگاه داده
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateFileSizeDisplay() {
    const select = document.getElementById('max_file_upload_size');
    const display = document.getElementById('file_size_display');
    const bytes = parseInt(select.value);
    const mb = (bytes / 1048576).toFixed(1);
    display.textContent = mb + ' MB';
}
</script>
{% endblock %}
