import pytest
from datetime import datetime, timedelta
from app.models import Subscription, User, SubscriptionStatus

class TestSubscriptionModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    def test_create_subscription(self, user):
        """Test creating a subscription"""
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=30)
        
        subscription = Subscription(
            user=user,
            plan_name="Pro Plan",
            status=SubscriptionStatus.ACTIVE,
            start_date=start_date,
            end_date=end_date,
            price=29.99,
            features=["unlimited_posts", "analytics", "auto_scheduling"]
        )
        subscription.save()
        
        assert subscription.user == user
        assert subscription.plan_name == "Pro Plan"
        assert subscription.status == SubscriptionStatus.ACTIVE
        assert subscription.start_date == start_date
        assert subscription.end_date == end_date
        assert subscription.price == 29.99
        assert "analytics" in subscription.features
    
    def test_subscription_status(self, user):
        """Test subscription status changes"""
        subscription = Subscription(
            user=user,
            plan_name="Basic Plan",
            status=SubscriptionStatus.ACTIVE,
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow() + timedelta(days=30),
            price=9.99
        )
        subscription.save()
        
        # Change status to canceled
        subscription.status = SubscriptionStatus.CANCELED
        subscription.save()
        
        assert subscription.status == SubscriptionStatus.CANCELED
        
        # Change status to expired
        subscription.status = SubscriptionStatus.EXPIRED
        subscription.save()
        
        assert subscription.status == SubscriptionStatus.EXPIRED
    
    def test_is_active(self, user):
        """Test subscription active check"""
        # Active subscription with future end date
        active_sub = Subscription(
            user=user,
            plan_name="Pro Plan",
            status=SubscriptionStatus.ACTIVE,
            start_date=datetime.utcnow() - timedelta(days=5),
            end_date=datetime.utcnow() + timedelta(days=25),
            price=29.99
        )
        active_sub.save()
        
        # Expired subscription
        expired_sub = Subscription(
            user=user,
            plan_name="Basic Plan",
            status=SubscriptionStatus.EXPIRED,
            start_date=datetime.utcnow() - timedelta(days=60),
            end_date=datetime.utcnow() - timedelta(days=30),
            price=9.99
        )
        expired_sub.save()
        
        # Assuming there's an is_active method or property
        if hasattr(Subscription, 'is_active'):
            assert active_sub.is_active() is True
            assert expired_sub.is_active() is False