from mongoengine import Document, <PERSON><PERSON><PERSON>, StringField, DateTimeField
from datetime import datetime
from .user import User

class ContentTemplate(Document):
    user = ReferenceField(User)  # Null for system templates
    title = StringField(required=True)
    description = StringField()
    template_body = StringField(required=True)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'content_templates',
        'indexes': ['user', 'title']
    }