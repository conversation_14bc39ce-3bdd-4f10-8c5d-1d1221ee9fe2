from mongoengine import Document, ReferenceField, IntField, DateTimeField, FloatField
from datetime import datetime
from .post import Post

class Analytics(Document):
    """Store analytics data for posts"""
    post = ReferenceField(Post, required=True)
    views_count = IntField(default=0)
    clicks_count = IntField(default=0)
    likes_count = IntField(default=0)
    comments_count = IntField(default=0)
    shares_count = IntField(default=0)
    engagement_rate = FloatField()  # New field for advanced metrics
    collected_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'analytics',
        'indexes': ['post', 'collected_at']
    }
