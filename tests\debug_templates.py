#!/usr/bin/env python3
"""
Debug script to test email templates rendering
"""
import os
import sys

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_template_rendering():
    """Test template rendering directly"""
    print("🔍 Testing Email Template Rendering")
    print("=" * 50)
    
    try:
        from app import create_app
        from app.services.email_template_service import EmailTemplateService
        from flask import render_template_string
        
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            print("✅ Flask app context created")
            
            # Test basic template rendering
            try:
                simple_template = """
                <h1>Test Template</h1>
                <p>Templates count: {{ templates|length if templates else 'None' }}</p>
                <ul>
                {% if templates %}
                    {% for template in templates %}
                    <li>{{ template.name }} ({{ template.template_key }})</li>
                    {% endfor %}
                {% else %}
                    <li>No templates found</li>
                {% endif %}
                </ul>
                """
                
                # Get templates
                templates = EmailTemplateService.list_templates()
                print(f"📊 Found {len(templates)} templates")
                
                # Render template
                rendered = render_template_string(simple_template, templates=templates)
                print("✅ Template rendering successful")
                print("\n📄 Rendered HTML:")
                print("-" * 30)
                print(rendered)
                print("-" * 30)
                
                return True
                
            except Exception as render_error:
                print(f"❌ Template rendering failed: {str(render_error)}")
                return False
                
    except Exception as e:
        print(f"❌ App context error: {str(e)}")
        return False

def test_admin_layout():
    """Test admin layout inheritance"""
    print("\n🏗️ Testing Admin Layout Inheritance")
    print("=" * 50)
    
    try:
        from app import create_app
        from flask import render_template_string
        
        app = create_app()
        
        with app.app_context():
            # Test simple admin template
            admin_template = """
            {% extends "admin/admin_layout.html" %}
            {% block title %}Test Page{% endblock %}
            {% block admin_content %}
            <div class="container-fluid">
                <h1>Test Admin Page</h1>
                <p>This is a test of admin layout inheritance.</p>
                <div class="alert alert-success">
                    Admin layout is working!
                </div>
            </div>
            {% endblock %}
            """
            
            try:
                rendered = render_template_string(admin_template)
                print("✅ Admin layout inheritance successful")
                print(f"📏 Rendered HTML length: {len(rendered)} characters")
                
                # Check for key elements
                if 'admin_content' in rendered or 'container-fluid' in rendered:
                    print("✅ Admin content block found")
                else:
                    print("⚠️  Admin content block might not be rendering")
                
                return True
                
            except Exception as render_error:
                print(f"❌ Admin layout rendering failed: {str(render_error)}")
                return False
                
    except Exception as e:
        print(f"❌ Admin layout test error: {str(e)}")
        return False

def test_translation_function():
    """Test translation function availability"""
    print("\n🌐 Testing Translation Function")
    print("=" * 50)
    
    try:
        from app import create_app
        from flask import render_template_string
        
        app = create_app()
        
        with app.app_context():
            # Test translation function
            translation_template = """
            <h1>Translation Test</h1>
            <p>Translation function available: {{ 't' in globals() }}</p>
            <p>Test translation: {{ t('common.save') if 't' in globals() else 'Translation function not available' }}</p>
            """
            
            try:
                rendered = render_template_string(translation_template)
                print("✅ Translation test successful")
                print("\n📄 Translation test result:")
                print("-" * 30)
                print(rendered)
                print("-" * 30)
                
                return True
                
            except Exception as render_error:
                print(f"❌ Translation test failed: {str(render_error)}")
                return False
                
    except Exception as e:
        print(f"❌ Translation test error: {str(e)}")
        return False

def test_email_routes():
    """Test email routes accessibility"""
    print("\n🛣️ Testing Email Routes")
    print("=" * 50)
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # Test routes without authentication first
            routes_to_test = [
                '/admin/email/debug',
                '/admin/email/templates-test'
            ]
            
            for route in routes_to_test:
                try:
                    response = client.get(route)
                    print(f"📍 Route {route}: Status {response.status_code}")
                    
                    if response.status_code == 302:
                        print(f"   → Redirected to: {response.location}")
                    elif response.status_code == 200:
                        print(f"   ✅ Route accessible")
                    else:
                        print(f"   ⚠️  Unexpected status code")
                        
                except Exception as route_error:
                    print(f"   ❌ Route error: {str(route_error)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Route testing error: {str(e)}")
        return False

def main():
    """Main debug function"""
    print("🚀 Rominext Email Templates Debug")
    print("=" * 50)
    
    tests = [
        ("Template Rendering", test_template_rendering),
        ("Admin Layout", test_admin_layout),
        ("Translation Function", test_translation_function),
        ("Email Routes", test_email_routes)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} test passed")
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The issue might be with authentication or specific template files.")
    else:
        print("⚠️  Some tests failed. Check the error messages above for clues.")
    
    print("\n💡 Next steps:")
    print("1. Run this script: python debug_templates.py")
    print("2. Check the test results above")
    print("3. If templates render correctly here, the issue is likely authentication-related")
    print("4. Try accessing /admin/email/templates-test directly after logging in")

if __name__ == "__main__":
    main()
