"""
Advanced File Upload Blueprint

This blueprint provides comprehensive file upload endpoints with support for:
- Single and batch file uploads
- Progress tracking
- File validation and processing
- Platform optimization
- Upload management
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
import json
import logging
from typing import List, Dict, Any

from ..services.asset_service import AssetService
from ..models.asset import AssetType
from ..utils.file_upload_helper import FileUploadHelper
from ..utils.upload_config import get_upload_config

logger = logging.getLogger(__name__)

upload_bp = Blueprint('upload', __name__, url_prefix='/api/upload')


@upload_bp.route('/single', methods=['POST'])
@login_required
def upload_single_file():
    """
    Upload a single file
    
    Form data:
    - file: The file to upload
    - file_type: Type of file (image, video, document, audio)
    - folder: Optional subfolder
    - process_file: Whether to process the file (default: true)
    - metadata: Optional JSON metadata
    """
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Get parameters
        file_type = request.form.get('file_type')
        if not file_type:
            return jsonify({
                'success': False,
                'error': 'file_type is required'
            }), 400
        
        # Validate file type
        if file_type not in [t.value for t in AssetType]:
            return jsonify({
                'success': False,
                'error': f'Invalid file type. Must be one of: {", ".join([t.value for t in AssetType])}'
            }), 400
        
        folder = request.form.get('folder')
        process_file = request.form.get('process_file', 'true').lower() == 'true'
        
        # Parse metadata if provided
        metadata = {}
        if 'metadata' in request.form:
            try:
                metadata = json.loads(request.form.get('metadata', '{}'))
            except json.JSONDecodeError:
                return jsonify({
                    'success': False,
                    'error': 'Invalid metadata format'
                }), 400
        
        # Upload asset
        asset = AssetService.upload_asset(
            user_id=str(current_user.id),
            file=file,
            file_type=file_type,
            metadata=metadata,
            process_file=process_file,
            folder=folder
        )
        
        return jsonify({
            'success': True,
            'asset': {
                'id': str(asset.id),
                'file_url': asset.file_url,
                'file_type': asset.file_type,
                'metadata': asset.metadata,
                'created_at': asset.created_at.isoformat()
            }
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        logger.error(f"Upload failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Upload failed'
        }), 500


@upload_bp.route('/batch', methods=['POST'])
@login_required
def upload_batch_files():
    """
    Upload multiple files in batch
    
    Form data:
    - files: Multiple files to upload
    - file_type: Type of files (image, video, document, audio)
    - folder: Optional subfolder
    - process_files: Whether to process the files (default: true)
    - metadata: Optional JSON metadata for all files
    """
    try:
        # Check if files are present
        if 'files' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No files provided'
            }), 400
        
        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({
                'success': False,
                'error': 'No files selected'
            }), 400
        
        # Get parameters
        file_type = request.form.get('file_type')
        if not file_type:
            return jsonify({
                'success': False,
                'error': 'file_type is required'
            }), 400
        
        # Validate file type
        if file_type not in [t.value for t in AssetType]:
            return jsonify({
                'success': False,
                'error': f'Invalid file type. Must be one of: {", ".join([t.value for t in AssetType])}'
            }), 400
        
        folder = request.form.get('folder')
        process_files = request.form.get('process_files', 'true').lower() == 'true'
        
        # Parse metadata if provided
        metadata = {}
        if 'metadata' in request.form:
            try:
                metadata = json.loads(request.form.get('metadata', '{}'))
            except json.JSONDecodeError:
                return jsonify({
                    'success': False,
                    'error': 'Invalid metadata format'
                }), 400
        
        # Upload assets
        assets = AssetService.upload_multiple_assets(
            user_id=str(current_user.id),
            files=files,
            file_type=file_type,
            metadata=metadata,
            process_files=process_files,
            folder=folder
        )
        
        return jsonify({
            'success': True,
            'assets': [{
                'id': str(asset.id),
                'file_url': asset.file_url,
                'file_type': asset.file_type,
                'metadata': asset.metadata,
                'created_at': asset.created_at.isoformat()
            } for asset in assets],
            'uploaded_count': len(assets),
            'total_files': len(files)
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        logger.error(f"Batch upload failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Batch upload failed'
        }), 500


@upload_bp.route('/optimize/<asset_id>', methods=['POST'])
@login_required
def optimize_for_platform(asset_id: str):
    """
    Optimize an asset for a specific social media platform
    
    JSON body:
    - platform: Target platform (instagram, facebook, twitter, linkedin)
    - content_type: Type of content (post, story, cover, etc.)
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'JSON data required'
            }), 400
        
        platform = data.get('platform')
        content_type = data.get('content_type', 'post')
        
        if not platform:
            return jsonify({
                'success': False,
                'error': 'platform is required'
            }), 400
        
        # Optimize asset
        optimized_url = AssetService.optimize_asset_for_platform(
            asset_id=asset_id,
            platform=platform,
            content_type=content_type
        )
        
        if optimized_url:
            return jsonify({
                'success': True,
                'optimized_url': optimized_url,
                'platform': platform,
                'content_type': content_type
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Optimization failed'
            }), 400
        
    except Exception as e:
        logger.error(f"Platform optimization failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Optimization failed'
        }), 500


@upload_bp.route('/config', methods=['GET'])
@login_required
def get_upload_config_endpoint():
    """Get current upload configuration"""
    try:
        config = get_upload_config()
        
        # Return safe configuration (no sensitive data)
        safe_config = {
            'max_file_size': config.max_file_size,
            'max_image_size': config.max_image_size,
            'max_video_size': config.max_video_size,
            'max_document_size': config.max_document_size,
            'allowed_image_extensions': config.allowed_image_extensions,
            'allowed_video_extensions': config.allowed_video_extensions,
            'allowed_document_extensions': config.allowed_document_extensions,
            'allowed_audio_extensions': config.allowed_audio_extensions,
            'create_thumbnails': config.create_thumbnails,
            'thumbnail_sizes': config.thumbnail_sizes,
            'image_quality': config.image_quality,
            'storage_backend': config.storage_backend.value,
            'chunk_size': config.chunk_size
        }
        
        return jsonify({
            'success': True,
            'config': safe_config
        })
        
    except Exception as e:
        logger.error(f"Failed to get upload config: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get configuration'
        }), 500


@upload_bp.route('/validate', methods=['POST'])
@login_required
def validate_file():
    """
    Validate a file without uploading it
    
    Form data:
    - file: The file to validate
    """
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Get upload helper and validate
        config = get_upload_config()
        upload_helper = FileUploadHelper(config.__dict__)
        
        is_valid, error_msg, metadata = upload_helper.validate_file(file)
        
        if is_valid:
            return jsonify({
                'success': True,
                'valid': True,
                'metadata': {
                    'original_filename': metadata.original_filename,
                    'file_size': metadata.file_size,
                    'mime_type': metadata.mime_type,
                    'file_type': metadata.file_type.value,
                    'file_extension': metadata.file_extension,
                    'dimensions': metadata.dimensions,
                    'duration': metadata.duration
                }
            })
        else:
            return jsonify({
                'success': True,
                'valid': False,
                'error': error_msg,
                'metadata': {
                    'original_filename': metadata.original_filename if metadata else None,
                    'file_size': metadata.file_size if metadata else None
                }
            })
        
    except Exception as e:
        logger.error(f"File validation failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Validation failed'
        }), 500


@upload_bp.route('/test', methods=['GET'])
@login_required
def upload_test_page():
    """Test page for upload functionality"""
    from flask import render_template
    return render_template('upload/upload_test.html')
