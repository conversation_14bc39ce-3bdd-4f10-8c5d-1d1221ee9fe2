import pytest
from datetime import datetime
from app.models import Post, User, PostStatus

class TestPostModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    def test_create_post(self, user):
        """Test creating a post"""
        post = Post(
            user=user,
            content="Test post content",
            status=PostStatus.DRAFT
        )
        post.save()
        
        assert post.user == user
        assert post.content == "Test post content"
        assert post.status == PostStatus.DRAFT
        assert post.created_at is not None
        
    def test_post_status_enum(self, user):
        """Test post status enum validation"""
        post = Post(
            user=user,
            content="Test content",
            status=PostStatus.DRAFT
        )
        post.save()
        
        # Change status to published
        post.status = PostStatus.PUBLISHED
        post.save()
        
        assert post.status == PostStatus.PUBLISHED