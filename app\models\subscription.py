from mongoengine import Document, <PERSON>Field, StringField, DateTimeField, IntField, EnumField
from datetime import datetime
from enum import Enum
from .user import User

class SubscriptionStatus(str, Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELED = "canceled"

class Subscription(Document):
    user = ReferenceField(User, required=True)
    plan_name = StringField(required=True)
    status = EnumField(SubscriptionStatus, default=SubscriptionStatus.ACTIVE)
    start_date = DateTimeField(required=True)
    end_date = DateTimeField(required=True)
    posts_limit_per_month = IntField(default=100)
    accounts_limit = IntField(default=5)  # Changed from channels_limit
    agents_limit = IntField(default=3)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'subscriptions',
        'indexes': ['user', 'status']
    }
