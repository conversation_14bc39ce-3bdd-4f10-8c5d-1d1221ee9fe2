{% extends "main_layout.html" %}

{% block title %}{{ t('pricing.title') }} - Rominext{% endblock %}

{% block head_extra %}
    <!-- Custom styles for pricing page only -->
    <style>
        /* Base styles for pricing content only */
        .pricing-section {
            background: transparent; /* Remove white background */
            padding: 3rem 0;
            overflow-x: hidden; /* Prevent horizontal scrolling */
            position: relative; /* For background patterns */
        }

        /* Add subtle background patterns from about page */
        .pricing-section:before {
            content: "";
            position: fixed;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(40, 167, 69, 0.05) 0%, transparent 70%);
            z-index: -1;
        }

        .pricing-section:after {
            content: "";
            position: fixed;
            bottom: 0;
            left: 0;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(40, 167, 69, 0.05) 0%, transparent 70%);
            z-index: -1;
        }

        /* RTL specific fixes */
        html[dir="rtl"] .pricing-section .container {
            padding-right: 15px;
            padding-left: 15px;
        }
        
        html[dir="rtl"] .feature-list {
            text-align: right;
            padding-right: 0;
        }
        
        html[dir="rtl"] .pricing-decoration.decoration-1 {
            right: -150px;
            left: auto;
        }
        
        html[dir="rtl"] .pricing-decoration.decoration-2 {
            left: -150px;
            right: auto;
        }
        
        /* Pricing card styles */
        .pricing-card {
            transition: all 0.3s ease;
            border-radius: 16px;
            overflow: hidden;
            background: white;
            position: relative;
            box-shadow: 0 8px 16px rgba(0,0,0,0.08);
            height: 100%;
            text-align: center;
        }

        
        
        .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        /* Popular plan highlight */
        .popular-badge {
            position: absolute;
            top: 0;
            right: 2rem;
            transform: translateY(-50%);
            background: #F59E0B;
            color: white;
            padding: 0.25rem 1rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
        }
        
        /* Plan header */
        .plan-header {
            padding: 2rem;
            position: relative;
            overflow: hidden;
            text-align: center;
        }
        
        .plan-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
        }
        
        /* Feature list */
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
            font-size: 0.9rem;
            text-align: right;
        }
        
        .feature-list li {
            position: relative;
            padding-right: 1.75rem;
            margin-bottom: 0.75rem;
        }
        
        .feature-list li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #10B981;
            font-weight: bold;
        }
        
        .feature-list .feature-disabled {
            color: #9CA3AF;
        }
        
        .feature-list .feature-disabled::before {
            content: '×';
            color: #EF4444;
        }
        
        /* Price styling */
        .price-amount {
            font-size: 2.5rem;
            font-weight: 800;
            line-height: 1;
        }
        
        /* Button styles */
        .btn-primary {
            background-color: #4F46E5;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.2s;
            display: block;
            width: 100%;
            text-align: center;
            text-decoration: none;
        }
        
        .btn-primary:hover {
            background-color: #4338CA;
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background-color: transparent;
            color: #4B5563;
            border: 2px solid #E5E7EB;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.2s;
            display: block;
            width: 100%;
            text-align: center;
            text-decoration: none;
        }
        
        .btn-secondary:hover {
            border-color: #D1D5DB;
            color: #1F2937;
            text-decoration: none;
        }
        
        /* FAQ section styles */
        .faq-section {
            background-color: #f8f9fa;
            padding: 4rem 0;
        }
        
        .faq-item {
            margin-bottom: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 1.5rem;
        }
        
        .faq-question {
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 0.5rem;
            position: relative;
            cursor: pointer;
        }
        
        .faq-answer {
            color: #4a5568;
        }
        
        /* Feature icons */
        .feature-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            margin-left: 8px;
            color: #4F46E5;
        }
        
        /* Intro section */
        .pricing-intro {
            max-width: 700px;
            margin: 0 auto 4rem auto;
            text-align: center;
        }
        
        .pricing-intro p {
            color: #4B5563;
            font-size: 1.1rem;
        }
        
        /* Decorative elements */
        .pricing-decoration {
            position: absolute;
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(79, 70, 229, 0.05) 100%);
            border-radius: 50%;
            z-index: -1;
        }
        
        .decoration-1 {
            top: -150px;
            left: -150px;
        }
        
        .decoration-2 {
            bottom: -150px;
            right: -150px;
        }

        .pricing-card .p-4 {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .pricing-card .feature-list {
            flex-grow: 1;
            margin-bottom: 1.5rem !important;
        }

        .pricing-card .btn-primary {
            margin-top: auto;
        }

        /* Fix for button visibility in pricing cards */
        .pricing-card .btn-primary {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            margin-top: 1rem !important;
            background-color: #4F46E5 !important;
            color: white !important;
            border: none !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            font-weight: 600 !important;
            transition: all 0.2s !important;
            text-align: center !important;
            text-decoration: none !important;
            z-index: 10 !important;
            position: relative !important;
        }
        
        .pricing-card .btn-primary:hover {
            background-color: #4338CA !important;
            transform: translateY(-2px) !important;
            color: white !important;
            text-decoration: none !important;
        }
        
        /* Ensure the button container is visible */
        .pricing-card .p-4 {
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
            position: relative !important;
            z-index: 5 !important;
        }
        
        /* Push button to bottom of card */
        .pricing-card .feature-list {
            flex-grow: 1 !important;
            margin-bottom: 1.5rem !important;
        }
    </style>
{% endblock %}

{% block content %}
<div class="pricing-section py-5">
    <div class="container position-relative">
        <!-- Decorative elements -->
        <div class="pricing-decoration decoration-1"></div>
        <div class="pricing-decoration decoration-2"></div>
        
        <div class="pricing-intro">
            <h1 class="display-4 fw-bold mb-4">طرح‌های قیمت‌گذاری</h1>
        </div>
        
        <!-- Key benefits points (RTL layout) -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="key-benefits p-4 rounded-4 bg-light">
                    <div class="row text-right">
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle text-primary ms-3"></i>
                                <p class="mb-0"> قبل از انتخاب پلن، لطفا<lemma> به نکات زیر توجه فرمایید:</p>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle text-primary ms-3"></i>
                                <p class="mb-0">هر پلن فقط شامل تعداد معینی پروژه و کاربر فعال است؛ در صورت نیاز به ظرفیت بیشتر، امکان ارتقاء وجود دارد.</p>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-credit-card text-primary ms-3"></i>
                                <p class="mb-0">هزینه‌ها به صورت ماهیانه محاسبه شده و پرداخت می‌شوند، مگر در صورت انتخاب پلن سالانه با تخفیف ویژه.</p>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-times-circle text-primary ms-3"></i>
                                <p class="mb-0">امکان لغو اشتراک در هر زمان وجود دارد، اما مبالغ پرداخت‌شده قابل بازگشت نمی‌باشند.</p>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-percentage text-primary ms-3"></i>
                                <p class="mb-0">قیمت‌ها بدون احتساب مالیات بر ارزش افزوده (VAT) ارائه شده‌اند.</p>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-headset text-primary ms-3"></i>
                                <p class="mb-0">پشتیبانی فنی برای همه پلن‌ها فعال است، اما سطح خدمات بسته به نوع پلن متفاوت می‌باشد.</p>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-puzzle-piece text-primary ms-3"></i>
                                <p class="mb-0">استفاده از امکانات پیشرفته (مثل اتصال به شبکه‌های بیشتر یا استفاده از مدل‌های اختصاصی) ممکن است شامل هزینه‌های اضافی باشد.</p>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-sync-alt text-primary ms-3"></i>
                                <p class="mb-0">رومینکست این حق را دارد که در صورت نیاز، قیمت‌ها و شرایط را به‌روزرسانی نماید؛ تغییرات از دوره پرداخت بعدی اعمال خواهند شد.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
            <!-- Free Plan -->
            <div class="col">
                <div class="pricing-card h-100">
                    <div class="plan-header">
                        <div class="mb-3">
                            <i class="fas fa-rocket feature-icon fa-2x text-primary"></i>
                        </div>
                        <h3 class="fw-bold mb-2">رایگان</h3>
                        <p class="text-muted small">برای شروع و تست قابلیت‌ها</p>
                        <div class="mt-4">
                            <span class="price-amount">۰</span>
                            <span class="text-muted">/همیشه رایگان</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <ul class="feature-list mb-4">
                            <li>۲۰ محتوای ماهانه</li>
                            <li>انتشار در ۱ شبکه اجتماعی</li>
                            <li>۱ Agent پایه</li>
                            <li>تحلیل‌های ابتدایی</li>
                            <li class="feature-disabled">بدون زمان‌بندی هوشمند</li>
                            <li class="feature-disabled">بدون پاسخ خودکار</li>
                        </ul>
                        <a href="{{ url_for('auth.register') }}?plan=free" class="btn btn-primary w-100">
                            شروع رایگان
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Pro Plan -->
            <div class="col">
                <div class="pricing-card h-100 border-2 border-indigo-100">
                    <div class="plan-header bg-indigo-50">
                        <div class="mb-3">
                            <i class="fas fa-crown feature-icon fa-2x text-warning"></i>
                        </div>
                        <h3 class="fw-bold mb-2">حرفه‌ای</h3>
                        <p class="text-muted small">مناسب برای فریلنسرها و پیج‌های فعال</p>
                        <div class="mt-4">
                            <span class="price-amount text-indigo-600">۲۹</span>
                            <span class="text-muted">/ماهانه</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <ul class="feature-list mb-4">
                            <li>۲۰۰ محتوای ماهانه</li>
                            <li>۳ پلتفرم اجتماعی</li>
                            <li>۳ Agent قابل تنظیم</li>
                            <li>زمان‌بندی پیشرفته</li>
                            <li>پاسخ هوشمند به پیام‌ها</li>
                            <li>گزارش‌گیری حرفه‌ای</li>
                        </ul>
                        <a href="{{ url_for('auth.register') }}?plan=pro" class="btn btn-primary w-100">
                            خرید اشتراک
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Team Plan -->
            <div class="col">
                <div class="pricing-card h-100">
                    <div class="plan-header">
                        <div class="mb-3">
                            <i class="fas fa-users feature-icon fa-2x text-info"></i>
                        </div>
                        <h3 class="fw-bold mb-2">تیمی</h3>
                        <p class="text-muted small">برای تیم‌های تولید محتوا و آژانس‌ها</p>
                        <div class="mt-4">
                            <span class="price-amount">۹۹</span>
                            <span class="text-muted">/ماهانه</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <ul class="feature-list mb-4">
                            <li>محتوای نامحدود</li>
                            <li>۱۰+ شبکه اجتماعی</li>
                            <li>۱۰ Agent پیشرفته</li>
                            <li>تعریف تیم و نقش‌ها</li>
                            <li>داشبورد همکاری تیمی</li>
                            <li>API اختصاصی</li>
                        </ul>
                        <a href="{{ url_for('auth.register') }}?plan=business" class="btn btn-primary w-100">
                            خرید اشتراک تیمی
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Enterprise Plan -->
            <div class="col">
                <div class="pricing-card h-100">
                    <div class="plan-header">
                        <div class="mb-3">
                            <i class="fas fa-building feature-icon fa-2x text-danger"></i>
                        </div>
                        <h3 class="fw-bold mb-2">سازمانی</h3>
                        <p class="text-muted small">برای شرکت‌ها و سازمان‌های بزرگ</p>
                        <div class="mt-4">
                            <span class="price-amount" style="font-size: 1.5rem;">تماس بگیرید</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <ul class="feature-list mb-4">
                            <li>همه امکانات طرح تیمی</li>
                            <li>پشتیبانی اختصاصی ۲۴/۷</li>
                            <li>آموزش اختصاصی</li>
                            <li>راه‌اندازی اختصاصی</li>
                            <li>SLA تضمین شده</li>
                            <li>امکانات سفارشی</li>
                        </ul>
                        <a href="{{ url_for('landing.about') }}" class="btn btn-primary w-100">
                            درخواست مشاوره
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="faq-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">سوالات متداول</h2>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="faq-item">
                    <h4 class="faq-question">آیا می‌توانم طرح خود را ارتقا دهم؟</h4>
                    <p class="faq-answer">بله، شما می‌توانید در هر زمان طرح خود را ارتقا دهید. تغییرات از دوره صورتحساب بعدی اعمال خواهد شد.</p>
                </div>
                
                <div class="faq-item">
                    <h4 class="faq-question">آیا امکان لغو اشتراک وجود دارد؟</h4>
                    <p class="faq-answer">بله، شما می‌توانید در هر زمان اشتراک خود را لغو کنید. پس از لغو، اشتراک شما تا پایان دوره پرداخت فعلی ادامه خواهد داشت.</p>
                </div>
                
                <div class="faq-item">
                    <h4 class="faq-question">منظور از محتوای ماهانه چیست؟</h4>
                    <p class="faq-answer">محتوای ماهانه به تعداد پست‌هایی اشاره دارد که می‌توانید با استفاده از پلتفرم ما در ماه ایجاد و منتشر کنید.</p>
                </div>
                
                <div class="faq-item">
                    <h4 class="faq-question">آیا پشتیبانی فنی در همه طرح‌ها ارائه می‌شود؟</h4>
                    <p class="faq-answer">بله، همه کاربران به پشتیبانی فنی پایه دسترسی دارند. اما طرح‌های حرفه‌ای، تیمی و سازمانی از پشتیبانی اختصاصی با اولویت بالاتر بهره‌مند می‌شوند.</p>
                </div>
                
                <div class="faq-item">
                    <h4 class="faq-question">آیا می‌توانم قبل از خرید، پلتفرم را تست کنم؟</h4>
                    <p class="faq-answer">بله، طرح رایگان ما به شما امکان می‌دهد تا قابلیت‌های اصلی پلتفرم را بدون هیچ هزینه‌ای تست کنید.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
