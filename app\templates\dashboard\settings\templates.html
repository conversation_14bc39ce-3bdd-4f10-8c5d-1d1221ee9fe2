{% extends "dashboard/dashboard_layout.html" %}

{% block title %}Response Templates - Rominext{% endblock %}

{% block dashboard_content %}
<div class="row mt-4">
    <div class="col-12">
        <h2>Response Templates</h2>
        
        <div class="card mb-4">
            <div class="card-header">Add New Template</div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="template" class="form-label">Template Text</label>
                        <textarea class="form-control" id="template" name="template" rows="3" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Template</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">Your Templates</div>
            <div class="card-body">
                {% if templates and templates|length > 0 %}
                    <div class="list-group">
                        {% for template in templates %}
                            <div class="list-group-item">
                                <p>{{ template }}</p>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">No templates added yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}