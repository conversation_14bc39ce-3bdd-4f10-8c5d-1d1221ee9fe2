# Use Python 3.10 slim image as base
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Copy requirements file
COPY ../../requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project (this ensures proper imports)
COPY . .

# Set environment variables (these will be overridden at runtime)
ENV TELEGRAM_BOT_TOKEN=""
ENV BACKEND_URL=""

# Run the bot as a module from the project root
CMD ["python", "-m", "bot.telegram.telegram_bot"]













