from flask import Blueprint, request, jsonify, current_app, redirect, url_for
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
import logging
from ..services.system_setting_service import SystemSettingService

logger = logging.getLogger(__name__)
system_setting_bp = Blueprint('system_setting', __name__, url_prefix='/api/system-settings')

# Validation schemas
class CreateSettingSchema(Schema):
    key = fields.String(required=True)
    value = fields.String(required=True)
    description = fields.String(missing=None)

class UpdateSettingSchema(Schema):
    value = fields.String(required=True)
    description = fields.String(missing=None)

# Helper function to check admin status
def admin_required(f):
    """Decorator to check if user is an admin"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('admin.login', next=request.url))
        if current_user.role != UserRole.ADMIN:
            return jsonify({"success": False, "error": "Admin access required"}), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@system_setting_bp.route('/', methods=['GET'])
@admin_required
def get_all_settings():
    """Get all system settings"""
    try:
        settings = SystemSettingService.get_all_settings()
        return jsonify({
            "success": True,
            "data": [
                {
                    "key": setting.key,
                    "value": setting.value,
                    "description": setting.description,
                    "updated_at": setting.updated_at.isoformat() if setting.updated_at else None
                } for setting in settings
            ]
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving all settings: {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve settings"}), 500

@system_setting_bp.route('/<key>', methods=['GET'])
@admin_required
def get_setting(key):
    """Get a specific system setting by key"""
    try:
        setting = SystemSettingService.get_setting(key)
        if not setting:
            return jsonify({"success": False, "error": "Setting not found"}), 404
            
        return jsonify({
            "success": True,
            "data": {
                "key": setting.key,
                "value": setting.value,
                "description": setting.description,
                "updated_at": setting.updated_at.isoformat() if setting.updated_at else None
            }
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving setting '{key}': {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve setting"}), 500

@system_setting_bp.route('/', methods=['POST'])
@admin_required
def create_setting():
    """Create a new system setting"""
    try:
        # Validate request data
        schema = CreateSettingSchema()
        data = schema.load(request.json)
        
        # Check if setting already exists
        existing = SystemSettingService.get_setting(data['key'])
        if existing:
            return jsonify({"success": False, "error": "Setting already exists"}), 409
        
        # Create setting
        setting = SystemSettingService.set_setting(
            key=data['key'],
            value=data['value'],
            description=data['description']
        )
        
        if not setting:
            return jsonify({"success": False, "error": "Failed to create setting"}), 500
            
        return jsonify({
            "success": True,
            "data": {
                "key": setting.key,
                "value": setting.value,
                "description": setting.description,
                "updated_at": setting.updated_at.isoformat() if setting.updated_at else None
            }
        }), 201
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error creating setting: {str(e)}")
        return jsonify({"success": False, "error": "Failed to create setting"}), 500

@system_setting_bp.route('/<key>', methods=['PUT'])
@admin_required
def update_setting(key):
    """Update an existing system setting"""
    try:
        # Check if setting exists
        existing = SystemSettingService.get_setting(key)
        if not existing:
            return jsonify({"success": False, "error": "Setting not found"}), 404
        
        # Validate request data
        schema = UpdateSettingSchema()
        data = schema.load(request.json)
        
        # Update setting
        setting = SystemSettingService.set_setting(
            key=key,
            value=data['value'],
            description=data['description']
        )
        
        if not setting:
            return jsonify({"success": False, "error": "Failed to update setting"}), 500
            
        return jsonify({
            "success": True,
            "data": {
                "key": setting.key,
                "value": setting.value,
                "description": setting.description,
                "updated_at": setting.updated_at.isoformat() if setting.updated_at else None
            }
        }), 200
    except ValidationError as e:
        return jsonify({"success": False, "error": e.messages}), 400
    except Exception as e:
        logger.error(f"Error updating setting '{key}': {str(e)}")
        return jsonify({"success": False, "error": "Failed to update setting"}), 500

@system_setting_bp.route('/<key>', methods=['DELETE'])
@admin_required
def delete_setting(key):
    """Delete a system setting"""
    try:
        # Check if setting exists
        existing = SystemSettingService.get_setting(key)
        if not existing:
            return jsonify({"success": False, "error": "Setting not found"}), 404
        
        # Delete setting
        success = SystemSettingService.delete_setting(key)
        if not success:
            return jsonify({"success": False, "error": "Failed to delete setting"}), 500
            
        return jsonify({
            "success": True,
            "message": f"Setting '{key}' deleted successfully"
        }), 200
    except Exception as e:
        logger.error(f"Error deleting setting '{key}': {str(e)}")
        return jsonify({"success": False, "error": "Failed to delete setting"}), 500

@system_setting_bp.route('/prefix/<prefix>', methods=['GET'])
@admin_required
def get_settings_by_prefix(prefix):
    """Get all settings with keys starting with the given prefix"""
    try:
        settings = SystemSettingService.get_settings_by_prefix(prefix)
        return jsonify({
            "success": True,
            "data": [
                {
                    "key": setting.key,
                    "value": setting.value,
                    "description": setting.description,
                    "updated_at": setting.updated_at.isoformat() if setting.updated_at else None
                } for setting in settings
            ]
        }), 200
    except Exception as e:
        logger.error(f"Error retrieving settings with prefix '{prefix}': {str(e)}")
        return jsonify({"success": False, "error": "Failed to retrieve settings"}), 500
