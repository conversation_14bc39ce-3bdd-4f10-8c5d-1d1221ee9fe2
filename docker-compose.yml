version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - .:/app
    environment:
      - FLASK_DEBUG=1
      - FACEBOOK_PAGE_ACCESS_TOKEN=${FACEBOOK_PAGE_ACCESS_TOKEN:-mock_token}
      - FACEBOOK_PAGE_ID=${FACEBOOK_PAGE_ID:-mock_page_id}
      - GROQ_API_KEY=${GROQ_API_KEY:-mock_api_key}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-mock_api_key}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-mock_api_key}
      - GROK_API_KEY=${GROK_API_KEY:-mock_api_key}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY:-mock_api_key}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY:-mock_api_key}
      - STABILITY_API_KEY=${STABILITY_API_KEY:-mock_api_key}
      - DEEPINFRA_API_KEY=${DEEPINFRA_API_KEY:-mock_api_key}
      - SECRET_KEY=${SECRET_KEY:-development_secret_key}
      - MONGODB_URI=mongodb://mongo:27017/rominext
    depends_on:
      - mongo

  mongo:
    image: mongo:7.0
    container_name: rominext_mongo
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:

networks:
  default:
    external:
      name: mongo-network
