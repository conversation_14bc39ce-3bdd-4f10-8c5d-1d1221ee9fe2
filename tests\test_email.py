#!/usr/bin/env python3
"""
Email system test script for Rominext
"""
import os
import sys
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app import create_app
from app.services.email_service import EmailService
from app.services.email_template_service import EmailTemplateService
from app.services.email_queue_service import email_queue_service

def test_smtp_connection():
    """Test SMTP connection"""
    print("🔌 Testing SMTP connection...")
    
    try:
        email_service = EmailService()
        result = email_service.test_smtp_connection()
        
        if result['success']:
            print("✅ SMTP connection successful!")
            return True
        else:
            print(f"❌ SMTP connection failed: {result['error']}")
            return False
    except Exception as e:
        print(f"❌ SMTP connection error: {str(e)}")
        return False

def test_simple_email():
    """Send a simple test email"""
    print("\n📧 Sending simple test email...")
    
    try:
        email_service = EmailService()
        
        result = email_service.send_email(
            to_email="<EMAIL>",
            subject="🎉 Rominext Email System Test",
            html_content="""
            <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center;">
                    <h1>🎉 Email System Test Successful!</h1>
                    <p style="font-size: 18px;">Your Rominext email system is working perfectly!</p>
                </div>
                
                <div style="padding: 30px; background-color: #f8f9fa; border-radius: 10px; margin-top: 20px;">
                    <h2 style="color: #333;">Test Details:</h2>
                    <ul style="color: #666; line-height: 1.6;">
                        <li><strong>Test Time:</strong> {}</li>
                        <li><strong>SMTP Host:</strong> mailpanel.bertina.us</li>
                        <li><strong>From:</strong> <EMAIL></li>
                        <li><strong>System:</strong> Rominext Advanced Email System</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding: 20px; border-top: 1px solid #eee;">
                    <p style="color: #888;">This email was sent from your Rominext application</p>
                    <p style="color: #888;">🚀 Ready to send professional emails!</p>
                </div>
            </body>
            </html>
            """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            text_content=f"""
🎉 Rominext Email System Test Successful!

Your Rominext email system is working perfectly!

Test Details:
- Test Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- SMTP Host: mailpanel.bertina.us
- From: <EMAIL>
- System: Rominext Advanced Email System

This email was sent from your Rominext application
🚀 Ready to send professional emails!
            """,
            to_name="Ehsan Ghanbari"
        )
        
        if result['success']:
            print(f"✅ Test email sent successfully!")
            print(f"📧 Tracking ID: {result['tracking_id']}")
            return True
        else:
            print(f"❌ Failed to send test email: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Email sending error: {str(e)}")
        return False

def test_welcome_template():
    """Test welcome email template"""
    print("\n🎊 Testing welcome email template...")
    
    try:
        # Queue welcome email
        queue_id = email_queue_service.send_welcome_email(
            user_email="<EMAIL>",
            user_name="Ehsan Ghanbari",
            language="en"
        )
        
        print(f"✅ Welcome email queued successfully!")
        print(f"📋 Queue ID: {queue_id}")
        
        # Give the queue worker a moment to process
        import time
        print("⏳ Waiting for queue processing...")
        time.sleep(3)
        
        return True
        
    except Exception as e:
        print(f"❌ Welcome email error: {str(e)}")
        return False

def test_persian_template():
    """Test Persian email template"""
    print("\n🇮🇷 Testing Persian email template...")
    
    try:
        # Queue welcome email in Persian
        queue_id = email_queue_service.send_welcome_email(
            user_email="<EMAIL>",
            user_name="احسان قنبری",
            language="fa"
        )
        
        print(f"✅ Persian welcome email queued successfully!")
        print(f"📋 Queue ID: {queue_id}")
        
        # Give the queue worker a moment to process
        import time
        print("⏳ Waiting for queue processing...")
        time.sleep(3)
        
        return True
        
    except Exception as e:
        print(f"❌ Persian email error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Rominext Email System Tests")
    print("=" * 50)
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        # Initialize system templates
        try:
            print("📝 Initializing system email templates...")
            EmailTemplateService.initialize_system_templates()
            print("✅ System templates initialized!")
        except Exception as e:
            print(f"⚠️  Template initialization warning: {str(e)}")
        
        # Start email queue worker
        try:
            print("🔄 Starting email queue worker...")
            email_queue_service.start_worker()
            print("✅ Email queue worker started!")
        except Exception as e:
            print(f"⚠️  Queue worker warning: {str(e)}")
        
        # Run tests
        tests_passed = 0
        total_tests = 4
        
        if test_smtp_connection():
            tests_passed += 1
            
        if test_simple_email():
            tests_passed += 1
            
        if test_welcome_template():
            tests_passed += 1
            
        if test_persian_template():
            tests_passed += 1
        
        # Results
        print("\n" + "=" * 50)
        print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 All tests passed! Your email system is working perfectly!")
            print("📧 Check your inbox: <EMAIL>")
        else:
            print("⚠️  Some tests failed. Please check the configuration.")
        
        # Stop queue worker
        try:
            email_queue_service.stop_worker()
            print("🛑 Email queue worker stopped")
        except:
            pass

if __name__ == "__main__":
    main()
