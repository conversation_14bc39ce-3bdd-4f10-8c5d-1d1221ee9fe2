import os
from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, CommandHandler, MessageHandler, ContextTypes, filters
import re
import requests
import traceback
from app.utils.logging_helpers import log_event
from app.models.log import LogLevel, LogCategory

# Replace these with your actual config
API_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
BACKEND_URL = os.getenv('BACKEND_URL')

# /start command handler
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text(
        "👋 Welcome to Rominext Bot!\n"
        "Please send your one-time verification code (e.g., romi_123456) or forward a message from your Telegram channel."
    )

# /verify command handler
async def verify_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    
    if not context.args or len(context.args) != 1:
        await update.message.reply_text("❌ Please provide a verification code. Example: /verify romi_123456")
        return
    
    code = context.args[0]
    
    if re.match(r"^romi_\d{6}$", code):
        try:
            response = requests.post(f"{BACKEND_URL}/verify-code", json={
                "telegram_user_id": user_id,
                "code": code
            })
            if response.status_code == 200:
                await update.message.reply_text("✅ Verification successful! You can now forward a message from your Telegram channel.")
            else:
                error_msg = response.json().get("error", "Verification failed")
                await update.message.reply_text(f"❌ {error_msg}. Please check your code or try again.")
        except Exception as e:
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.SYSTEM,
                message=f"Telegram bot verification error: {str(e)}",
                details=traceback.format_exc()
            )
            await update.message.reply_text("🚫 Server error. Please try again later.")
    else:
        await update.message.reply_text("❗ Please send a valid verification code in format romi_XXXXXX.")

# Handle verification code input
async def handle_verification(update: Update, context: ContextTypes.DEFAULT_TYPE):
    message = update.message.text.strip()
    user_id = update.effective_user.id

    if re.match(r"^romi_\d{6}$", message):
        try:
            response = requests.post(f"{BACKEND_URL}/verify-code", json={
                "telegram_user_id": user_id,
                "code": message
            })
            if response.status_code == 200:
                await update.message.reply_text("✅ Verification successful! You can now forward a message from your Telegram channel.")
            else:
                error_msg = response.json().get("error", "Verification failed")
                await update.message.reply_text(f"❌ {error_msg}. Please check your code or try again.")
        except Exception as e:
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.SYSTEM,
                message=f"Telegram bot verification error: {str(e)}",
                details=traceback.format_exc()
            )
            await update.message.reply_text("🚫 Server error. Please try again later.")
    else:
        await update.message.reply_text("❗ Please send a valid verification code in format romi_XXXXXX.")

# Handle forwarded messages to detect channel connection
async def handle_forwarded(update: Update, context: ContextTypes.DEFAULT_TYPE):
    message = update.message
    user_id = update.effective_user.id

    if message.forward_from_chat:
        channel_id = message.forward_from_chat.id
        try:
            response = requests.post(f"{BACKEND_URL}/connect-channel", json={
                "telegram_user_id": user_id,
                "channel_id": channel_id
            })
            if response.status_code == 200:
                await update.message.reply_text("✅ Channel connected successfully!")
            else:
                await update.message.reply_text("❌ Could not connect your channel. Please verify your account first.")
        except Exception as e:
            log_event(
                level=LogLevel.ERROR,
                category=LogCategory.SYSTEM,
                message=f"Telegram channel connection error: {str(e)}",
                details=traceback.format_exc()
            )
            await update.message.reply_text("🚫 Server error. Please try again later.")
    else:
        await update.message.reply_text("⚠️ Please forward a message from your channel.")

# Entry point
if __name__ == "__main__":
    try:
        app = ApplicationBuilder().token(API_TOKEN).build()

        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("verify", verify_command))
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_verification))
        app.add_handler(MessageHandler(filters.FORWARDED, handle_forwarded))

        print("🤖 Rominext Bot is running...")
        app.run_polling()
    except Exception as e:
        log_event(
            level=LogLevel.CRITICAL,
            category=LogCategory.SYSTEM,
            message=f"Telegram bot initialization failed: {str(e)}",
            details=traceback.format_exc()
        )
        print(f"Critical error: {str(e)}")
