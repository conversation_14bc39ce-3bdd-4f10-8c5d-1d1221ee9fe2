{% extends "admin/admin_layout.html" %}

{% block title %}تنظیمات ایمیل - Rominext{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.settings-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
}

.btn-test {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    color: white;
}

.btn-test:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    color: white;
}

.settings-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.password-toggle {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header settings-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            تنظیمات ایمیل
                        </h5>
                        <div>
                            <a href="{{ url_for('admin.email_test') }}" class="btn btn-test btn-sm">
                                <i class="fas fa-vial me-1"></i>
                                تست ایمیل
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.settings_email') }}">
                        <!-- SMTP Configuration Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-server me-2"></i>
                                تنظیمات SMTP
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_host" class="form-label">SMTP Host</label>
                                    <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                           value="{{ settings.get('smtp_host', '') }}" placeholder="smtp.example.com">
                                    <div class="form-text">آدرس سرور SMTP</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_port" class="form-label">SMTP Port</label>
                                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                           value="{{ settings.get('smtp_port', '587') }}" placeholder="587">
                                    <div class="form-text">پورت سرور SMTP (معمولاً 587 یا 465)</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_username" class="form-label">SMTP Username</label>
                                    <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                           value="{{ settings.get('smtp_username', '') }}" placeholder="<EMAIL>">
                                    <div class="form-text">نام کاربری SMTP</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_password" class="form-label">SMTP Password</label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                               value="{{ settings.get('smtp_password', '') }}" placeholder="••••••••">
                                        <i class="fas fa-eye password-toggle" onclick="togglePassword('smtp_password')"></i>
                                    </div>
                                    <div class="form-text">رمز عبور SMTP</div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-shield-alt me-2"></i>
                                تنظیمات امنیتی
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="smtp_use_tls" name="smtp_use_tls" 
                                               {% if settings.get('smtp_use_tls', 'true') == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="smtp_use_tls">
                                            استفاده از TLS
                                        </label>
                                        <div class="form-text">فعال‌سازی رمزنگاری TLS</div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="smtp_use_ssl" name="smtp_use_ssl" 
                                               {% if settings.get('smtp_use_ssl') == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="smtp_use_ssl">
                                            استفاده از SSL
                                        </label>
                                        <div class="form-text">فعال‌سازی رمزنگاری SSL</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Email Identity Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-id-card me-2"></i>
                                هویت ایمیل
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email_from_name" class="form-label">نام فرستنده</label>
                                    <input type="text" class="form-control" id="email_from_name" name="email_from_name" 
                                           value="{{ settings.get('email_from_name', 'Rominext') }}" placeholder="Rominext">
                                    <div class="form-text">نام نمایش داده شده به عنوان فرستنده</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email_from_address" class="form-label">آدرس ایمیل فرستنده</label>
                                    <input type="email" class="form-control" id="email_from_address" name="email_from_address" 
                                           value="{{ settings.get('email_from_address', '') }}" placeholder="<EMAIL>">
                                    <div class="form-text">آدرس ایمیل فرستنده</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email_reply_to" class="form-label">آدرس پاسخ (اختیاری)</label>
                                    <input type="email" class="form-control" id="email_reply_to" name="email_reply_to" 
                                           value="{{ settings.get('email_reply_to', '') }}" placeholder="<EMAIL>">
                                    <div class="form-text">آدرس ایمیل برای پاسخ‌ها</div>
                                </div>
                            </div>
                        </div>

                        <!-- Current Configuration Display -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-info-circle me-2"></i>
                                تنظیمات فعلی
                            </h6>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-cog me-2"></i>پیکربندی فعلی:</h6>
                                        <ul class="mb-0">
                                            <li><strong>SMTP Host:</strong> {{ settings.get('smtp_host', 'تنظیم نشده') }}</li>
                                            <li><strong>SMTP Port:</strong> {{ settings.get('smtp_port', 'تنظیم نشده') }}</li>
                                            <li><strong>From Address:</strong> {{ settings.get('email_from_address', 'تنظیم نشده') }}</li>
                                            <li><strong>TLS:</strong> {{ 'فعال' if settings.get('smtp_use_tls') == 'true' else 'غیرفعال' }}</li>
                                            <li><strong>SSL:</strong> {{ 'فعال' if settings.get('smtp_use_ssl') == 'true' else 'غیرفعال' }}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    ذخیره تنظیمات
                                </button>
                                <button type="reset" class="btn btn-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    بازنشانی
                                </button>
                            </div>
                            <div>
                                <a href="{{ url_for('admin.email_test') }}" class="btn btn-test me-2">
                                    <i class="fas fa-vial me-2"></i>
                                    تست ایمیل
                                </a>
                                <a href="{{ url_for('admin.settings_general') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    تنظیمات عمومی
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = field.nextElementSibling;
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
