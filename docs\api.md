# API Documentation

This document provides comprehensive documentation for the Rominext REST API, including authentication, endpoints, request/response formats, and examples.

## Base URL

```
Development: http://localhost:5000/api
Production: https://your-domain.com/api
```

## Authentication

### JWT Token Authentication

Most API endpoints require authentication using JWT tokens.

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "customer"
  }
}
```

#### Using Authentication Token

Include the token in the Authorization header:

```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Core Endpoints

### Health Check

#### Get System Health
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "database": "connected",
  "services": {
    "ai_providers": "operational",
    "social_connectors": "operational"
  }
}
```

## User Management

### Users

#### Get Current User
```http
GET /api/users/me
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": "user_id",
  "email": "<EMAIL>",
  "name": "User Name",
  "role": "customer",
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "settings": {
    "timezone": "UTC",
    "language": "en"
  }
}
```

#### Update User Profile
```http
PUT /api/users/me
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Updated Name",
  "settings": {
    "timezone": "America/New_York",
    "language": "en"
  }
}
```

## Account Management

### Social Media Accounts

#### List User Accounts
```http
GET /api/accounts
Authorization: Bearer {token}
```

**Response:**
```json
{
  "accounts": [
    {
      "id": "account_id",
      "platform": "facebook",
      "account_name": "My Business Page",
      "account_id": "facebook_page_id",
      "status": "active",
      "connected_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1
}
```

#### Add Social Media Account
```http
POST /api/accounts
Authorization: Bearer {token}
Content-Type: application/json

{
  "platform": "facebook",
  "access_token": "facebook_access_token",
  "account_id": "facebook_page_id",
  "account_name": "My Business Page"
}
```

#### Remove Account
```http
DELETE /api/accounts/{account_id}
Authorization: Bearer {token}
```

## Content Management

### Posts

#### Create Post
```http
POST /api/posts
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "This is my post content",
  "account_id": "account_id",
  "media_url": "https://example.com/image.jpg",
  "scheduled_at": "2024-01-01T12:00:00Z",
  "status": "draft"
}
```

**Response:**
```json
{
  "id": "post_id",
  "content": "This is my post content",
  "account_id": "account_id",
  "media_url": "https://example.com/image.jpg",
  "scheduled_at": "2024-01-01T12:00:00Z",
  "status": "draft",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### List Posts
```http
GET /api/posts?status=draft&limit=10&offset=0
Authorization: Bearer {token}
```

**Query Parameters:**
- `status`: Filter by post status (draft, scheduled, published, failed)
- `account_id`: Filter by account
- `limit`: Number of posts to return (default: 20, max: 100)
- `offset`: Number of posts to skip (default: 0)

**Response:**
```json
{
  "posts": [
    {
      "id": "post_id",
      "content": "Post content",
      "account": {
        "id": "account_id",
        "platform": "facebook",
        "account_name": "My Page"
      },
      "status": "draft",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

#### Get Post Details
```http
GET /api/posts/{post_id}
Authorization: Bearer {token}
```

#### Update Post
```http
PUT /api/posts/{post_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Updated post content",
  "scheduled_at": "2024-01-01T15:00:00Z"
}
```

#### Delete Post
```http
DELETE /api/posts/{post_id}
Authorization: Bearer {token}
```

#### Publish Post
```http
POST /api/posts/{post_id}/publish
Authorization: Bearer {token}
```

## AI Content Generation

### Generate Content

#### Generate Post Content
```http
POST /api/ai/generate/content
Authorization: Bearer {token}
Content-Type: application/json

{
  "prompt": "Create a social media post about sustainable living",
  "platform": "facebook",
  "tone": "professional",
  "length": "medium",
  "include_hashtags": true,
  "ai_provider": "openai"
}
```

**Response:**
```json
{
  "content": "🌱 Embrace sustainable living! Small changes make a big impact...",
  "hashtags": ["#SustainableLiving", "#EcoFriendly", "#GreenLife"],
  "suggestions": [
    "Consider adding an image of eco-friendly products",
    "Best posting time: 2-4 PM on weekdays"
  ],
  "ai_provider": "openai",
  "generation_time": 2.3
}
```

#### Generate Image
```http
POST /api/ai/generate/image
Authorization: Bearer {token}
Content-Type: application/json

{
  "prompt": "A beautiful sunset over mountains",
  "style": "photorealistic",
  "size": "1024x1024",
  "ai_provider": "stabilityai"
}
```

**Response:**
```json
{
  "image_url": "https://generated-images.example.com/image123.jpg",
  "prompt": "A beautiful sunset over mountains",
  "style": "photorealistic",
  "size": "1024x1024",
  "ai_provider": "stabilityai"
}
```

## Scheduling

### Post Scheduling

#### Schedule Post
```http
POST /api/scheduling/schedule
Authorization: Bearer {token}
Content-Type: application/json

{
  "post_id": "post_id",
  "scheduled_at": "2024-01-01T12:00:00Z",
  "timezone": "America/New_York",
  "recurrence": {
    "type": "weekly",
    "interval": 1,
    "days": ["monday", "wednesday", "friday"]
  }
}
```

#### Get Scheduled Posts
```http
GET /api/scheduling/scheduled?start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer {token}
```

**Response:**
```json
{
  "scheduled_posts": [
    {
      "id": "schedule_id",
      "post_id": "post_id",
      "scheduled_at": "2024-01-01T12:00:00Z",
      "status": "scheduled",
      "recurrence": {
        "type": "weekly",
        "interval": 1,
        "days": ["monday", "wednesday", "friday"]
      }
    }
  ],
  "total": 1
}
```

## Analytics

### Post Analytics

#### Get Post Performance
```http
GET /api/analytics/posts/{post_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "post_id": "post_id",
  "metrics": {
    "impressions": 1250,
    "reach": 980,
    "engagement": 45,
    "likes": 32,
    "comments": 8,
    "shares": 5,
    "clicks": 12
  },
  "engagement_rate": 3.6,
  "best_performing_time": "14:30",
  "audience_demographics": {
    "age_groups": {
      "18-24": 15,
      "25-34": 35,
      "35-44": 30,
      "45-54": 20
    },
    "gender": {
      "male": 45,
      "female": 55
    }
  }
}
```

#### Get Account Analytics
```http
GET /api/analytics/accounts/{account_id}?period=30d
Authorization: Bearer {token}
```

**Query Parameters:**
- `period`: Time period (7d, 30d, 90d, 1y)

## Comment Management

### Comments

#### Get Post Comments
```http
GET /api/comments/posts/{post_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "comments": [
    {
      "id": "comment_id",
      "post_id": "post_id",
      "author": "John Doe",
      "content": "Great post!",
      "sentiment": "positive",
      "created_at": "2024-01-01T10:30:00Z",
      "replied": false
    }
  ],
  "total": 1
}
```

#### Reply to Comment
```http
POST /api/comments/{comment_id}/reply
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Thank you for your feedback!",
  "auto_generated": false
}
```

## Webhooks

### Social Media Webhooks

#### Facebook Webhook
```http
POST /api/webhooks/facebook
Content-Type: application/json

{
  "object": "page",
  "entry": [
    {
      "id": "page_id",
      "time": **********,
      "changes": [
        {
          "value": {
            "item": "comment",
            "comment_id": "comment_id",
            "post_id": "post_id",
            "message": "New comment content"
          },
          "field": "feed"
        }
      ]
    }
  ]
}
```

## Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456"
}
```

### Common Error Codes

- `400 BAD_REQUEST`: Invalid request data
- `401 UNAUTHORIZED`: Missing or invalid authentication
- `403 FORBIDDEN`: Insufficient permissions
- `404 NOT_FOUND`: Resource not found
- `429 RATE_LIMITED`: Too many requests
- `500 INTERNAL_ERROR`: Server error

## Rate Limiting

### Rate Limits

- **General API**: 100 requests per minute per user
- **AI Generation**: 20 requests per minute per user
- **Webhooks**: 1000 requests per minute per endpoint

### Rate Limit Headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## SDKs and Libraries

### Python SDK Example

```python
import requests

class RominextAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def create_post(self, content, account_id):
        data = {
            'content': content,
            'account_id': account_id,
            'status': 'draft'
        }
        response = requests.post(
            f'{self.base_url}/posts',
            json=data,
            headers=self.headers
        )
        return response.json()

# Usage
api = RominextAPI('http://localhost:5000/api', 'your_token')
post = api.create_post('Hello World!', 'account_id')
```

### JavaScript SDK Example

```javascript
class RominextAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }

    async createPost(content, accountId) {
        const response = await fetch(`${this.baseUrl}/posts`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({
                content: content,
                account_id: accountId,
                status: 'draft'
            })
        });
        return response.json();
    }
}

// Usage
const api = new RominextAPI('http://localhost:5000/api', 'your_token');
const post = await api.createPost('Hello World!', 'account_id');
```

## Testing the API

### Using cURL

```bash
# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Create post
curl -X POST http://localhost:5000/api/posts \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"content":"Test post","account_id":"account_id","status":"draft"}'
```

### Postman Collection

A Postman collection is available at `/docs/postman/rominext-api.json` with pre-configured requests for all endpoints.

---

For interactive API documentation, visit the Swagger UI at `/api/docs/` when running the application.
