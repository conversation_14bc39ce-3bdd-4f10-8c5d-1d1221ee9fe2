{% extends "dashboard/dashboard_layout.html" %}

{% block title %}Profile Settings - Rominext{% endblock %}

{% block dashboard_content %}
<div class="row mt-4">
    <div class="col-12">
        <h2>Profile Settings</h2>
        
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ current_user.name }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ current_user.email }}" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

