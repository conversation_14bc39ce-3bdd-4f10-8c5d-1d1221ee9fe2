import requests
from typing import Dict, Any, List
from .base_social_connector import BaseSocialIntegration as BaseConnector

class LinkedInConnector(BaseConnector):
    """
    Integration class for LinkedIn API (v2)
    """

    def __init__(self, auth_data: dict):
        self.access_token = auth_data.get("access_token")
        self.api_base_url = "https://api.linkedin.com/v2"
        self.supported_features = ["post", "edit", "delete", "metrics", "comments"]

    def authenticate(self) -> bool:
        """
        Authenticate using the Access token
        """
        url = f"{self.api_base_url}/me"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        resp = requests.get(url, headers=headers)
        return resp.status_code == 200

    def post_content(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Post an update (text, article, or image)
        """
        message = content_data.get("message", "")
        image_url = content_data.get("image_url")

        url = f"{self.api_base_url}/ugcPosts"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        # Construct the UGC post payload
        payload = {
            "author": f"urn:li:person:{content_data.get('author_id')}",
            "lifecycleState": "PUBLISHED",
            "specificContent": {
                "com.linkedin.ugc.ShareContent": {
                    "shareCommentary": {
                        "text": message
                    },
                    "shareMediaCategory": "ARTICLE" if image_url else "NONE",
                    "media": [{
                        "status": "READY",
                        "description": message,
                        "media": image_url,
                        "title": "LinkedIn Post Media"
                    }] if image_url else []
                }
            },
            "visibility": {
                "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
            }
        }

        resp = requests.post(url, headers=headers, json=payload)
        resp.raise_for_status()
        return resp.json()

    def edit_content(self, post_id: str, updated_data: Dict[str, Any]) -> bool:
        """
        Edit a post on LinkedIn (not directly supported in API)
        """
        raise NotImplementedError("LinkedIn API does not support direct post editing.")

    def delete_content(self, post_id: str) -> bool:
        """
        Delete a post by post ID
        """
        url = f"{self.api_base_url}/ugcPosts/{post_id}"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }

        resp = requests.delete(url, headers=headers)
        return resp.status_code == 204

    def fetch_profile_info(self) -> Dict[str, Any]:
        """
        Fetch authenticated user's LinkedIn profile info
        """
        url = f"{self.api_base_url}/me"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        resp = requests.get(url, headers=headers)
        resp.raise_for_status()
        return resp.json()

    def fetch_recent_posts(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Fetch recent posts from the authenticated user's feed
        """
        url = f"{self.api_base_url}/activityFeeds"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        params = {
            "count": limit
        }
        resp = requests.get(url, headers=headers, params=params)
        resp.raise_for_status()
        return resp.json().get("elements", [])

    def post_comment(self, post_id: str, comment_text: str) -> Dict[str, Any]:
        """
        Post a comment on a LinkedIn post
        """
        url = f"{self.api_base_url}/socialActions/{post_id}/comments"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        payload = {
            "text": comment_text
        }

        resp = requests.post(url, headers=headers, json=payload)
        resp.raise_for_status()
        return resp.json()

    def fetch_comments(self, post_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Fetch comments for a specific post
        """
        url = f"{self.api_base_url}/socialActions/{post_id}/comments"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        params = {
            "count": limit
        }
        resp = requests.get(url, headers=headers, params=params)
        resp.raise_for_status()
        return resp.json().get("elements", [])

    def fetch_metrics(self, post_id: str) -> Dict[str, Any]:
        """
        Fetch post engagement metrics (likes, comments, shares)
        """
        url = f"{self.api_base_url}/socialActions/{post_id}/likes"
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        resp = requests.get(url, headers=headers)
        resp.raise_for_status()
        return resp.json()

    def verify_connection(self) -> bool:
        return self.authenticate()

    def schedule_content(self, content_data: Dict[str, Any], scheduled_time: str) -> Dict[str, Any]:
        """
        Schedule a post for future publishing
        Note: LinkedIn API doesn't directly support scheduling posts.
        This would typically be handled by our application's scheduling system.
        """
        # For now, we'll raise NotImplementedError as LinkedIn API doesn't support this directly
        raise NotImplementedError("LinkedIn API does not support scheduling posts directly. Use application-level scheduling.")

    def upload_media(self, media_path: str, media_type: str) -> str:
        """
        Upload media to LinkedIn
        """
        url = f"{self.api_base_url}/assets"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "X-Restli-Protocol-Version": "2.0.0"
        }
        
        # Determine media type
        content_type = {
            "image": "image/jpeg",
            "video": "video/mp4",
            "document": "application/pdf"
        }.get(media_type.lower(), "application/octet-stream")
        
        with open(media_path, 'rb') as media:
            files = {'media': (media_path, media, content_type)}
            resp = requests.post(url, headers=headers, files=files)
        
        resp.raise_for_status()
        return resp.json().get("id", "")
