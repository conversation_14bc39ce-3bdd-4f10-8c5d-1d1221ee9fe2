{% extends "admin/admin_layout.html" %}

{% block title %}Email Test - Rominext{% endblock %}

{% block extra_css %}
<style>
.test-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.test-card .card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.btn-test {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    color: white;
}

.btn-test:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    color: white;
    transform: translateY(-1px);
}

.config-display {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-vial me-2"></i>
                    Email System Test
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-envelope me-2"></i>Templates
                    </a>
                    <a href="{{ url_for('admin.email_analytics') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-line me-2"></i>Analytics
                    </a>
                    <a href="{{ url_for('admin.settings_email') }}" class="btn btn-outline-warning">
                        <i class="fas fa-cog me-2"></i>Settings
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Manual Email Test -->
                    <div class="test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-paper-plane me-2"></i>
                                Manual Email Test
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('admin.email_test') }}">
                                <div class="mb-3">
                                    <label for="to_email" class="form-label">To Email</label>
                                    <input type="email" class="form-control" id="to_email" name="to_email" 
                                           value="<EMAIL>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="Test Email from Rominext" required>
                                </div>
                                <div class="mb-3">
                                    <label for="html_content" class="form-label">HTML Content</label>
                                    <textarea class="form-control" id="html_content" name="html_content" rows="8">
<h1>Test Email</h1>
<p>Hello!</p>
<p>This is a test email from <strong>Rominext</strong> email system.</p>
<p>If you received this email, the email system is working correctly.</p>
<p>Best regards,<br>The Rominext Team</p>
                                    </textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="text_content" class="form-label">Text Content</label>
                                    <textarea class="form-control" id="text_content" name="text_content" rows="6">
Test Email

Hello!

This is a test email from Rominext email system.

If you received this email, the email system is working correctly.

Best regards,
The Rominext Team
                                    </textarea>
                                </div>
                                <button type="submit" class="btn btn-test">
                                    <i class="fas fa-paper-plane me-2"></i>Send Test Email
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Template Test -->
                    <div class="test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>
                                Template Test
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small">Test email templates with sample data</p>
                            <div class="mb-3">
                                <label for="template_key" class="form-label">Template</label>
                                <select class="form-select" id="template_key">
                                    <option value="">Select Template</option>
                                    <option value="welcome">Welcome Email</option>
                                    <option value="password_reset">Password Reset</option>
                                    <option value="email_verification">Email Verification</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="template_language" class="form-label">Language</label>
                                <select class="form-select" id="template_language">
                                    <option value="en">English</option>
                                    <option value="fa">Persian</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-test" onclick="sendTemplateTest()">
                                <i class="fas fa-vial me-2"></i>Test Template
                            </button>
                            <div id="templateTestResult" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Quick Test -->
                    <div class="test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small">Quick test actions with predefined settings</p>
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('admin.email_quick_send_test') }}" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>Send Quick Test Email
                                </a>
                                <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-envelope me-2"></i>Manage Templates
                                </a>
                                <a href="{{ url_for('admin.email_analytics') }}" class="btn btn-outline-info">
                                    <i class="fas fa-chart-line me-2"></i>View Analytics
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Current Configuration -->
                    <div class="test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>
                                Current Configuration
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="config-display">
                                <h6>SMTP Settings</h6>
                                <ul class="list-unstyled mb-0">
                                    <li><strong>Host:</strong> {{ config.SMTP_HOST or 'Not configured' }}</li>
                                    <li><strong>Port:</strong> {{ config.SMTP_PORT or 'Not configured' }}</li>
                                    <li><strong>Username:</strong> {{ config.SMTP_USERNAME or 'Not configured' }}</li>
                                    <li><strong>TLS:</strong> {{ 'Enabled' if config.SMTP_USE_TLS else 'Disabled' }}</li>
                                    <li><strong>SSL:</strong> {{ 'Enabled' if config.SMTP_USE_SSL else 'Disabled' }}</li>
                                </ul>
                            </div>
                            <div class="config-display mt-3">
                                <h6>Email Identity</h6>
                                <ul class="list-unstyled mb-0">
                                    <li><strong>From Name:</strong> {{ config.EMAIL_FROM_NAME or 'Not configured' }}</li>
                                    <li><strong>From Address:</strong> {{ config.EMAIL_FROM_ADDRESS or 'Not configured' }}</li>
                                    <li><strong>Reply To:</strong> {{ config.EMAIL_REPLY_TO or 'Not configured' }}</li>
                                </ul>
                            </div>
                            <div class="mt-3">
                                <a href="{{ url_for('admin.settings_email') }}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit me-2"></i>Edit Settings
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Test History -->
                    <div class="test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>
                                Recent Tests
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center py-3">
                                <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">No recent tests</p>
                                <small class="text-muted">Test history will appear here</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendTemplateTest() {
    const templateKey = document.getElementById('template_key').value;
    const language = document.getElementById('template_language').value;
    
    if (!templateKey) {
        alert('Please select a template');
        return;
    }
    
    const resultDiv = document.getElementById('templateTestResult');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Sending template email...';
    
    fetch('{{ url_for("admin.email_api_send") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            template_key: templateKey,
            to_email: '{{ current_user.email }}',
            language: language,
            variables: {
                user_name: '{{ current_user.name or current_user.email }}',
                reset_link: 'https://example.com/reset',
                verification_link: 'https://example.com/verify'
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '<div class="alert alert-success">Template email sent successfully!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger">Error: ' + data.error + '</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<div class="alert alert-danger">Failed to send template email</div>';
    });
}
</script>
{% endblock %}
