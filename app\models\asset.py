from mongoengine import Document, ReferenceField, StringField, DateTimeField, EnumField, DictField
from datetime import datetime
from enum import Enum
from .user import User

class AssetType(str, Enum):
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"
    AUDIO = "audio"

class Asset(Document):
    user = ReferenceField(User, required=True)
    file_url = StringField(required=True)
    file_type = EnumField(AssetType, required=True)
    metadata = DictField(default={})
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'assets',
        'indexes': ['user', 'file_type']
    }