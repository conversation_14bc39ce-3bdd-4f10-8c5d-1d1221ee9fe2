from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, login_user, logout_user, current_user
from functools import wraps
from ..models import User, UserRole
from mongoengine.errors import NotUniqueError, ValidationError, OperationError
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename
from ..services.translation_service import TranslationService
from ..services.email_service import EmailService
from ..services.email_template_service import EmailTemplateService
from ..services.email_queue_service import email_queue_service
from ..services.email_analytics_service import EmailAnalyticsService
from ..models.email_template import EmailTemplate, EmailLog
from ..utils.logging_helpers import log_event
from ..models.log import LogLevel, LogCategory
import os
import uuid
import logging
import traceback
from datetime import datetime, timedelta

# Get translation function
def translate(key, default=None):
    return current_app.translation_service.translate(key, default)

# Helper function to validate image files
def allowed_file(filename):
    """Check if file extension is allowed for images"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Create blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Make UserRole available to all templates under this blueprint
@admin_bp.context_processor
def inject_user_role():
    return {'UserRole': UserRole}

# Admin required decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            # Store the requested URL for redirecting after login
            next_url = request.url
            return redirect(url_for('admin.login', next=next_url))
        
        if current_user.role != UserRole.ADMIN:
            flash(translate('auth.access_denied'), 'danger')
            return redirect(url_for('admin.login'))
        
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/')
@admin_required
def index():
    """Admin dashboard home"""
    error_message = None
    users_count = 0
    posts_count = 0
    comments_count = 0
    errors_count = 0
    recent_activities = []
    
    try:
        # Get counts for dashboard
        users_count = User.objects.count()
        
        # Try to get other models if they exist
        try:
            from ..models import Post
            posts_count = Post.objects.count()
        except (ImportError, AttributeError):
            pass
            
        try:
            from ..models import Comment
            comments_count = Comment.objects.count()
        except (ImportError, AttributeError):
            pass
            
        try:
            from ..models.log import Log
            errors_count = Log.objects(level='ERROR').count()
            
            # Get recent activities
            recent_logs = Log.objects.order_by('-created_at').limit(5)
            
            for log in recent_logs:
                icon = 'info-circle'
                color = 'primary'
                
                if log.level == 'ERROR':
                    icon = 'exclamation-triangle'
                    color = 'danger'
                elif log.level == 'WARNING':
                    icon = 'exclamation-circle'
                    color = 'warning'
                elif log.level == 'SUCCESS':
                    icon = 'check-circle'
                    color = 'success'
                
                recent_activities.append({
                    'icon': icon,
                    'color': color,
                    'message': log.message[:50] + '...' if len(log.message) > 50 else log.message,
                    'time': log.created_at.strftime('%Y-%m-%d %H:%M')
                })
        except (ImportError, AttributeError, OperationError) as e:
            # Handle MongoDB index error
            if "IndexOptionsConflict" in str(e):
                error_message = "خطا در پایگاه داده: تداخل در ایندکس‌ها. لطفا با مدیر سیستم تماس بگیرید."
            else:
                error_message = f"خطا در دسترسی به لاگ‌ها: {str(e)}"
    
    except Exception as e:
        error_message = f"خطا در بارگذاری داشبورد: {str(e)}"
    
    return render_template('admin/index.html', 
                          users_count=users_count,
                          posts_count=posts_count,
                          comments_count=comments_count,
                          errors_count=errors_count,
                          recent_activities=recent_activities,
                          error_message=error_message)

@admin_bp.route('/users')
@admin_required
def users():
    """Admin users management"""
    try:
        # Get all users
        users = User.objects.all()
        
        # Pass the users to the template
        return render_template('admin/users.html', users=users, UserRole=UserRole)
    except Exception as e:
        flash(f'خطا در بارگذاری لیست کاربران: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

@admin_bp.route('/api/admin/users', methods=['GET'])
@admin_required
def api_users():
    """API endpoint for users with filtering and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')
        role = request.args.get('role', '')
        status = request.args.get('status', '')
        
        # Build query
        query = {}
        
        if search:
            # Search in name or email
            query['$or'] = [
                {'name': {'$regex': search, '$options': 'i'}},
                {'email': {'$regex': search, '$options': 'i'}}
            ]
        
        if role:
            query['role'] = role
        
        if status:
            is_active = status == 'ACTIVE'
            query['is_active'] = is_active
        
        # Get total count for pagination
        total = User.objects(__raw__=query).count()
        total_pages = (total + per_page - 1) // per_page  # Ceiling division
        
        # Get users with pagination
        users = User.objects(__raw__=query).skip((page - 1) * per_page).limit(per_page)
        
        # Format users for response
        formatted_users = []
        for user in users:
            formatted_users.append({
                'id': str(user.id),
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'is_active': user.is_active if hasattr(user, 'is_active') else True,
                'created_at': user.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'data': formatted_users,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': total_pages
            }
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/api/admin/users/<user_id>/toggle-status', methods=['POST'])
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status"""
    try:
        data = request.get_json()
        active = data.get('active', True)
        
        user = User.objects(id=user_id).first()
        if not user:
            return jsonify({
                'success': False,
                'error': 'کاربر یافت نشد'
            }), 404
        
        # Update user status
        user.status = UserStatus.ACTIVE if active else UserStatus.SUSPENDED
        user.save()
        
        return jsonify({
            'success': True,
            'message': 'وضعیت کاربر با موفقیت تغییر کرد'
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/users/add', methods=['POST'])
@admin_required
def add_user():
    """Add a new user"""
    try:
        name = request.form.get('name')
        email = request.form.get('email')
        password = request.form.get('password')
        role_value = request.form.get('role', UserRole.CUSTOMER.value)
        is_active = request.form.get('is_active') == 'on'
        
        # Convert string role to enum
        role = UserRole(role_value)
        
        if not name or not email or not password:
            flash(translate('auth.provide_all_fields'), 'danger')
            return redirect(url_for('admin.users'))

        # Check if user already exists
        if User.objects(email=email).first():
            flash(translate('auth.email_exists'), 'danger')
            return redirect(url_for('admin.users'))
        
        # Create new user with is_admin flag
        is_admin = (role == UserRole.ADMIN)
        
        # Create the user
        user = User.create_user(
            email=email,
            password=password,
            name=name,
            is_admin=is_admin,
            is_active=is_active
        )
        
        # For non-default roles (not ADMIN or CUSTOMER), update explicitly
        if role == UserRole.SUPPORT:
            user.role = UserRole.SUPPORT
            user.save()
        
        flash(translate('auth.user_created_success'), 'success')
        return redirect(url_for('admin.users'))
    except ValidationError as e:
        flash(f'{translate("auth.validation_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.users'))
    except Exception as e:
        flash(f'{translate("auth.user_creation_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.users'))

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page"""
    # If already logged in and is admin, redirect to admin index
    if current_user.is_authenticated and current_user.role == UserRole.ADMIN:
        return redirect(url_for('admin.index'))
    
    # If already logged in but not admin, show error
    if current_user.is_authenticated:
        flash(translate('auth.admin_access_denied'), 'danger')
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        remember = request.form.get('remember', False) == 'on'
        
        if not email or not password:
            flash(translate('auth.provide_email_password'), 'danger')
            return render_template('admin/login.html')
        
        try:
            user = User.objects(email=email).first()
            
            if user and user.check_password(password):
                if user.role == UserRole.ADMIN:
                    login_user(user, remember=remember)
                    # Simplified success message
                    flash(translate('auth.welcome'), 'success')
                    
                    # Redirect to the next page if provided
                    next_page = request.args.get('next')
                    if next_page:
                        return redirect(next_page)
                    return redirect(url_for('admin.index'))
                else:
                    flash(translate('auth.admin_access_denied'), 'danger')
            else:
                flash(translate('auth.invalid_credentials'), 'danger')
        except Exception as e:
            flash(f'{translate("auth.login_error")}: {str(e)}', 'danger')
    
    return render_template('admin/login.html')

@admin_bp.route('/logout')
@login_required
def logout():
    """Admin logout"""
    logout_user()
    flash(translate('auth.logout_success'), 'success')
    return redirect(url_for('admin.login'))

@admin_bp.route('/posts')
@admin_required
def posts():
    """Admin posts management"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = 10
        search = request.args.get('search', '')
        platform = request.args.get('platform', '')
        status = request.args.get('status', '')
        
        # Import Post model
        from ..models import Post
        
        # Build query
        query = {}
        
        if search:
            # Search in title or content
            query['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'content': {'$regex': search, '$options': 'i'}}
            ]
        
        if platform and platform != 'همه':
            query['platform'] = platform
        
        if status:
            if status == 'published':
                query['published'] = True
                query['scheduled'] = False
            elif status == 'scheduled':
                query['scheduled'] = True
            elif status == 'draft':
                query['published'] = False
                query['scheduled'] = False
        
        # Get total count for pagination
        total_posts = Post.objects(__raw__=query).count()
        total_pages = (total_posts + per_page - 1) // per_page  # Ceiling division
        
        # Get posts with pagination
        posts = Post.objects(__raw__=query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        return render_template('admin/posts.html', 
                              posts=posts,
                              total_posts=total_posts,
                              total_pages=total_pages,
                              current_page=page)
    except Exception as e:
        flash(f'خطا در بارگذاری لیست پست‌ها: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

@admin_bp.route('/api/admin/posts', methods=['GET'])
@admin_required
def api_posts():
    """API endpoint for posts with filtering and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')
        platform = request.args.get('platform', '')
        status = request.args.get('status', '')
        
        # Import Post model
        from ..models import Post
        
        # Build query
        query = {}
        
        if search:
            # Search in title or content
            query['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'content': {'$regex': search, '$options': 'i'}}
            ]
        
        if platform and platform != 'همه':
            query['platform'] = platform
        
        if status:
            if status == 'published':
                query['published'] = True
                query['scheduled'] = False
            elif status == 'scheduled':
                query['scheduled'] = True
            elif status == 'draft':
                query['published'] = False
                query['scheduled'] = False
        
        # Get total count for pagination
        total = Post.objects(__raw__=query).count()
        total_pages = (total + per_page - 1) // per_page  # Ceiling division
        
        # Get posts with pagination
        posts = Post.objects(__raw__=query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        # Format posts for response
        formatted_posts = []
        for post in posts:
            formatted_posts.append({
                'id': str(post.id),
                'title': post.title if hasattr(post, 'title') else '',
                'content': post.content,
                'platform': post.platform,
                'published': post.published if hasattr(post, 'published') else False,
                'scheduled': post.scheduled if hasattr(post, 'scheduled') else False,
                'publish_date': post.publish_date.isoformat() if hasattr(post, 'publish_date') and post.publish_date else None,
                'likes': post.analytics_data.get('likes', 0) if hasattr(post, 'analytics_data') else 0,
                'comments': post.analytics_data.get('comments', 0) if hasattr(post, 'analytics_data') else 0,
                'created_at': post.created_at.isoformat() if hasattr(post, 'created_at') else None
            })
        
        return jsonify({
            'success': True,
            'data': formatted_posts,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': total_pages
            }
        }), 200
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/posts/delete/<post_id>', methods=['GET'])
@admin_required
def delete_post(post_id):
    """Delete a post"""
    try:
        from ..models import Post
        post = Post.objects(id=post_id).first()
        
        if not post:
            flash(translate('auth.post_not_found'), 'danger')
            return redirect(url_for('admin.posts'))

        # Delete the post
        post.delete()

        flash(translate('auth.post_deleted_success'), 'success')
        return redirect(url_for('admin.posts'))
    except Exception as e:
        flash(f'{translate("auth.post_delete_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.posts'))

@admin_bp.route('/posts/publish/<post_id>', methods=['GET'])
@admin_required
def publish_post(post_id):
    """Publish a post"""
    try:
        from ..models import Post
        post = Post.objects(id=post_id).first()
        
        if not post:
            flash(translate('auth.post_not_found'), 'danger')
            return redirect(url_for('admin.posts'))

        # Update post status
        post.published = True
        post.scheduled = False
        post.publish_date = datetime.utcnow()
        post.save()

        flash(translate('auth.post_published_success'), 'success')
        return redirect(url_for('admin.posts'))
    except Exception as e:
        flash(f'{translate("auth.post_publish_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.posts'))

@admin_bp.route('/posts/add', methods=['POST'])
@admin_required
def add_post():
    """Add a new post"""
    try:
        title = request.form.get('title')
        content = request.form.get('content')
        platform = request.form.get('platform')
        status = request.form.get('status')
        schedule_date = request.form.get('schedule_date')
        
        # Handle image upload
        image = None
        if 'image' in request.files and request.files['image'].filename:
            image_file = request.files['image']
            # Process and save image
            # This is a placeholder - implement actual image saving logic
            image = f"uploads/{secure_filename(image_file.filename)}"
            # image_file.save(os.path.join(app.config['UPLOAD_FOLDER'], secure_filename(image_file.filename)))
        
        # Create post object
        try:
            from ..models import Post
            post = Post(
                title=title,
                content=content,
                platform=platform,
                published=(status == 'published'),
                scheduled=(status == 'scheduled'),
                publish_date=datetime.fromisoformat(schedule_date) if schedule_date else None,
                image=image,
                created_by=current_user.id
            )
            post.save()
            flash(translate('auth.post_created_success'), 'success')
        except (ImportError, AttributeError):
            # If Post model doesn't exist
            flash(f'{translate("auth.post_created_success")} (حالت نمایشی)', 'success')

        return redirect(url_for('admin.posts'))
    except Exception as e:
        flash(f'{translate("auth.post_creation_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.posts'))

@admin_bp.route('/logs')
@admin_required
def logs():
    """Admin logs management"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = 20
        level = request.args.get('level', '')
        category = request.args.get('category', '')
        
        # Import Log model
        from ..models.log import Log, LogLevel
        
        # Build query
        query = {}
        
        if level and level != 'all':
            query['level'] = level
            
        # Get total count for pagination
        total_logs = Log.objects(**query).count()
        total_pages = (total_logs + per_page - 1) // per_page  # Ceiling division
        
        # Get logs with pagination
        logs = Log.objects(**query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        return render_template('admin/logs.html', 
                              logs=logs,
                              LogLevel=LogLevel,
                              total_logs=total_logs,
                              total_pages=total_pages,
                              current_page=page,
                              current_level=level)
    except Exception as e:
        flash(f'خطا در بارگذاری لیست لاگ‌ها: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

# Add blog management routes to admin blueprint
@admin_bp.route('/blogs')
@admin_required
def blogs():
    """Admin blogs management"""
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = 10
        search = request.args.get('search', '')
        status = request.args.get('status', '')
        
        # Import Blog model
        from ..models import Blog
        
        # Build query
        query = {}
        
        if search:
            # Search in title or content
            query['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'content': {'$regex': search, '$options': 'i'}}
            ]
        
        if status:
            if status == 'published':
                query['is_published'] = True
            elif status == 'draft':
                query['is_published'] = False
        
        # Get total count for pagination
        total_blogs = Blog.objects(__raw__=query).count()
        total_pages = (total_blogs + per_page - 1) // per_page  # Ceiling division
        
        # Get blogs with pagination
        blogs = Blog.objects(__raw__=query).order_by('-created_at').skip((page - 1) * per_page).limit(per_page)
        
        return render_template('admin/blogs.html', 
                              blogs=blogs,
                              total_blogs=total_blogs,
                              total_pages=total_pages,
                              current_page=page)
    except Exception as e:
        flash(f'Error loading blog list: {str(e)}', 'danger')
        return redirect(url_for('admin.index'))

@admin_bp.route('/blogs/create', methods=['GET', 'POST'])
@admin_required
def create_blog():
    """Create a new blog post"""
    from ..services.blog_service import BlogService
    blog_service = BlogService()
    
    if request.method == 'POST':
        try:
            # Validate request data
            title = request.form.get('title')
            content = request.form.get('content')
            tags = request.form.get('tags', '')
            tags_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            is_published = 'is_published' in request.form
            
            # Handle featured image
            featured_image = None
            if 'featured_image' in request.files and request.files['featured_image'].filename:
                image_file = request.files['featured_image']

                # Validate file type
                if not allowed_file(image_file.filename):
                    flash('فرمت فایل تصویر مجاز نیست. فقط PNG, JPG, JPEG, GIF, WEBP پذیرفته می‌شود.', 'danger')
                    return render_template('admin/blog/create.html')

                # Create uploads directory if it doesn't exist
                upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'blog')
                os.makedirs(upload_dir, exist_ok=True)

                # Generate secure filename
                filename = secure_filename(image_file.filename)
                file_extension = os.path.splitext(filename)[1]
                unique_filename = f"{uuid.uuid4()}{file_extension}"

                # Save the file
                file_path = os.path.join(upload_dir, unique_filename)
                image_file.save(file_path)

                # Store relative path for database
                featured_image = f"uploads/blog/{unique_filename}"
            
            # Create blog
            blog = blog_service.create_blog(
                user_id=current_user.id,
                title=title,
                content=content,
                tags=tags_list,
                featured_image=featured_image,
                is_published=is_published
            )
            
            flash(translate('auth.blog_created_success'), 'success')
            return redirect(url_for('admin.blogs'))

        except Exception as e:
            flash(f'{translate("auth.blog_creation_error")}: {str(e)}', 'danger')
    
    return render_template('admin/blog/create.html')

@admin_bp.route('/blogs/edit/<blog_id>', methods=['GET', 'POST'])
@admin_required
def edit_blog(blog_id):
    """Edit a blog post"""
    from ..models import Blog
    from ..services.blog_service import BlogService
    blog_service = BlogService()
    
    try:
        blog = Blog.objects.get(id=blog_id)
        
        if request.method == 'POST':
            try:
                # Get form data
                title = request.form.get('title')
                content = request.form.get('content')
                tags = request.form.get('tags', '')
                tags_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                is_published = 'is_published' in request.form
                
                # Handle featured image
                featured_image = blog.featured_image
                if 'featured_image' in request.files and request.files['featured_image'].filename:
                    image_file = request.files['featured_image']

                    # Validate file type
                    if not allowed_file(image_file.filename):
                        flash('فرمت فایل تصویر مجاز نیست. فقط PNG, JPG, JPEG, GIF, WEBP پذیرفته می‌شود.', 'danger')
                        return render_template('admin/blog/edit.html', blog=blog)

                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'blog')
                    os.makedirs(upload_dir, exist_ok=True)

                    # Generate secure filename
                    filename = secure_filename(image_file.filename)
                    file_extension = os.path.splitext(filename)[1]
                    unique_filename = f"{uuid.uuid4()}{file_extension}"

                    # Save the file
                    file_path = os.path.join(upload_dir, unique_filename)
                    image_file.save(file_path)

                    # Store relative path for database
                    featured_image = f"uploads/blog/{unique_filename}"

                    # Optionally delete old image file
                    if blog.featured_image and blog.featured_image.startswith('uploads/'):
                        old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'],
                                                   blog.featured_image.replace('uploads/', ''))
                        if os.path.exists(old_file_path):
                            try:
                                os.remove(old_file_path)
                            except OSError:
                                pass  # Ignore if file can't be deleted
                
                # Update blog
                updated_blog = blog_service.update_blog(
                    blog_id=blog_id,
                    title=title,
                    content=content,
                    tags=tags_list,
                    featured_image=featured_image,
                    is_published=is_published
                )
                
                flash(translate('auth.blog_updated_success'), 'success')
                return redirect(url_for('admin.blogs'))

            except Exception as e:
                flash(f'{translate("auth.blog_update_error")}: {str(e)}', 'danger')
        
        return render_template('admin/blog/edit.html', blog=blog)
    except Exception as e:
        flash(f'Error editing blog: {str(e)}', 'danger')
        return redirect(url_for('admin.blogs'))

@admin_bp.route('/blogs/delete/<blog_id>', methods=['POST'])
@admin_required
def delete_blog(blog_id):
    """Delete a blog post"""
    from ..services.blog_service import BlogService
    blog_service = BlogService()
    
    try:
        # Delete blog
        blog_service.delete_blog(blog_id)
        
        flash(translate('auth.blog_deleted_success'), 'success')
        return redirect(url_for('admin.blogs'))
    except Exception as e:
        flash(f'{translate("auth.blog_delete_error")}: {str(e)}', 'danger')
        return redirect(url_for('admin.blogs'))

# Helper function for mock data
def get_mock_posts():
    """Get mock posts data for development"""
    return [
        {
            'id': 1,
            'title': 'معرفی محصول جدید',
            'content': 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
            'platform': 'instagram',
            'published': True,
            'scheduled': False,
            'publish_date': datetime.now(),
            'likes': 120,
            'comments': 14,
            'image': 'https://via.placeholder.com/300'
        },
        {
            'id': 2,
            'title': 'اطلاعیه مهم',
            'content': 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
            'platform': 'telegram',
            'published': False,
            'scheduled': True,
            'publish_date': datetime.now() + timedelta(days=2),
            'likes': 0,
            'comments': 0,
            'image': None
        },
        {
            'id': 3,
            'title': 'نکات مهم',
            'content': 'لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است.',
            'platform': 'twitter',
            'published': True,
            'scheduled': False,
            'publish_date': datetime.now(),
            'likes': 50,
            'comments': 10,
            'image': 'https://via.placeholder.com/300'
        }
    ]


# ============================================================================
# EMAIL MANAGEMENT ROUTES
# ============================================================================

logger = logging.getLogger(__name__)

@admin_bp.route('/email/templates-test')
@admin_required
def email_templates_test():
    """Simple email templates test page"""
    try:
        templates = EmailTemplateService.list_templates()
        logger.info(f"Test page: Found {len(templates)} email templates")
        return render_template('admin/email/templates.html', templates=templates)
    except Exception as e:
        logger.error(f'Error in test page: {str(e)}')
        return f"<h1>Error: {str(e)}</h1><p>Check logs for details</p>"

@admin_bp.route('/email/templates')
@admin_required
def email_templates():
    """Email templates management page"""
    try:
        # Initialize system templates if they don't exist
        try:
            EmailTemplateService.initialize_system_templates()
            logger.info("System templates initialized successfully")
        except Exception as init_error:
            logger.warning(f"Template initialization warning: {str(init_error)}")
            flash(f'Template initialization warning: {str(init_error)}', 'warning')

        # Get templates
        templates = EmailTemplateService.list_templates()
        logger.info(f"Found {len(templates)} email templates")

        # Debug: Print template details
        if templates:
            for template in templates:
                logger.info(f"Template: {template.template_key} - {template.name} - Active: {template.is_active}")
        else:
            logger.warning("No templates found in database")
            flash('No email templates found. System templates should be automatically created.', 'info')

        return render_template('admin/email/templates.html', templates=templates)
    except Exception as e:
        logger.error(f'Error loading email templates: {str(e)}')
        flash(f'Error loading templates: {str(e)}', 'error')
        return redirect(url_for('admin.index'))

@admin_bp.route('/email/templates/create', methods=['GET', 'POST'])
@admin_required
def email_template_create():
    """Create new email template"""
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form

            template = EmailTemplateService.create_template(
                template_key=data['template_key'],
                name=data['name'],
                description=data.get('description'),
                category=data['category'],
                subjects=data['subjects'],
                html_content=data['html_content'],
                text_content=data.get('text_content', {}),
                variables=data.get('variables', []),
                supported_languages=data.get('supported_languages', ['en', 'fa']),
                default_language=data.get('default_language', 'en'),
                created_by=current_user
            )

            log_event(
                level=LogLevel.INFO,
                category=LogCategory.SYSTEM,
                message=f"Email template created: {template.template_key}",
                user_id=str(current_user.id)
            )

            if request.is_json:
                return jsonify({'success': True, 'template': template.to_dict()})
            else:
                flash('Template created successfully', 'success')
                return redirect(url_for('admin.email_templates'))

        except Exception as e:
            logger.error(f"Failed to create email template: {str(e)}")
            if request.is_json:
                return jsonify({'success': False, 'error': str(e)}), 400
            else:
                flash(f'Error creating template: {str(e)}', 'error')

    return render_template('admin/email/template_form.html')

@admin_bp.route('/email/templates/<template_id>/edit', methods=['GET', 'POST'])
@admin_required
def email_template_edit(template_id):
    """Edit email template"""
    template = EmailTemplateService.get_template_by_id(template_id)
    if not template:
        flash('Template not found', 'error')
        return redirect(url_for('admin.email_templates'))

    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form

            # Update template
            update_data = {}
            for field in ['name', 'description', 'category', 'subjects', 'html_content', 'text_content', 'variables']:
                if field in data:
                    update_data[field] = data[field]

            updated_template = EmailTemplateService.update_template(template_id, **update_data)

            log_event(
                level=LogLevel.INFO,
                category=LogCategory.SYSTEM,
                message=f"Email template updated: {updated_template.template_key}",
                user_id=str(current_user.id)
            )

            if request.is_json:
                return jsonify({'success': True, 'template': updated_template.to_dict()})
            else:
                flash('Template updated successfully', 'success')
                return redirect(url_for('admin.email_templates'))

        except Exception as e:
            logger.error(f"Failed to update email template: {str(e)}")
            if request.is_json:
                return jsonify({'success': False, 'error': str(e)}), 400
            else:
                flash(f'Error updating template: {str(e)}', 'error')

    return render_template('admin/email/template_form.html', template=template)

@admin_bp.route('/email/templates/<template_id>/delete', methods=['POST'])
@admin_required
def email_template_delete(template_id):
    """Delete email template"""
    try:
        EmailTemplateService.delete_template(template_id)

        log_event(
            level=LogLevel.INFO,
            category=LogCategory.SYSTEM,
            message=f"Email template deleted: {template_id}",
            user_id=str(current_user.id)
        )

        if request.is_json:
            return jsonify({'success': True})
        else:
            flash('Template deleted successfully', 'success')
            return redirect(url_for('admin.email_templates'))

    except Exception as e:
        logger.error(f"Failed to delete email template: {str(e)}")
        if request.is_json:
            return jsonify({'success': False, 'error': str(e)}), 400
        else:
            flash(f'Error deleting template: {str(e)}', 'error')
            return redirect(url_for('admin.email_templates'))

@admin_bp.route('/email/templates/<template_id>/preview')
@admin_required
def email_template_preview(template_id):
    """Preview email template"""
    try:
        template = EmailTemplateService.get_template_by_id(template_id)
        if not template:
            return jsonify({'error': 'Template not found'}), 404

        language = request.args.get('language', 'en')
        content = template.get_content(language)

        # Sample variables for preview
        sample_variables = {
            'user_name': 'John Doe',
            'site_name': 'Rominext',
            'reset_link': 'https://example.com/reset',
            'current_year': datetime.now().year
        }

        # Render with sample data
        email_service = EmailService()
        subject = email_service._render_template(content['subject'], sample_variables)
        html_content = email_service._render_template(content['html_content'], sample_variables)

        return jsonify({
            'subject': subject,
            'html_content': html_content,
            'variables': template.variables
        })

    except Exception as e:
        logger.error(f"Failed to preview template: {str(e)}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/email/analytics')
@admin_required
def email_analytics():
    """Email analytics dashboard"""
    try:
        # Get date range from query params
        days = int(request.args.get('days', 30))
        start_date = datetime.utcnow() - timedelta(days=days)
        end_date = datetime.utcnow()

        # Get overall stats
        overall_stats = EmailAnalyticsService.get_email_stats(start_date, end_date)

        # Get template performance
        template_performance = EmailAnalyticsService.get_template_performance(start_date, end_date)

        # Get daily stats
        daily_stats = EmailAnalyticsService.get_daily_email_stats(start_date, end_date)

        # Get queue stats
        queue_stats = email_queue_service.get_queue_stats()

        return render_template('admin/email/analytics.html',
                             overall_stats=overall_stats,
                             template_performance=template_performance,
                             daily_stats=daily_stats,
                             queue_stats=queue_stats,
                             days=days)

    except Exception as e:
        logger.error(f"Failed to load email analytics: {str(e)}")
        flash(f'Error loading analytics: {str(e)}', 'error')
        return redirect(url_for('admin.index'))

@admin_bp.route('/email/test', methods=['GET', 'POST'])
@admin_required
def email_test():
    """Test email sending"""
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form

            email_service = EmailService()

            if data.get('test_type') == 'smtp':
                # Test SMTP connection
                result = email_service.test_smtp_connection()
            else:
                # Send test email
                result = email_service.send_email(
                    to_email=data['to_email'],
                    subject=data.get('subject', 'Test Email from Rominext'),
                    html_content=data.get('html_content', '<h1>Test Email</h1><p>This is a test email from Rominext.</p>'),
                    text_content=data.get('text_content', 'Test Email\n\nThis is a test email from Rominext.')
                )

            if request.is_json:
                return jsonify(result)
            else:
                if result['success']:
                    flash('Test email sent successfully', 'success')
                else:
                    flash(f'Test failed: {result.get("error")}', 'error')
                return redirect(url_for('admin.email_test'))

        except Exception as e:
            logger.error(f"Email test failed: {str(e)}")
            if request.is_json:
                return jsonify({'success': False, 'error': str(e)}), 500
            else:
                flash(f'Test failed: {str(e)}', 'error')

    # Get current settings for display
    from ..config import get_settings
    settings = get_settings()

    return render_template('admin/email/test.html', config=settings)

@admin_bp.route('/email/quick-test')
@admin_required
def email_quick_test():
    """Quick email test with predefined settings"""
    try:
        email_service = EmailService()

        # Send a quick test email to the configured test address
        result = email_service.send_email(
            to_email="<EMAIL>",
            subject="🎉 Rominext Email System - Quick Test",
            html_content="""
            <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center;">
                    <h1>🎉 Quick Test Successful!</h1>
                    <p style="font-size: 18px;">Your Rominext email system is working!</p>
                </div>

                <div style="padding: 30px; background-color: #f8f9fa; border-radius: 10px; margin-top: 20px;">
                    <h2 style="color: #333;">Configuration Details:</h2>
                    <ul style="color: #666; line-height: 1.6;">
                        <li><strong>SMTP Host:</strong> mailpanel.bertina.us</li>
                        <li><strong>From:</strong> <EMAIL></li>
                        <li><strong>Test Time:</strong> {}</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 30px; padding: 20px; border-top: 1px solid #eee;">
                    <p style="color: #888;">Email sent from Rominext Admin Panel</p>
                    <p style="color: #888;">🚀 System is ready!</p>
                </div>
            </body>
            </html>
            """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            text_content=f"""
🎉 Rominext Email System - Quick Test

Your Rominext email system is working!

Configuration Details:
- SMTP Host: mailpanel.bertina.us
- From: <EMAIL>
- Test Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

Email sent from Rominext Admin Panel
🚀 System is ready!
            """,
            to_name="Ehsan Ghanbari"
        )

        if result['success']:
            flash(f'✅ Quick test email sent successfully! Check <EMAIL>', 'success')
        else:
            flash(f'❌ Quick test failed: {result.get("error")}', 'error')

    except Exception as e:
        flash(f'❌ Quick test error: {str(e)}', 'error')

    return redirect(url_for('admin.email_test'))

# API routes for email
@admin_bp.route('/email/api/send', methods=['POST'])
@admin_required
def email_api_send():
    """API endpoint to send email"""
    try:
        data = request.get_json()

        if not data.get('template_key') or not data.get('to_email'):
            return jsonify({'error': 'template_key and to_email are required'}), 400

        # Queue email for sending
        queue_id = email_queue_service.queue_email(
            template_key=data['template_key'],
            to_email=data['to_email'],
            variables=data.get('variables', {}),
            language=data.get('language', 'en'),
            to_name=data.get('to_name'),
            user_id=str(current_user.id),
            priority=data.get('priority', 2)
        )

        return jsonify({
            'success': True,
            'queue_id': queue_id,
            'message': 'Email queued for sending'
        })

    except Exception as e:
        logger.error(f"API send email failed: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Force template initialization route
@admin_bp.route('/email/init-templates')
@admin_required
def email_init_templates():
    """Force initialize email templates"""
    try:
        from ..models.email_template import EmailTemplate

        # Clear existing templates (for testing)
        # EmailTemplate.objects.delete()  # Uncomment if you want to reset

        # Force initialize
        EmailTemplateService.initialize_system_templates()

        # Count templates
        total_templates = EmailTemplate.objects.count()
        templates = EmailTemplate.objects.all()

        template_list = []
        for template in templates:
            template_list.append({
                'key': template.template_key,
                'name': template.name,
                'category': template.category,
                'active': template.is_active,
                'system': template.is_system_template
            })

        flash(f'✅ Templates initialized! Found {total_templates} templates.', 'success')
        return redirect(url_for('admin.email_templates'))

    except Exception as e:
        flash(f'❌ Template initialization failed: {str(e)}', 'error')
        return redirect(url_for('admin.email_templates'))

# Debug route for email system
@admin_bp.route('/email/debug')
@admin_required
def email_debug():
    """Debug email system"""
    try:
        debug_info = {
            'database_connection': 'Unknown',
            'templates_count': 0,
            'templates_list': [],
            'system_templates_initialized': False,
            'error_messages': []
        }

        # Test database connection
        try:
            from ..models.email_template import EmailTemplate
            total_templates = EmailTemplate.objects.count()
            debug_info['database_connection'] = 'Connected'
            debug_info['templates_count'] = total_templates

            # Get all templates
            templates = EmailTemplate.objects.all()
            debug_info['templates_list'] = [
                {
                    'id': str(t.id),
                    'template_key': t.template_key,
                    'name': t.name,
                    'category': t.category,
                    'is_active': t.is_active,
                    'is_system_template': t.is_system_template,
                    'supported_languages': t.supported_languages
                }
                for t in templates
            ]

        except Exception as db_error:
            debug_info['database_connection'] = f'Error: {str(db_error)}'
            debug_info['error_messages'].append(f'Database error: {str(db_error)}')

        # Test template initialization
        try:
            EmailTemplateService.initialize_system_templates()
            debug_info['system_templates_initialized'] = True
        except Exception as init_error:
            debug_info['system_templates_initialized'] = False
            debug_info['error_messages'].append(f'Template initialization error: {str(init_error)}')

        return jsonify(debug_info)

    except Exception as e:
        return jsonify({'error': f'Debug route error: {str(e)}'}), 500

# Simple test route
@admin_bp.route('/email/simple-test')
@admin_required
def email_simple_test():
    """Very simple test route"""
    try:
        # Create fake template data for testing
        fake_templates = [
            {
                'id': '1',
                'name': 'Test Template 1',
                'template_key': 'test1',
                'category': 'test',
                'is_active': True,
                'is_system_template': True,
                'supported_languages': ['en', 'fa'],
                'description': 'Test template 1'
            },
            {
                'id': '2',
                'name': 'Test Template 2',
                'template_key': 'test2',
                'category': 'test',
                'is_active': True,
                'is_system_template': False,
                'supported_languages': ['en'],
                'description': 'Test template 2'
            }
        ]

        return render_template('admin/email/templates.html', templates=fake_templates)

    except Exception as e:
        return f"<h1>Simple Test Error</h1><p>{str(e)}</p><pre>{traceback.format_exc()}</pre>"

# Quick email test route
@admin_bp.route('/email/quick-send-test')
@admin_required
def email_quick_send_test():
    """Quick test to send an actual email"""
    try:
        from ..services.email_service import EmailService

        email_service = EmailService()

        # Send a simple test email
        result = email_service.send_email(
            to_email="<EMAIL>",
            subject="🚀 Rominext Email Test - " + datetime.now().strftime("%H:%M:%S"),
            html_content="""
            <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h1>✅ Email System Working!</h1>
                    <p>This email was sent successfully from the Rominext admin panel.</p>
                </div>

                <div style="padding: 20px; background-color: #f8f9fa; border-radius: 10px; margin-top: 20px;">
                    <h3>Test Details:</h3>
                    <ul>
                        <li><strong>Time:</strong> {}</li>
                        <li><strong>SMTP Host:</strong> mailpanel.bertina.us</li>
                        <li><strong>From:</strong> <EMAIL></li>
                        <li><strong>Status:</strong> Email system is operational</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px; padding: 15px; border-top: 1px solid #ddd;">
                    <p style="color: #666;">Sent from Rominext Email System</p>
                </div>
            </body>
            </html>
            """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            text_content=f"""
✅ Email System Working!

This email was sent successfully from the Rominext admin panel.

Test Details:
- Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- SMTP Host: mailpanel.bertina.us
- From: <EMAIL>
- Status: Email system is operational

Sent from Rominext Email System
            """,
            to_name="Ehsan Ghanbari"
        )

        if result['success']:
            flash(f'✅ Test email sent successfully! Tracking ID: {result["tracking_id"]}', 'success')
            return redirect(url_for('admin.email_test'))
        else:
            flash(f'❌ Email sending failed: {result.get("error")}', 'error')
            return redirect(url_for('admin.email_test'))

    except Exception as e:
        flash(f'❌ Email test error: {str(e)}', 'error')
        return redirect(url_for('admin.email_test'))


# ============================================================================
# SYSTEM SETTINGS ROUTES
# ============================================================================

@admin_bp.route('/settings')
@admin_required
def settings():
    """Main settings page - redirects to general settings"""
    return redirect(url_for('admin.settings_general'))

@admin_bp.route('/settings/general', methods=['GET', 'POST'])
@admin_required
def settings_general():
    """General system settings"""
    from ..services.system_setting_service import SystemSettingService

    if request.method == 'POST':
        try:
            # Update general settings
            settings_to_update = [
                ('site_name', request.form.get('site_name', 'Rominext')),
                ('site_description', request.form.get('site_description', 'AI-Powered Social Media Management')),
                ('site_url', request.form.get('site_url', 'https://rominext.com')),
                ('admin_email', request.form.get('admin_email', '<EMAIL>')),
                ('default_language', request.form.get('default_language', 'en')),
                ('default_timezone', request.form.get('default_timezone', 'UTC')),
                ('maintenance_mode', 'true' if request.form.get('maintenance_mode') else 'false'),
                ('registration_enabled', 'true' if request.form.get('registration_enabled') else 'false'),
            ]

            for key, value in settings_to_update:
                SystemSettingService.set_setting(key, str(value))

            flash('General settings updated successfully', 'success')
            return redirect(url_for('admin.settings_general'))

        except Exception as e:
            flash(f'Error updating settings: {str(e)}', 'danger')

    # Get current settings
    settings = {}
    setting_keys = ['site_name', 'site_description', 'site_url', 'admin_email',
                   'default_language', 'default_timezone', 'maintenance_mode', 'registration_enabled']

    for key in setting_keys:
        setting = SystemSettingService.get_setting(key)
        settings[key] = setting.value if setting else ''

    return render_template('admin/settings/general.html', settings=settings)

@admin_bp.route('/settings/email', methods=['GET', 'POST'])
@admin_required
def settings_email():
    """Email settings"""
    from ..services.system_setting_service import SystemSettingService

    if request.method == 'POST':
        try:
            # Update email settings
            settings_to_update = [
                ('smtp_host', request.form.get('smtp_host', '')),
                ('smtp_port', request.form.get('smtp_port', '587')),
                ('smtp_username', request.form.get('smtp_username', '')),
                ('smtp_password', request.form.get('smtp_password', '')),
                ('smtp_use_tls', 'true' if request.form.get('smtp_use_tls') else 'false'),
                ('smtp_use_ssl', 'true' if request.form.get('smtp_use_ssl') else 'false'),
                ('email_from_name', request.form.get('email_from_name', 'Rominext')),
                ('email_from_address', request.form.get('email_from_address', '')),
                ('email_reply_to', request.form.get('email_reply_to', '')),
            ]

            for key, value in settings_to_update:
                SystemSettingService.set_setting(key, str(value))

            flash('Email settings updated successfully', 'success')
            return redirect(url_for('admin.settings_email'))

        except Exception as e:
            flash(f'Error updating email settings: {str(e)}', 'danger')

    # Get current settings
    settings = {}
    setting_keys = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password',
                   'smtp_use_tls', 'smtp_use_ssl', 'email_from_name', 'email_from_address', 'email_reply_to']

    for key in setting_keys:
        setting = SystemSettingService.get_setting(key)
        settings[key] = setting.value if setting else ''

    return render_template('admin/settings/email.html', settings=settings)

@admin_bp.route('/settings/api', methods=['GET', 'POST'])
@admin_required
def settings_api():
    """API settings"""
    from ..services.system_setting_service import SystemSettingService

    if request.method == 'POST':
        try:
            # Update API settings
            settings_to_update = [
                ('facebook_page_access_token', request.form.get('facebook_page_access_token', '')),
                ('facebook_page_id', request.form.get('facebook_page_id', '')),
                ('groq_api_key', request.form.get('groq_api_key', '')),
                ('openai_api_key', request.form.get('openai_api_key', '')),
                ('deepseek_api_key', request.form.get('deepseek_api_key', '')),
                ('grok_api_key', request.form.get('grok_api_key', '')),
                ('huggingface_api_key', request.form.get('huggingface_api_key', '')),
                ('together_api_key', request.form.get('together_api_key', '')),
                ('stability_api_key', request.form.get('stability_api_key', '')),
                ('deepinfra_api_key', request.form.get('deepinfra_api_key', '')),
                ('telegram_bot_token', request.form.get('telegram_bot_token', '')),
                ('default_ai_provider', request.form.get('default_ai_provider', 'openai')),
            ]

            for key, value in settings_to_update:
                SystemSettingService.set_setting(key, str(value))

            flash('API settings updated successfully', 'success')
            return redirect(url_for('admin.settings_api'))

        except Exception as e:
            flash(f'Error updating API settings: {str(e)}', 'danger')

    # Get current settings
    settings = {}
    setting_keys = ['facebook_page_access_token', 'facebook_page_id', 'groq_api_key', 'openai_api_key',
                   'deepseek_api_key', 'grok_api_key', 'huggingface_api_key', 'together_api_key',
                   'stability_api_key', 'deepinfra_api_key', 'telegram_bot_token', 'default_ai_provider']

    for key in setting_keys:
        setting = SystemSettingService.get_setting(key)
        settings[key] = setting.value if setting else ''

    return render_template('admin/settings/api.html', settings=settings)

@admin_bp.route('/settings/security', methods=['GET', 'POST'])
@admin_required
def settings_security():
    """Security settings"""
    from ..services.system_setting_service import SystemSettingService

    if request.method == 'POST':
        try:
            # Update security settings
            settings_to_update = [
                ('password_min_length', request.form.get('password_min_length', '8')),
                ('session_timeout', request.form.get('session_timeout', '3600')),
                ('max_login_attempts', request.form.get('max_login_attempts', '5')),
                ('lockout_duration', request.form.get('lockout_duration', '900')),
                ('enable_two_factor_auth', 'true' if request.form.get('enable_two_factor_auth') else 'false'),
                ('require_email_verification', 'true' if request.form.get('require_email_verification') else 'false'),
                ('password_complexity', 'true' if request.form.get('password_complexity') else 'false'),
                ('enable_captcha', 'true' if request.form.get('enable_captcha') else 'false'),
            ]

            for key, value in settings_to_update:
                SystemSettingService.set_setting(key, str(value))

            flash('Security settings updated successfully', 'success')
            return redirect(url_for('admin.settings_security'))

        except Exception as e:
            flash(f'Error updating security settings: {str(e)}', 'danger')

    # Get current settings
    settings = {}
    setting_keys = ['password_min_length', 'session_timeout', 'max_login_attempts', 'lockout_duration',
                   'enable_two_factor_auth', 'require_email_verification', 'password_complexity', 'enable_captcha']

    for key in setting_keys:
        setting = SystemSettingService.get_setting(key)
        settings[key] = setting.value if setting else ''

    return render_template('admin/settings/security.html', settings=settings)

@admin_bp.route('/settings/system', methods=['GET', 'POST'])
@admin_required
def settings_system():
    """System settings"""
    from ..services.system_setting_service import SystemSettingService

    if request.method == 'POST':
        try:
            # Update system settings
            settings_to_update = [
                ('max_posts_per_user', request.form.get('max_posts_per_user', '1000')),
                ('max_accounts_per_user', request.form.get('max_accounts_per_user', '10')),
                ('max_file_upload_size', request.form.get('max_file_upload_size', '********')),
                ('allowed_file_types', request.form.get('allowed_file_types', 'jpg,jpeg,png,gif,mp4,mov')),
                ('enable_analytics', 'true' if request.form.get('enable_analytics') else 'false'),
                ('enable_scheduling', 'true' if request.form.get('enable_scheduling') else 'false'),
                ('enable_ai_generation', 'true' if request.form.get('enable_ai_generation') else 'false'),
                ('backup_retention_days', request.form.get('backup_retention_days', '30')),
                ('log_retention_days', request.form.get('log_retention_days', '90')),
                ('debug_mode', 'true' if request.form.get('debug_mode') else 'false'),
            ]

            for key, value in settings_to_update:
                SystemSettingService.set_setting(key, str(value))

            flash('System settings updated successfully', 'success')
            return redirect(url_for('admin.settings_system'))

        except Exception as e:
            flash(f'Error updating system settings: {str(e)}', 'danger')

    # Get current settings
    settings = {}
    setting_keys = ['max_posts_per_user', 'max_accounts_per_user', 'max_file_upload_size', 'allowed_file_types',
                   'enable_analytics', 'enable_scheduling', 'enable_ai_generation', 'backup_retention_days',
                   'log_retention_days', 'debug_mode']

    for key in setting_keys:
        setting = SystemSettingService.get_setting(key)
        settings[key] = setting.value if setting else ''

    return render_template('admin/settings/system.html', settings=settings)

@admin_bp.route('/settings/database', methods=['GET', 'POST'])
@admin_required
def settings_database():
    """Database settings and maintenance"""
    from ..services.system_setting_service import SystemSettingService

    if request.method == 'POST':
        try:
            # Update database settings
            settings_to_update = [
                ('db_connection_pool_size', request.form.get('db_connection_pool_size', '10')),
                ('db_query_timeout', request.form.get('db_query_timeout', '30')),
                ('enable_db_logging', 'true' if request.form.get('enable_db_logging') else 'false'),
                ('auto_backup_enabled', 'true' if request.form.get('auto_backup_enabled') else 'false'),
                ('backup_frequency', request.form.get('backup_frequency', 'daily')),
            ]

            for key, value in settings_to_update:
                SystemSettingService.set_setting(key, str(value))

            flash('Database settings updated successfully', 'success')
            return redirect(url_for('admin.settings_database'))

        except Exception as e:
            flash(f'Error updating database settings: {str(e)}', 'danger')

    # Get current settings
    settings = {}
    setting_keys = ['db_connection_pool_size', 'db_query_timeout', 'enable_db_logging',
                   'auto_backup_enabled', 'backup_frequency']

    for key in setting_keys:
        setting = SystemSettingService.get_setting(key)
        settings[key] = setting.value if setting else ''

    # Get database statistics
    try:
        from ..models.user import User
        from ..models.post import Post
        from ..models.log import Log

        db_stats = {
            'users_count': User.objects.count(),
            'posts_count': Post.objects.count() if hasattr(Post, 'objects') else 0,
            'logs_count': Log.objects.count() if hasattr(Log, 'objects') else 0,
        }
    except Exception:
        db_stats = {'users_count': 0, 'posts_count': 0, 'logs_count': 0}

    return render_template('admin/settings/database.html', settings=settings, db_stats=db_stats)

@admin_bp.route('/settings/initialize', methods=['POST'])
@admin_required
def settings_initialize():
    """Initialize default system settings"""
    from ..services.system_setting_service import SystemSettingService

    try:
        # Initialize default settings
        default_settings = [
            ('site_name', 'Rominext', 'Site name'),
            ('site_description', 'AI-Powered Social Media Management', 'Site description'),
            ('site_url', 'https://rominext.com', 'Site URL'),
            ('admin_email', '<EMAIL>', 'Administrator email'),
            ('default_language', 'en', 'Default language'),
            ('default_timezone', 'UTC', 'Default timezone'),
            ('maintenance_mode', 'false', 'Maintenance mode'),
            ('registration_enabled', 'true', 'Registration enabled'),
            ('password_min_length', '8', 'Minimum password length'),
            ('session_timeout', '3600', 'Session timeout in seconds'),
            ('max_login_attempts', '5', 'Maximum login attempts'),
            ('lockout_duration', '900', 'Account lockout duration in seconds'),
            ('enable_two_factor_auth', 'false', 'Enable two-factor authentication'),
            ('require_email_verification', 'true', 'Require email verification'),
            ('password_complexity', 'false', 'Require complex passwords'),
            ('enable_captcha', 'false', 'Enable CAPTCHA'),
            ('max_posts_per_user', '1000', 'Maximum posts per user'),
            ('max_accounts_per_user', '10', 'Maximum accounts per user'),
            ('max_file_upload_size', '********', 'Maximum file upload size in bytes'),
            ('allowed_file_types', 'jpg,jpeg,png,gif,mp4,mov', 'Allowed file types'),
            ('enable_analytics', 'true', 'Enable analytics'),
            ('enable_scheduling', 'true', 'Enable post scheduling'),
            ('enable_ai_generation', 'true', 'Enable AI content generation'),
            ('backup_retention_days', '30', 'Backup retention days'),
            ('log_retention_days', '90', 'Log retention days'),
            ('debug_mode', 'false', 'Debug mode'),
            ('db_connection_pool_size', '10', 'Database connection pool size'),
            ('db_query_timeout', '30', 'Database query timeout'),
            ('enable_db_logging', 'false', 'Enable database logging'),
            ('auto_backup_enabled', 'false', 'Auto backup enabled'),
            ('backup_frequency', 'daily', 'Backup frequency'),
        ]

        initialized_count = 0
        for key, value, description in default_settings:
            existing = SystemSettingService.get_setting(key)
            if not existing:
                SystemSettingService.set_setting(key, value, description)
                initialized_count += 1

        flash(f'Initialized {initialized_count} default settings', 'success')

    except Exception as e:
        flash(f'Error initializing settings: {str(e)}', 'danger')

    return redirect(url_for('admin.settings_general'))
