import os
import requests
from .base_ai_provider import BaseAIProvider

class TogetherAIProvider(BaseAIProvider):
    """
    Concrete implementation of BaseAIProvider for Together.ai services.
    """

    def __init__(self, api_key=None, base_url="https://api.together.xyz/v1", cache=None):
        """
        Initialize Together.ai provider with API key and base URL.
        
        :param api_key: The Together.ai API key (optional, will use env var if not provided)
        :param base_url: The base URL for Together.ai API
        :param cache: Optional cache mechanism
        """
        self.api_key = api_key or os.getenv("TOGETHER_API_KEY")
        super().__init__(base_url=base_url, cache=cache)

    def authenticate(self):
        """
        Authenticate with Together.ai API.
        For Together.ai, we just need to store the API key.
        """
        if not self.api_key:
            import logging
            logging.warning("Together.ai API key is not set. Some functionality may be limited.")
        self.auth_token = self.api_key

    def get_headers(self):
        """
        Get headers for API requests.
        """
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        return headers

    def make_request(self, endpoint: str, payload: dict) -> dict:
        """
        Make a request to the Together.ai API.
        
        :param endpoint: The specific endpoint to call
        :param payload: The payload to send in the request
        :return: The raw response from the API
        """
        self.log_request(endpoint, payload)

        headers = self.get_headers()

        response = requests.post(
            url=self.base_url.rstrip("/") + "/" + endpoint.lstrip("/"),
            headers=headers,
            json=payload
        )

        if response.ok:
            response_data = response.json()
            self.cache_response(endpoint, response_data)
            return response_data
        else:
            self.handle_error(f"Error {response.status_code}: {response.text}")
            response.raise_for_status()

    def process_response(self, response: dict) -> dict:
        """
        Process the response from Together.ai API to extract generated content.
        """
        # Together.ai follows OpenAI-like response format
        if "choices" in response and len(response["choices"]) > 0:
            choice = response["choices"][0]
            
            # Handle chat completion response
            if "message" in choice and "content" in choice["message"]:
                return {"text": choice["message"]["content"], "raw_response": response}
            
            # Handle completion response
            elif "text" in choice:
                return {"text": choice["text"], "raw_response": response}
        
        # Return the raw response if we can't extract in a standard way
        return {"text": str(response), "raw_response": response}

    def generate_text(self, prompt: str, model: str = "mistralai/Mixtral-8x7B-Instruct-v0.1", max_tokens: int = 100) -> dict:
        """
        Generate text using Together.ai's completion models.
        """
        endpoint = "completions"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "max_tokens": max_tokens,
            "temperature": 0.7
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}

    def chat_completion(self, messages: list, model: str = "mistralai/Mixtral-8x7B-Instruct-v0.1", max_tokens: int = 150) -> dict:
        """
        Chat with Together.ai's chat models.
        """
        endpoint = "chat/completions"
        
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": 0.7
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
    
    def embeddings(self, text: str, model: str = "togethercomputer/m2-bert-80M-8k-retrieval") -> dict:
        """
        Generate embeddings for text using Together.ai's embedding models.
        """
        endpoint = "embeddings"
        
        payload = {
            "model": model,
            "input": text
        }
        
        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            
            # Extract embeddings from response
            if "data" in raw_response and len(raw_response["data"]) > 0:
                embedding = raw_response["data"][0]["embedding"]
                return {"embedding": embedding, "raw_response": raw_response}
            
            return {"error": "No embedding found in response", "raw_response": raw_response}
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
