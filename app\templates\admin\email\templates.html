{% extends "admin/admin_layout.html" %}

{% block title %}Email Templates - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Email Templates</h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.email_template_create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create New Template
                    </a>
                    <a href="{{ url_for('admin.email_init_templates') }}" class="btn btn-warning">
                        <i class="fas fa-sync me-2"></i>Initialize Templates
                    </a>
                    <a href="{{ url_for('admin.email_debug') }}" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-bug me-2"></i>Debug
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Email Templates</h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary" onclick="filterTemplates('all')">All</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="filterTemplates('system')">System</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="filterTemplates('custom')">Custom</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if templates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Key</th>
                                    <th>Category</th>
                                    <th>Languages</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for template in templates %}
                                <tr>
                                    <td>
                                        <strong>{{ template.name }}</strong>
                                        {% if template.description %}
                                        <br><small class="text-muted">{{ template.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ template.template_key }}</code>
                                        {% if template.is_system_template %}
                                        <span class="badge bg-info ms-1">System</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ template.category }}</span>
                                    </td>
                                    <td>
                                        {% for lang in template.supported_languages %}
                                        <span class="badge bg-light text-dark me-1">{{ lang.upper() }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% if template.is_active %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if template.created_at %}
                                        <small>{{ template.created_at.strftime('%Y-%m-%d') }}</small>
                                        {% else %}
                                        <small class="text-muted">Unknown</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('admin.email_template_edit', template_id=template.id) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-info" onclick="previewTemplate('{{ template.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if not template.is_system_template %}
                                            <button class="btn btn-outline-danger" onclick="deleteTemplate('{{ template.id }}', '{{ template.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <h5>No Email Templates Found</h5>
                        <p class="text-muted">Templates variable is empty or None.</p>
                        <div class="btn-group">
                            <a href="{{ url_for('admin.email_init_templates') }}" class="btn btn-warning">
                                <i class="fas fa-sync me-2"></i>Initialize Templates
                            </a>
                            <a href="{{ url_for('admin.email_template_create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Template
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterTemplates(type) {
    const rows = document.querySelectorAll('tbody tr');
    const buttons = document.querySelectorAll('.btn-group-sm .btn');
    
    // Update active button
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    rows.forEach(row => {
        const isSystem = row.querySelector('.badge.bg-info');
        
        switch(type) {
            case 'system':
                row.style.display = isSystem ? '' : 'none';
                break;
            case 'custom':
                row.style.display = !isSystem ? '' : 'none';
                break;
            default:
                row.style.display = '';
        }
    });
}

function previewTemplate(templateId) {
    // Open preview in new window/modal
    window.open(`{{ url_for('admin.email_template_preview', template_id='TEMPLATE_ID') }}`.replace('TEMPLATE_ID', templateId), 
                '_blank', 'width=800,height=600');
}

function deleteTemplate(templateId, templateName) {
    if (confirm(`Are you sure you want to delete the template "${templateName}"?`)) {
        fetch(`{{ url_for('admin.email_template_delete', template_id='TEMPLATE_ID') }}`.replace('TEMPLATE_ID', templateId), {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete template');
        });
    }
}
</script>
{% endblock %}
