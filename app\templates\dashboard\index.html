{% extends "dashboard/dashboard_layout.html" %}

{% block title %}Dashboard - Rominext{% endblock %}

{% block dashboard_content %}
<!-- Modern Welcome Banner -->
<div class="welcome-banner mb-4 p-4 rounded shadow-sm w-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-right: 4px solid #4dabf7;">
    <div class="d-flex align-items-center">
        <div class="welcome-icon me-3 d-none d-md-block">
            <i class="fas fa-robot fa-2x text-primary"></i>
        </div>
        <div>
            <h2 class="mb-1 fw-bold">خوش آمدید، {{ current_user.name }}</h2>
            <p class="text-muted mb-0">دستیار محتوای هوشمند شما آماده کمک به تعامل با مخاطبان شماست.</p>
        </div>
    </div>
</div>

<!-- Modern Quick Actions -->
<div class="quick-actions mb-4 p-0 rounded shadow-sm overflow-hidden">
    <div class="p-3" style="background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);">
        <h5 class="mb-3 fw-bold"><i class="fas fa-bolt me-2 text-primary"></i>دسترسی سریع</h5>
    </div>
    <div class="p-3 bg-white">
        <div class="row g-2">
            <div class="col-md-3 col-6">
                <a href="{{ url_for('dashboard.quick_post') }}" class="action-card p-3 rounded text-center d-block" style="background: #f8f9fa; transition: all 0.3s ease;">
                    <i class="fas fa-bolt mb-2 fa-lg text-primary"></i>
                    <div>تولید فوری محتوا</div>
                </a>
            </div>
            <div class="col-md-3 col-6">
                <a href="{{ url_for('dashboard.create_post') }}" class="action-card p-3 rounded text-center d-block" style="background: #f8f9fa; transition: all 0.3s ease;">
                    <i class="fas fa-edit mb-2 fa-lg text-success"></i>
                    <div>تولید پیشرفته محتوا</div>
                </a>
            </div>
            <div class="col-md-3 col-6">
                <a href="{{ url_for('strategy.index') }}" class="action-card p-3 rounded text-center d-block" style="background: #f8f9fa; transition: all 0.3s ease;">
                    <i class="fas fa-chess mb-2 fa-lg text-info"></i>
                    <div>استراتژی</div>
                </a>
            </div>
            <div class="col-md-3 col-6">
                <a href="{{ url_for('comments.monitor') }}" class="action-card p-3 rounded text-center d-block" style="background: #f8f9fa; transition: all 0.3s ease;">
                    <i class="fas fa-comments mb-2 fa-lg text-warning"></i>
                    <div>کامنت‌ها</div>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Modern Stats Cards -->
    <div class="col-lg-8">
        <div class="row g-3">
            <div class="col-md-4">
                <div class="stat-card card border-0 rounded-4 shadow-sm overflow-hidden" style="min-height: 120px; background-color: rgba(77, 171, 247, 0.1);">
                    <div class="card-body p-0">
                        <div class="d-flex h-100">
                            <div class="p-3 flex-grow-1">
                                <div class="stat-label small text-muted mb-1">فعالیت‌های هوش مصنوعی</div>
                                <h3 class="stat-value mb-0 fw-bold">{{ stats.total_comments|default(0) }}</h3>
                            </div>
                            <div class="stat-icon-container d-flex align-items-center justify-content-center p-3">
                                <i class="fas fa-robot fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card card border-0 rounded-4 shadow-sm overflow-hidden" style="min-height: 120px; background-color: rgba(64, 201, 151, 0.1);">
                    <div class="card-body p-0">
                        <div class="d-flex h-100">
                            <div class="p-3 flex-grow-1">
                                <div class="stat-label small text-muted mb-1">تعاملات</div>
                                <h3 class="stat-value mb-0 fw-bold">{{ stats.engagement_metrics.likes|default(0) }}</h3>
                            </div>
                            <div class="stat-icon-container d-flex align-items-center justify-content-center p-3">
                                <i class="fas fa-heart fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card card border-0 rounded-4 shadow-sm overflow-hidden" style="min-height: 120px; background-color: rgba(255, 193, 7, 0.1);">
                    <div class="card-body p-0">
                        <div class="d-flex h-100">
                            <div class="p-3 flex-grow-1">
                                <div class="stat-label small text-muted mb-1">پست‌های امروز</div>
                                <h3 class="stat-value mb-0 fw-bold">{{ stats.today_posts|default(0) }}</h3>
                            </div>
                            <div class="stat-icon-container d-flex align-items-center justify-content-center p-3">
                                <i class="fas fa-file-alt fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Post Card -->
        <div class="card mt-4 border-0 rounded-4 shadow-sm overflow-hidden">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex align-items-center">
                    <div class="feature-icon me-3 rounded-circle p-2" style="background-color: rgba(77, 171, 247, 0.1);">
                        <i class="fas fa-star text-primary"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">پست برتر</h5>
                </div>
            </div>
            <div class="card-body">
                {% if posts and posts|length > 0 %}
                    {% set top_post = posts|sort(attribute='engagement_score', reverse=True)|first %}
                    <div class="top-post p-3 rounded" style="background-color: #f8f9fa;">
                        <p class="card-text mb-3">{{ top_post.message }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="post-meta">
                                <span class="badge rounded-pill bg-light text-dark me-2">
                                    <i class="fas fa-calendar-alt text-muted me-1"></i> {{ top_post.created_time|default('') }}
                                </span>
                                <span class="badge rounded-pill bg-light text-dark">
                                    <i class="fas fa-chart-line text-success me-1"></i> امتیاز: {{ top_post.engagement_score|default(0) }}
                                </span>
                            </div>
                            <div class="engagement-stats">
                                <span class="badge rounded-pill bg-primary bg-opacity-10 text-primary me-2">
                                    <i class="fas fa-heart me-1"></i> {{ top_post.likes|default(0) }}
                                </span>
                                <span class="badge rounded-pill bg-info bg-opacity-10 text-info me-2">
                                    <i class="fas fa-comment me-1"></i> {{ top_post.comments|default(0) }}
                                </span>
                                <span class="badge rounded-pill bg-success bg-opacity-10 text-success">
                                    <i class="fas fa-share me-1"></i> {{ top_post.shares|default(0) }}
                                </span>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <div class="empty-state-icon mb-3">
                            <i class="fas fa-file-alt fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted">هنوز پستی موجود نیست. اولین پست خود را ایجاد کنید تا معیارهای عملکرد را ببینید.</p>
                        <a href="{{ url_for('dashboard.create_post') }}" class="btn btn-sm btn-primary mt-2">
                            <i class="fas fa-plus me-1"></i> ایجاد پست جدید
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Recent Posts Section -->
        <div class="card mt-4 border-0 rounded-4 shadow-sm overflow-hidden">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex align-items-center">
                    <div class="feature-icon me-3 rounded-circle p-2" style="background-color: rgba(111, 66, 193, 0.1);">
                        <i class="fas fa-file-alt text-purple"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">پست‌های اخیر</h5>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    {% if posts and posts|length > 0 %}
                        {% for post in posts[:3] %}
                        <div class="list-group-item border-0 py-3">
                            <p class="mb-2">{{ post.message }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ post.created_time|default('') }}</small>
                                <div>
                                    <span class="badge rounded-pill bg-primary bg-opacity-10 text-primary me-1">
                                        <i class="fas fa-heart me-1"></i> {{ post.likes|default(0) }}
                                    </span>
                                    <span class="badge rounded-pill bg-info bg-opacity-10 text-info">
                                        <i class="fas fa-comment me-1"></i> {{ post.comments|default(0) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">پستی برای نمایش وجود ندارد</p>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-white border-0 text-center py-3">
                    <a href="{{ url_for('dashboard.posts_list') }}" class="btn btn-sm btn-outline-primary">
                        مشاهده همه پست‌ها
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Post Suggestion Card -->
        <div class="card border-0 rounded-4 shadow-sm overflow-hidden mb-4">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex align-items-center">
                    <div class="feature-icon me-3 rounded-circle p-2" style="background-color: rgba(255, 193, 7, 0.1);">
                        <i class="fas fa-lightbulb text-warning"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">پیشنهاد پست</h5>
                </div>
            </div>
            <div class="card-body">
                {% if post_suggestions and post_suggestions|length > 0 %}
                    <div class="suggestion-box p-3 rounded mb-3" style="background-color: #f8f9fa;">
                        <p class="suggestion-text mb-0">{{ post_suggestions[0].message }}</p>
                    </div>
                    <button class="btn btn-primary w-100 use-suggestion" 
                            data-message="{{ post_suggestions[0].message }}">
                        <i class="fas fa-magic me-1"></i> استفاده از این پست
                    </button>
                {% else %}
                    <div class="text-center py-4">
                        <div class="empty-state-icon mb-3">
                            <i class="fas fa-lightbulb fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted">در حال حاضر پیشنهادی موجود نیست.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Activity Summary Card -->
        <div class="card border-0 rounded-4 shadow-sm overflow-hidden">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex align-items-center">
                    <div class="feature-icon me-3 rounded-circle p-2" style="background-color: rgba(64, 201, 151, 0.1);">
                        <i class="fas fa-history text-success"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">فعالیت‌های اخیر</h5>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    {% if activity_summary and activity_summary.activities %}
                        {% for activity in activity_summary.activities %}
                        {% if loop.index <= 5 %}
                        <div class="list-group-item border-0 py-3">
                            <div class="d-flex">
                                <div class="activity-icon me-3 rounded-circle p-2" style="background-color: rgba({{ {'primary': '77, 171, 247', 'success': '64, 201, 151', 'warning': '255, 193, 7', 'info': '23, 162, 184', 'danger': '220, 53, 69'}[activity.color|default('primary')] }}, 0.1);">
                                    <i class="fas fa-{{ activity.icon|default('check') }} text-{{ activity.color|default('primary') }}"></i>
                                </div>
                                <div>
                                    <p class="mb-0">{{ activity.text }}</p>
                                    <small class="text-muted">{{ activity.time }}</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">فعالیتی برای نمایش وجود ندارد</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Hover effects for action cards
        document.querySelectorAll('.action-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1)';
                this.style.backgroundColor = '#ffffff';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
                this.style.backgroundColor = '#f8f9fa';
            });
        });
        
        // Use suggestion button
        document.querySelectorAll('.use-suggestion').forEach(button => {
            button.addEventListener('click', function() {
                const message = this.getAttribute('data-message');
                window.location.href = "{{ url_for('dashboard.create_post') }}?message=" + encodeURIComponent(message);
            });
        });
    });
</script>
{% endblock %}

{% block styles %}
<style>
/* Modern Dashboard Styles */
.welcome-banner {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-right: 4px solid #4dabf7;
    text-align: right;
    direction: rtl;
}

.stat-card {
    transition: all 0.3s ease;
    height: 100%;
    border: none !important;
}

.stat-card .card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.stat-card .d-flex {
    flex: 1;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

.stat-icon-container {
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-card {
    transition: all 0.3s ease;
    text-decoration: none;
    color: #212529;
}

.feature-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-state-icon {
    opacity: 0.5;
}

.suggestion-box {
    border-right: 3px solid #ffc107;
}

.top-post {
    border-right: 3px solid #4dabf7;
}

.text-purple {
    color: #6f42c1;
}

.rounded-4 {
    border-radius: 0.75rem !important;
}

/* RTL specific adjustments */
.card-header, .card-body {
    text-align: right;
    direction: rtl;
}

.me-1, .me-2, .me-3 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.me-2 {
    margin-left: 0.5rem !important;
}

.me-3 {
    margin-left: 1rem !important;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .stat-card {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}
