#!/usr/bin/env python3
"""
Test email database connection and template initialization
"""
import os
import sys
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_database_connection():
    """Test database connection"""
    print("🔌 Testing database connection...")
    
    try:
        from app import create_app
        from app.models.email_template import EmailTemplate
        from app.services.email_template_service import EmailTemplateService
        
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            # Test basic database connection
            try:
                total_templates = EmailTemplate.objects.count()
                print(f"✅ Database connection successful!")
                print(f"📊 Found {total_templates} email templates in database")
                
                # List all templates
                templates = EmailTemplate.objects.all()
                if templates:
                    print("\n📋 Existing templates:")
                    for template in templates:
                        print(f"   - {template.template_key}: {template.name} ({template.category})")
                else:
                    print("📋 No templates found in database")
                
                return True
                
            except Exception as db_error:
                print(f"❌ Database connection failed: {str(db_error)}")
                return False
                
    except Exception as e:
        print(f"❌ Application setup error: {str(e)}")
        return False

def test_template_initialization():
    """Test template initialization"""
    print("\n📝 Testing template initialization...")
    
    try:
        from app import create_app
        from app.services.email_template_service import EmailTemplateService
        
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            try:
                # Initialize system templates
                EmailTemplateService.initialize_system_templates()
                print("✅ System templates initialized successfully!")
                
                # Check templates after initialization
                templates = EmailTemplateService.list_templates()
                print(f"📊 Total templates after initialization: {len(templates)}")
                
                if templates:
                    print("\n📋 Available templates:")
                    for template in templates:
                        status = "✅ Active" if template.is_active else "❌ Inactive"
                        system = "🔧 System" if template.is_system_template else "👤 User"
                        print(f"   - {template.template_key}: {template.name}")
                        print(f"     Category: {template.category} | {status} | {system}")
                        print(f"     Languages: {', '.join(template.supported_languages)}")
                        print()
                
                return True
                
            except Exception as init_error:
                print(f"❌ Template initialization failed: {str(init_error)}")
                return False
                
    except Exception as e:
        print(f"❌ Template initialization error: {str(e)}")
        return False

def test_template_service():
    """Test template service methods"""
    print("\n🔧 Testing template service methods...")
    
    try:
        from app import create_app
        from app.services.email_template_service import EmailTemplateService
        
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            try:
                # Test list_templates method
                all_templates = EmailTemplateService.list_templates()
                print(f"✅ list_templates() returned {len(all_templates)} templates")
                
                # Test with different filters
                system_templates = EmailTemplateService.list_templates(include_system=True)
                user_templates = EmailTemplateService.list_templates(include_system=False)
                
                print(f"📊 System templates: {len([t for t in system_templates if t.is_system_template])}")
                print(f"📊 User templates: {len([t for t in user_templates if not t.is_system_template])}")
                
                # Test specific template retrieval
                welcome_template = EmailTemplateService.get_template('welcome')
                if welcome_template:
                    print(f"✅ Found welcome template: {welcome_template.name}")
                    print(f"   Subjects: {list(welcome_template.subjects.keys())}")
                    print(f"   Content languages: {list(welcome_template.html_content.keys())}")
                else:
                    print("❌ Welcome template not found")
                
                return True
                
            except Exception as service_error:
                print(f"❌ Template service error: {str(service_error)}")
                return False
                
    except Exception as e:
        print(f"❌ Template service test error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Email System Database Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    if test_database_connection():
        tests_passed += 1
    
    if test_template_initialization():
        tests_passed += 1
    
    if test_template_service():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Email database system is working correctly!")
        print("\n💡 If templates still don't show in the admin panel:")
        print("   1. Check the admin panel URL: /admin/email/templates")
        print("   2. Check the debug URL: /admin/email/debug")
        print("   3. Check browser console for JavaScript errors")
        print("   4. Verify admin user permissions")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Ensure MongoDB is running")
        print("   2. Check database connection settings")
        print("   3. Verify Flask app configuration")
        print("   4. Check for import errors")

if __name__ == "__main__":
    main()
