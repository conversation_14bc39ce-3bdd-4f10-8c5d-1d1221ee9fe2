{% extends "dashboard/dashboard_layout.html" %}

{% block title %}تولید فوری محتوا - Rominext{% endblock %}

{% block dashboard_content %}
<div id="quickContentGenerator"></div>

<script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
<script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
<script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

<script type="text/babel">
  function QuickContentGenerator() {
    const [formData, setFormData] = React.useState({
      platform: 'instagram',
      subject: ''
    });
    
    const [loading, setLoading] = React.useState(false);
    const [generatedContent, setGeneratedContent] = React.useState(null);
    const [editedContent, setEditedContent] = React.useState(null);
    
    const handleInputChange = (e) => {
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));
    };
    
    const handlePlatformSelect = (platform) => {
      setFormData(prev => ({ ...prev, platform }));
      setGeneratedContent(null);
      setEditedContent(null);
    };
    
    const handleContentEdit = (e) => {
      const { name, value } = e.target;
      setEditedContent(prev => ({ ...prev, [name]: value }));
    };
    
    const generateContent = async (e) => {
      e.preventDefault();
      setLoading(true);
      
      try {
        // Replace with actual API call
        const response = await fetch('/api/posts/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            platform: formData.platform,
            subject: formData.subject
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to generate content');
        }
        
        const data = await response.json();
        setGeneratedContent(data);
        setEditedContent(data); // Initialize edited content with generated content
      } catch (error) {
        console.error('Error generating content:', error);
        alert('Failed to generate content. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    const publishContent = async () => {
      if (!editedContent) return;
      
      try {
        // Replace with actual publish API call
        const response = await fetch('/api/posts/publish', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            platform: formData.platform,
            content: editedContent
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to publish content');
        }
        
        alert('محتوا با موفقیت منتشر شد!');
      } catch (error) {
        console.error('Error publishing content:', error);
        alert('Failed to publish content. Please try again.');
      }
    };
    
    const renderEditablePreview = () => {
      if (!editedContent) return null;
      
      if (formData.platform === 'instagram') {
        return (
          <div className="post-preview instagram-preview">
            <div className="preview-header">
              <h6><i className="fab fa-instagram me-2"></i>Instagram Preview</h6>
            </div>
            <div className="preview-image">
              <div className="image-placeholder">
                <i className="far fa-image"></i>
                <textarea
                  className="form-control mt-2"
                  name="imageIdea"
                  value={editedContent.imageIdea}
                  onChange={handleContentEdit}
                  rows="2"
                ></textarea>
              </div>
            </div>
            <div className="preview-content">
              <div className="mb-3">
                <label className="form-label">متن پست</label>
                <textarea
                  className="form-control"
                  name="text"
                  value={editedContent.text}
                  onChange={handleContentEdit}
                  rows="4"
                ></textarea>
              </div>
              <div className="mb-3">
                <label className="form-label">هشتگ‌ها</label>
                <textarea
                  className="form-control text-primary"
                  name="hashtags"
                  value={editedContent.hashtags}
                  onChange={handleContentEdit}
                  rows="2"
                ></textarea>
              </div>
              <div className="mb-3">
                <label className="form-label">دعوت به اقدام</label>
                <textarea
                  className="form-control fw-bold"
                  name="callToAction"
                  value={editedContent.callToAction}
                  onChange={handleContentEdit}
                  rows="2"
                ></textarea>
              </div>
            </div>
          </div>
        );
      } else if (formData.platform === 'telegram') {
        return (
          <div className="post-preview telegram-preview">
            <div className="preview-header">
              <h6><i className="fab fa-telegram-plane me-2"></i>Telegram Preview</h6>
            </div>
            <div className="preview-image">
              <div className="image-placeholder">
                <i className="far fa-image"></i>
                <textarea
                  className="form-control mt-2"
                  name="imageIdea"
                  value={editedContent.imageIdea}
                  onChange={handleContentEdit}
                  rows="2"
                ></textarea>
              </div>
            </div>
            <div className="preview-content">
              <div className="mb-3">
                <label className="form-label">متن پست</label>
                <textarea
                  className="form-control"
                  name="text"
                  value={editedContent.text}
                  onChange={handleContentEdit}
                  rows="4"
                ></textarea>
              </div>
              <div className="mb-3">
                <label className="form-label">دعوت به اقدام</label>
                <textarea
                  className="form-control fw-bold"
                  name="callToAction"
                  value={editedContent.callToAction}
                  onChange={handleContentEdit}
                  rows="2"
                ></textarea>
              </div>
            </div>
          </div>
        );
      }
      
      return null;
    };
    
    return (
      <div className="container-fluid">
        <div className="row">
          <div className="col-md-6">
            <div className="card mb-4">
              <div className="card-body">
                <h5 className="card-title mb-3">تولید فوری محتوا</h5>
                
                <form onSubmit={generateContent}>
                  <div className="mb-3">
                    <label className="form-label">پلتفرم</label>
                    <div className="d-flex flex-wrap gap-2 mb-3">
                      <button 
                        type="button"
                        className={`btn platform-btn ${formData.platform === 'instagram' ? 'active' : ''}`}
                        onClick={() => handlePlatformSelect('instagram')}
                      >
                        <i className="fab fa-instagram ms-2"></i> اینستاگرام
                      </button>
                      <button 
                        type="button"
                        className={`btn platform-btn ${formData.platform === 'telegram' ? 'active' : ''}`}
                        onClick={() => handlePlatformSelect('telegram')}
                      >
                        <i className="fab fa-telegram ms-2"></i> تلگرام
                      </button>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <label className="form-label">موضوع پست</label>
                    <textarea 
                      className="form-control" 
                      name="subject" 
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="موضوع پست را توضیح دهید..."
                      rows="4"
                      required
                    ></textarea>
                    <small className="form-text text-muted">
                      توضیح دهید درباره چه موضوعی می‌خواهید پست ایجاد کنید.
                    </small>
                  </div>
                  
                  <button 
                    type="submit" 
                    className="btn btn-primary w-100" 
                    disabled={loading || !formData.subject}
                  >
                    {loading ? (
                      <><span className="spinner-border spinner-border-sm ms-2"></span> در حال تولید...</>
                    ) : (
                      <><i className="fas fa-magic ms-2"></i> تولید محتوا</>
                    )}
                  </button>
                </form>
              </div>
            </div>
          </div>
          
          <div className="col-md-6">
            {editedContent && !loading ? (
              <div className="card mb-4">
                <div className="card-body">
                  <h5 className="card-title mb-3">ویرایش و پیش‌نمایش محتوا</h5>
                  {renderEditablePreview()}
                  
                  <div className="mt-3">
                    <button 
                      className="btn btn-outline-primary me-2"
                      onClick={() => {
                        const content = `${editedContent.text}\n\n${editedContent.hashtags || ''}\n\n${editedContent.callToAction}`;
                        navigator.clipboard.writeText(content);
                        alert('محتوا کپی شد!');
                      }}
                    >
                      <i className="far fa-copy ms-2"></i> کپی متن
                    </button>
                    
                    <button 
                      className="btn btn-outline-secondary me-2"
                      onClick={() => {
                        setEditedContent(generatedContent);
                      }}
                    >
                      <i className="fas fa-undo ms-2"></i> بازگرداندن به حالت اولیه
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="card mb-4">
                <div className="card-body text-center py-5">
                  <i className="fas fa-magic fa-3x mb-3 text-muted"></i>
                  <h5 className="text-muted">موضوع پست را وارد کنید و دکمه تولید محتوا را بزنید</h5>
                  <p className="text-muted">پیش‌نمایش محتوای تولید شده اینجا نمایش داده می‌شود</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {editedContent && (
          <div className="row">
            <div className="col-12">
              <div className="card mb-4">
                <div className="card-body">
                  <h5 className="card-title mb-3">انتشار محتوا</h5>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">تاریخ انتشار</label>
                        <input type="date" className="form-control" />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">زمان انتشار</label>
                        <input type="time" className="form-control" />
                      </div>
                    </div>
                  </div>
                  <div className="d-flex justify-content-end gap-2">
                    <button 
                      className="btn btn-success"
                      onClick={publishContent}
                    >
                      <i className="fas fa-paper-plane ms-2"></i> انتشار محتوا
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
  
  const root = ReactDOM.createRoot(document.getElementById('quickContentGenerator'));
  root.render(<QuickContentGenerator />);
</script>

<style>
  .post-preview {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
  }
  
  .preview-header {
    padding: 10px 15px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
  }
  
  .preview-header h6 {
    margin: 0;
  }
  
  .preview-image {
    height: 200px;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .image-placeholder {
    text-align: center;
    padding: 20px;
  }
  
  .image-placeholder i {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 10px;
  }
  
  .preview-content {
    padding: 15px;
  }
  
  .instagram-preview .preview-header {
    background-color: #f8f9fa;
  }
  
  .telegram-preview .preview-header {
    background-color: #f8f9fa;
  }
  
  .platform-btn {
    border: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 8px 16px;
    transition: all 0.2s;
  }
  
  .platform-btn.active {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
  }
  
  .platform-btn:hover:not(.active) {
    background-color: #e9ecef;
  }
</style>
{% endblock %}


