from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import logging
import threading
from queue import Queue
from ..models.log import Log, LogLevel, LogCategory
from ..models.user import User

logger = logging.getLogger(__name__)

class LogService:
    """Service for managing application logs"""
    
    # Queue for asynchronous logging
    _log_queue = Queue()
    _worker_thread = None
    _running = False
    
    @classmethod
    def start_worker(cls):
        """Start the background worker thread for processing logs"""
        if cls._worker_thread is None or not cls._worker_thread.is_alive():
            # Clear any existing errors before starting
            cls._running = True
            cls._worker_thread = threading.Thread(target=cls._process_log_queue, daemon=True)
            cls._worker_thread.start()
            logger.info("Log service worker thread started")
            
            # Add a health check after a short delay to ensure thread started properly
            def check_worker_health():
                import time
                time.sleep(2)
                if not cls._worker_thread.is_alive():
                    logger.error("Log worker thread failed to start properly")
                    
            health_check = threading.Thread(target=check_worker_health, daemon=True)
            health_check.start()
    
    @classmethod
    def stop_worker(cls):
        """Stop the background worker thread"""
        cls._running = False
        if cls._worker_thread and cls._worker_thread.is_alive():
            cls._worker_thread.join(timeout=5.0)
            logger.info("Log service worker thread stopped")
    
    @classmethod
    def _process_log_queue(cls):
        """Process logs from the queue in background"""
        import queue
        
        while cls._running:
            try:
                log_data = cls._log_queue.get(block=True, timeout=1.0)
                if log_data:
                    cls._save_log(**log_data)
                cls._log_queue.task_done()
            except queue.Empty:
                # This is normal when queue is empty and timeout occurs
                # No need to log this as an error
                continue
            except Exception as e:
                # Only log actual errors, not the expected Empty exception
                import traceback
                error_details = traceback.format_exc()
                logger.error(f"Error processing log queue: {str(e)}\n{error_details}")
                
                # Add a small delay to prevent tight error loops
                import time
                time.sleep(0.5)
    
    @staticmethod
    def _save_log(
        level: LogLevel,
        category: LogCategory,
        message: str,
        details: Optional[str] = None,
        user_id: Optional[str] = None,
        source: Optional[str] = None
    ) -> Optional[Log]:
        """Internal method to save a log to the database"""
        try:
            log = Log(
                level=level,
                category=category,
                message=message,
                details=details,
                source=source
            )
            
            if user_id:
                # Check if user_id is valid before assigning
                from ..models.user import User
                user = User.objects(id=user_id).first()
                if user:
                    log.user = user
                else:
                    logger.warning(f"User with ID {user_id} not found when saving log")
            
            log.save()
            return log
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            # Use standard Python logging instead of trying to save to DB again
            logger.error(f"Error saving log to database: {str(e)}")
            logger.debug(error_details)
            return None
    
    @staticmethod
    def add_log(
        level: LogLevel,
        category: LogCategory,
        message: str,
        details: Optional[str] = None,
        user_id: Optional[str] = None,
        source: Optional[str] = None
    ) -> None:
        """
        Add a new log entry asynchronously
        
        Args:
            level: Log level
            category: Log category
            message: Log message
            details: Optional detailed information
            user_id: Optional user ID for user-related logs
            source: Optional source of the log
        """
        try:
            # Start worker if not already running
            if not LogService._worker_thread or not LogService._worker_thread.is_alive():
                LogService.start_worker()
                
            # Add to queue for async processing
            LogService._log_queue.put({
                'level': level,
                'category': category,
                'message': message,
                'details': details,
                'user_id': user_id,
                'source': source
            })
        except Exception as e:
            # Fall back to synchronous logging if queue fails
            logger.error(f"Error queuing log: {str(e)}")
            LogService._save_log(level, category, message, details, user_id, source)
    
    @staticmethod
    def get_logs(
        level: Optional[LogLevel] = None,
        category: Optional[LogCategory] = None,
        user_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        skip: int = 0
    ) -> List[Log]:
        """
        Get logs with optional filtering
        
        Args:
            level: Optional log level filter
            category: Optional log category filter
            user_id: Optional user ID filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Maximum number of logs to return
            skip: Number of logs to skip (for pagination)
            
        Returns:
            List of Log objects
        """
        try:
            query = {}
            
            if level:
                query['level'] = level
                
            if category:
                query['category'] = category
                
            if user_id:
                query['user'] = user_id
                
            if start_date:
                query['created_at__gte'] = start_date
                
            if end_date:
                query['created_at__lte'] = end_date
                
            return Log.objects(**query).order_by('-created_at').skip(skip).limit(limit)
        except Exception as e:
            logger.error(f"Error retrieving logs: {str(e)}")
            return []
