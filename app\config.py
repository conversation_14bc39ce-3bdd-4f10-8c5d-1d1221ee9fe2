try:
    from pydantic_settings import BaseSettings, SettingsConfigDict
    PYDANTIC_AVAILABLE = True
except ImportError:
    # Fallback for when pydantic_settings is not available
    PYDANTIC_AVAILABLE = False
    BaseSettings = object
    SettingsConfigDict = dict

from typing import Optional, Dict, Any, List, ClassVar
import os
from functools import lru_cache

# Try to load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv not available, skip loading .env file
    pass

class Settings:
    """Application settings"""
    def __init__(self):
        # API Keys
        self.FACEBOOK_PAGE_ACCESS_TOKEN = os.getenv("FACEBOOK_PAGE_ACCESS_TOKEN", "")
        self.FACEBOOK_PAGE_ID = os.getenv("FACEBOOK_PAGE_ID", "")
        self.GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
        self.OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
        self.DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
        self.GROK_API_KEY = os.getenv("GROK_API_KEY", "")
        self.HUGGINGFACE_API_KEY = os.getenv("HUGGINGFACE_API_KEY", "")
        self.TOGETHER_API_KEY = os.getenv("TOGETHER_API_KEY", "")
        self.STABILITY_API_KEY = os.getenv("STABILITY_API_KEY", "")
        self.DEEPINFRA_API_KEY = os.getenv("DEEPINFRA_API_KEY", "")
        self.TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN", "")

        # Email Settings
        self.SMTP_HOST = os.getenv("SMTP_HOST", "mailpanel.bertina.us")
        self.SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
        self.SMTP_USERNAME = os.getenv("SMTP_USERNAME", "pilardin")
        self.SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "vQD437vrc@7F@Y")
        self.SMTP_USE_TLS = os.getenv("SMTP_USE_TLS", "True").lower() == "true"
        self.SMTP_USE_SSL = os.getenv("SMTP_USE_SSL", "False").lower() == "true"
        self.EMAIL_FROM_NAME = os.getenv("EMAIL_FROM_NAME", "Rominext")
        self.EMAIL_FROM_ADDRESS = os.getenv("EMAIL_FROM_ADDRESS", "<EMAIL>")
        self.EMAIL_REPLY_TO = os.getenv("EMAIL_REPLY_TO", "")

        # Security Settings
        self.SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")

        # App Settings
        self.MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017/rominext")
        self.ENABLE_SWAGGER = os.getenv("ENABLE_SWAGGER", "False").lower() == "true"
        self.DEFAULT_AI_PROVIDER = os.getenv("DEFAULT_AI_PROVIDER", "openai")

        # MongoDB settings
        self.MONGODB_DB = os.getenv("MONGODB_DB", "rominext")
        self.MONGODB_HOST = os.getenv("MONGODB_HOST", "localhost")
        self.MONGODB_PORT = int(os.getenv("MONGODB_PORT", "27017"))
        self.MONGODB_USERNAME = os.getenv("MONGODB_USERNAME", "")
        self.MONGODB_PASSWORD = os.getenv("MONGODB_PASSWORD", "")

class TestConfig(Settings):
    """Test configuration settings"""
    def __init__(self):
        super().__init__()
        self.TESTING = True
        self.WTF_CSRF_ENABLED = False
        self.MONGODB_SETTINGS = {
            'db': 'test_db',
            'host': 'mongomock://localhost'
        }
        self.SECRET_KEY = 'test-secret-key'
        self.DEBUG = True

@lru_cache()
def get_settings() -> Settings:
    return Settings()



