from typing import Dict, Any, List, Optional
import logging
from ..integrations.social.base_social_connector import BaseSocialIntegration

logger = logging.getLogger(__name__)

class SocialConnectorManager:
    """
    Manager class for social media connectors.
    Allows selecting and using different social media connectors based on platform name.
    """
    
    def __init__(self, connectors: Dict[str, BaseSocialIntegration]):
        """
        Initialize with a dictionary of connectors.
        
        Args:
            connectors: Dictionary mapping platform names to connector instances
        """
        self.connectors = connectors
        self.default_connector_name = next(iter(connectors.keys())) if connectors else None
    
    def get_connector(self, platform_name: str) -> Optional[BaseSocialIntegration]:
        """
        Get a connector by platform name.
        
        Args:
            platform_name: Name of the social media platform
            
        Returns:
            Connector instance or None if not found
        """
        return self.connectors.get(platform_name.lower())
    
    def set_default_connector(self, platform_name: str) -> bool:
        """
        Set the default connector.
        
        Args:
            platform_name: Name of the social media platform
            
        Returns:
            True if successful, False if platform not found
        """
        if platform_name.lower() in self.connectors:
            self.default_connector_name = platform_name.lower()
            return True
        return False
    
    def get_default_connector(self) -> Optional[BaseSocialIntegration]:
        """
        Get the default connector.
        
        Returns:
            Default connector instance or None if not set
        """
        if not self.default_connector_name:
            return None
        return self.connectors.get(self.default_connector_name)
    
    def get_available_platforms(self) -> List[str]:
        """
        Get a list of available platform names.
        
        Returns:
            List of platform names
        """
        return list(self.connectors.keys())
    
    def execute_on_platform(self, platform_name: str, method_name: str, *args, **kwargs) -> Any:
        """
        Execute a method on a specific platform connector.
        
        Args:
            platform_name: Name of the social media platform
            method_name: Name of the method to call
            *args: Positional arguments for the method
            **kwargs: Keyword arguments for the method
            
        Returns:
            Result from the method call
            
        Raises:
            ValueError: If platform not found or method not available
        """
        connector = self.get_connector(platform_name)
        if not connector:
            raise ValueError(f"Platform '{platform_name}' not found")
        
        if not hasattr(connector, method_name):
            raise ValueError(f"Method '{method_name}' not available for platform '{platform_name}'")
        
        method = getattr(connector, method_name)
        return method(*args, **kwargs)

# Features routes moved from features.py
# @landing_bp.route('/features/')
# @landing_bp.route('/features')
# def features():
#     """Display the main features page."""
#     lang = session.get('lang', 'fa')
#     return render_template('landing/features.html', lang=lang)

# @landing_bp.route('/features/content')
# def features_content():
#     """Display the content creation features page."""
#     lang = session.get('lang', 'fa')
#     return render_template('landing/features_content.html', lang=lang)




















