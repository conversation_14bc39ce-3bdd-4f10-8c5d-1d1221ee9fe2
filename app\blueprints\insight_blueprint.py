from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from marshmallow import Schema, fields, ValidationError
from datetime import datetime
import logging
from ..services.insight_service import InsightService

logger = logging.getLogger(__name__)
insight_bp = Blueprint('insight', __name__, url_prefix='/api/insight')
insight_service = InsightService()

# Validation schemas
class ABTestSchema(Schema):
    post_a_id = fields.String(required=True)
    post_b_id = fields.String(required=True)
    start_date = fields.DateTime(required=True)
    end_date = fields.DateTime(required=True)

@insight_bp.route('/post/<string:post_id>/performance', methods=['GET'])
@login_required
def get_post_performance(post_id):
    """
    Get performance metrics for a specific post.
    
    Args:
        post_id: The ID of the post to analyze
        
    Returns:
        JSON response with performance metrics
    """
    try:
        performance_data = insight_service.get_post_performance(post_id)
        return jsonify({"success": True, "data": performance_data}), 200
    except ValueError as e:
        logger.error(f"Error getting post performance: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 404
    except Exception as e:
        logger.error(f"Unexpected error getting post performance: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500

@insight_bp.route('/post/<string:post_id>/sentiment', methods=['GET'])
@login_required
def analyze_comment_sentiment(post_id):
    """
    Get sentiment analysis of comments for a specific post.
    
    Args:
        post_id: The ID of the post to analyze comments for
        
    Returns:
        JSON response with comment sentiment data
    """
    try:
        sentiment_data = insight_service.analyze_comment_sentiment(post_id)
        return jsonify({"success": True, "data": sentiment_data}), 200
    except ValueError as e:
        logger.error(f"Error analyzing comment sentiment: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 404
    except Exception as e:
        logger.error(f"Unexpected error analyzing comment sentiment: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500

@insight_bp.route('/abtest/create', methods=['POST'])
@login_required
def create_ab_test():
    """
    Create a new A/B test experiment.
    
    Request body:
        post_a_id: The ID of the first post to test
        post_b_id: The ID of the second post to test
        start_date: When the test should start (ISO format)
        end_date: When the test should end (ISO format)
        
    Returns:
        JSON response with created experiment ID
    """
    try:
        # Validate request data
        schema = ABTestSchema()
        data = schema.load(request.json)
        
        # Create the experiment
        experiment_id = insight_service.create_ab_test(
            user_id=str(current_user.id),
            post_a_id=data['post_a_id'],
            post_b_id=data['post_b_id'],
            start_date=data['start_date'],
            end_date=data['end_date']
        )
        
        return jsonify({
            "success": True, 
            "data": {"experiment_id": experiment_id}
        }), 201
    except ValidationError as e:
        logger.error(f"Validation error creating AB test: {str(e)}")
        return jsonify({"success": False, "error": e.messages}), 400
    except ValueError as e:
        logger.error(f"Error creating AB test: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 400
    except Exception as e:
        logger.error(f"Unexpected error creating AB test: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500

@insight_bp.route('/abtest/<string:experiment_id>/result', methods=['GET'])
@login_required
def get_ab_test_result(experiment_id):
    """
    Get the result of an A/B test experiment.
    
    Args:
        experiment_id: The ID of the experiment
        
    Returns:
        JSON response with test results and analytics
    """
    try:
        result_data = insight_service.get_ab_test_result(experiment_id)
        return jsonify({"success": True, "data": result_data}), 200
    except ValueError as e:
        logger.error(f"Error getting AB test result: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 404
    except Exception as e:
        logger.error(f"Unexpected error getting AB test result: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500

@insight_bp.route('/audience/learn', methods=['POST'])
@login_required
def learn_audience_preferences():
    """
    Analyze user's post performance and update personalization profile.
    
    Returns:
        JSON response with updated personalization profile
    """
    try:
        profile_data = insight_service.learn_audience_preferences(str(current_user.id))
        return jsonify({"success": True, "data": profile_data}), 200
    except ValueError as e:
        logger.error(f"Error learning audience preferences: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 404
    except Exception as e:
        logger.error(f"Unexpected error learning audience preferences: {str(e)}")
        return jsonify({"success": False, "error": "An unexpected error occurred"}), 500