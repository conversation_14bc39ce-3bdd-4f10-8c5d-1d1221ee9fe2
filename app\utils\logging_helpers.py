from ..services.log_service import LogService
from ..models.log import LogLevel, LogCategory
import inspect
import os
from typing import Optional, Any

def log_event(
    level: LogLevel,
    category: LogCategory,
    message: str,
    details: Optional[str] = None,
    user_id: Optional[str] = None
):
    """
    Helper function to log events with automatic source tracking
    
    Args:
        level: Log level
        category: Log category
        message: Log message
        details: Optional detailed information
        user_id: Optional user ID for user-related logs
    """
    # Get the caller's frame
    frame = inspect.currentframe().f_back
    
    # Extract file name and line number
    filename = os.path.basename(frame.f_code.co_filename)
    line_number = frame.f_lineno
    function_name = frame.f_code.co_name
    
    # Create source string
    source = f"{filename}:{function_name}:{line_number}"
    
    # Log the event
    LogService.add_log(
        level=level,
        category=category,
        message=message,
        details=details,
        user_id=user_id,
        source=source
    )