import pytest
from app.models import Account, User, Platform, AccountStatus

class TestAccountModel:
    @pytest.fixture
    def user(self):
        """Create a test user"""
        user = User.create_user(
            email="<EMAIL>",
            password="password123",
            name="Test User"
        )
        yield user
        user.delete()
    
    def test_create_account(self, user):
        """Test creating an account"""
        account = Account(
            user=user,
            name="Business Instagram",
            platform=Platform.INSTAGRAM,
            status=AccountStatus.ACTIVE,
            credentials={
                "access_token": "test-token-123",
                "user_id": "********"
            },
            metadata={
                "followers": 1000,
                "following": 500
            }
        )
        account.save()
        
        assert account.user == user
        assert account.name == "Business Instagram"
        assert account.platform == Platform.INSTAGRAM
        assert account.status == AccountStatus.ACTIVE
        assert account.credentials.get("access_token") == "test-token-123"
        assert account.metadata.get("followers") == 1000
    
    def test_account_platforms(self, user):
        """Test different account platforms"""
        # Instagram account
        instagram = Account(
            user=user,
            name="Instagram Account",
            platform=Platform.INSTAGRAM,
            status=AccountStatus.ACTIVE
        )
        instagram.save()
        
        # Facebook account
        facebook = Account(
            user=user,
            name="Facebook Page",
            platform=Platform.FACEBOOK,
            status=AccountStatus.ACTIVE
        )
        facebook.save()
        
        # Twitter account
        twitter = Account(
            user=user,
            name="Twitter Profile",
            platform=Platform.TWITTER,
            status=AccountStatus.ACTIVE
        )
        twitter.save()
        
        # LinkedIn account
        linkedin = Account(
            user=user,
            name="LinkedIn Page",
            platform=Platform.LINKEDIN,
            status=AccountStatus.ACTIVE
        )
        linkedin.save()
        
        assert instagram.platform == Platform.INSTAGRAM
        assert facebook.platform == Platform.FACEBOOK
        assert twitter.platform == Platform.TWITTER
        assert linkedin.platform == Platform.LINKEDIN
    
    def test_account_status(self, user):
        """Test account status changes"""
        account = Account(
            user=user,
            name="Test Account",
            platform=Platform.INSTAGRAM,
            status=AccountStatus.ACTIVE
        )
        account.save()
        
        # Change status to disconnected
        account.status = AccountStatus.DISCONNECTED
        account.save()
        
        assert account.status == AccountStatus.DISCONNECTED
        
        # Change status to pending
        account.status = AccountStatus.PENDING
        account.save()
        
        assert account.status == AccountStatus.PENDING