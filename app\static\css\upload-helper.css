/**
 * Advanced File Upload Helper Styles
 * 
 * Provides modern, responsive styling for the file upload components
 */

.upload-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Dropzone Styles */
.upload-dropzone {
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    background: #f8fafc;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-dropzone:hover {
    border-color: #4299e1;
    background: #ebf8ff;
}

.upload-dropzone.drag-over {
    border-color: #3182ce;
    background: #bee3f8;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 48px;
    color: #a0aec0;
    margin-bottom: 16px;
    transition: color 0.3s ease;
}

.upload-dropzone:hover .upload-icon {
    color: #4299e1;
}

.upload-text h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
}

.upload-text p {
    margin: 0;
    color: #718096;
    font-size: 14px;
}

/* File List Styles */
.upload-files {
    margin-top: 24px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.upload-files h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.file-item.success {
    background: #f0fff4;
    border-color: #9ae6b4;
}

.file-item.error {
    background: #fed7d7;
    border-color: #fc8181;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    color: #2d3748;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-details {
    font-size: 12px;
    color: #718096;
    margin-top: 2px;
}

.file-progress {
    flex: 0 0 120px;
    margin: 0 16px;
}

.file-actions {
    flex: 0 0 auto;
}

/* Progress Bar Styles */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-bar.small {
    height: 4px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4299e1, #3182ce);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Upload Controls */
.upload-controls {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.upload-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.upload-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #4a5568;
}

.upload-options input[type="checkbox"] {
    margin: 0;
}

.upload-options input[type="text"] {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 14px;
    margin-left: 8px;
}

.upload-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* Upload Progress */
.upload-progress {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.upload-progress .progress-bar {
    margin-bottom: 12px;
    height: 12px;
}

.progress-text {
    font-size: 14px;
    color: #4a5568;
    font-weight: 500;
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #3182ce;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #e53e3e;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-container {
        padding: 16px;
    }
    
    .upload-dropzone {
        padding: 40px 16px;
    }
    
    .upload-icon {
        font-size: 36px;
    }
    
    .upload-text h3 {
        font-size: 18px;
    }
    
    .file-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .file-progress {
        flex: 1;
        margin: 0;
    }
    
    .upload-buttons {
        flex-direction: column;
    }
    
    .upload-options {
        gap: 16px;
    }
    
    .upload-options label {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .upload-options input[type="text"] {
        margin-left: 0;
        margin-top: 4px;
        width: 100%;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .upload-container {
        color: #e2e8f0;
    }
    
    .upload-dropzone {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .upload-dropzone:hover {
        background: #374151;
        border-color: #60a5fa;
    }
    
    .upload-files,
    .upload-controls,
    .upload-progress {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .file-item {
        background: #374151;
        border-color: #4a5568;
    }
    
    .upload-text h3,
    .upload-files h4,
    .file-name {
        color: #f7fafc;
    }
    
    .upload-text p,
    .file-details,
    .progress-text {
        color: #a0aec0;
    }
    
    .progress-bar {
        background: #4a5568;
    }
    
    .btn-secondary {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .btn-secondary:hover:not(:disabled) {
        background: #718096;
    }
}

/* Animation for file additions */
.file-item {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    border-top-color: #4299e1;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
