from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, DateTimeField, BooleanField, ListField, IntField
from datetime import datetime
from .user import User

class EmailTemplate(Document):
    """
    Email template model for storing multilingual email templates
    """
    # Template identification
    template_key = StringField(required=True, unique=True, max_length=100)  # e.g., 'password_reset', 'welcome'
    name = String<PERSON>ield(required=True, max_length=200)  # Human readable name
    description = StringField(max_length=500)
    category = StringField(required=True, max_length=50)  # e.g., 'auth', 'marketing', 'notification'
    
    # Template content (multilingual)
    subjects = DictField(required=True)  # {'en': 'Welcome!', 'fa': 'خوش آمدید!'}
    html_content = DictField(required=True)  # {'en': '<html>...', 'fa': '<html>...'}
    text_content = DictField()  # Plain text version
    
    # Template variables and metadata
    variables = ListField(StringField())  # List of template variables like ['user_name', 'reset_link']
    default_language = StringField(default='en', max_length=5)
    supported_languages = ListField(StringField(), default=['en', 'fa'])
    
    # Template settings
    is_active = BooleanField(default=True)
    is_system_template = BooleanField(default=False)  # System templates cannot be deleted
    
    # Ownership and timestamps
    created_by = ReferenceField(User)  # Null for system templates
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'email_templates',
        'indexes': [
            'template_key',
            'category',
            'is_active',
            'created_by'
        ]
    }
    
    def save(self, *args, **kwargs):
        """Override save to update timestamp"""
        self.updated_at = datetime.utcnow()
        return super().save(*args, **kwargs)
    
    def get_content(self, language='en'):
        """Get template content for specific language with fallback"""
        if language not in self.supported_languages:
            language = self.default_language
            
        return {
            'subject': self.subjects.get(language, self.subjects.get(self.default_language, '')),
            'html_content': self.html_content.get(language, self.html_content.get(self.default_language, '')),
            'text_content': self.text_content.get(language, self.text_content.get(self.default_language, ''))
        }
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': str(self.id),
            'template_key': self.template_key,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'subjects': self.subjects,
            'html_content': self.html_content,
            'text_content': self.text_content,
            'variables': self.variables,
            'default_language': self.default_language,
            'supported_languages': self.supported_languages,
            'is_active': self.is_active,
            'is_system_template': self.is_system_template,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class EmailLog(Document):
    """
    Email log model for tracking sent emails
    """
    # Email identification
    template_key = StringField(max_length=100)
    template_id = ReferenceField(EmailTemplate)
    
    # Recipient information
    recipient_email = StringField(required=True, max_length=255)
    recipient_name = StringField(max_length=255)
    recipient_user = ReferenceField(User)
    
    # Email content
    subject = StringField(required=True, max_length=500)
    language = StringField(default='en', max_length=5)
    
    # Sending details
    sender_email = StringField(required=True, max_length=255)
    sender_name = StringField(max_length=255)
    
    # Status and tracking
    status = StringField(required=True, choices=[
        'pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed'
    ], default='pending')
    
    # Timestamps
    created_at = DateTimeField(default=datetime.utcnow)
    sent_at = DateTimeField()
    delivered_at = DateTimeField()
    opened_at = DateTimeField()
    clicked_at = DateTimeField()
    
    # Error handling
    error_message = StringField()
    retry_count = IntField(default=0)
    max_retries = IntField(default=3)
    
    # Tracking data
    tracking_id = StringField(unique=True)  # Unique ID for tracking
    user_agent = StringField()
    ip_address = StringField()
    
    meta = {
        'collection': 'email_logs',
        'indexes': [
            'recipient_email',
            'template_key',
            'status',
            'created_at',
            'tracking_id'
        ]
    }
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': str(self.id),
            'template_key': self.template_key,
            'recipient_email': self.recipient_email,
            'recipient_name': self.recipient_name,
            'subject': self.subject,
            'language': self.language,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'delivered_at': self.delivered_at.isoformat() if self.delivered_at else None,
            'opened_at': self.opened_at.isoformat() if self.opened_at else None,
            'clicked_at': self.clicked_at.isoformat() if self.clicked_at else None,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }
