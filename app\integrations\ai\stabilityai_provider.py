import os
import requests
import base64
from io import BytesIO
from .base_ai_provider import BaseAIProvider

class StabilityAIProvider(BaseAIProvider):
    """
    Concrete implementation of BaseAIProvider for Stability AI services.
    """

    def __init__(self, api_key=None, base_url="https://api.stability.ai/v1", cache=None):
        """
        Initialize Stability AI provider with API key and base URL.
        
        :param api_key: The Stability AI API key (optional, will use env var if not provided)
        :param base_url: The base URL for Stability AI API
        :param cache: Optional cache mechanism
        """
        self.api_key = api_key or os.getenv("STABILITY_API_KEY")
        super().__init__(base_url=base_url, cache=cache)
        self.supported_tasks = ["image_generation", "image_to_image", "upscaling"]

    def authenticate(self):
        """
        Authenticate with Stability AI API.
        For Stability AI, we just need to store the API key.
        """
        if not self.api_key:
            import logging
            logging.warning("Stability AI API key is not set. Some functionality may be limited.")
        self.auth_token = self.api_key

    def get_headers(self):
        """
        Get headers for API requests.
        """
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        return headers

    def make_request(self, endpoint: str, payload: dict, files=None) -> dict:
        """
        Make a request to the Stability AI API.
        
        :param endpoint: The specific endpoint to call
        :param payload: The payload to send in the request
        :param files: Optional files to upload
        :return: The raw response from the API
        """
        self.log_request(endpoint, payload)

        headers = self.get_headers()
        url = self.base_url.rstrip("/") + "/" + endpoint.lstrip("/")

        if files:
            # For multipart/form-data requests (file uploads)
            # Remove Content-Type from headers as it will be set automatically
            if "Content-Type" in headers:
                del headers["Content-Type"]
            
            response = requests.post(
                url=url,
                headers=headers,
                data=payload,
                files=files
            )
        else:
            # For JSON requests
            response = requests.post(
                url=url,
                headers=headers,
                json=payload
            )

        if response.ok:
            # Check if response is JSON
            if response.headers.get("Content-Type", "").startswith("application/json"):
                response_data = response.json()
                self.cache_response(endpoint, response_data)
                return response_data
            else:
                # For binary responses (images)
                return {"binary_data": response.content}
        else:
            self.handle_error(f"Error {response.status_code}: {response.text}")
            response.raise_for_status()

    def process_response(self, response: dict) -> dict:
        """
        Process the response from Stability AI API to extract generated content.
        """
        # Handle binary data (images)
        if "binary_data" in response:
            # Convert binary data to base64 for easier handling
            base64_data = base64.b64encode(response["binary_data"]).decode("utf-8")
            return {"image_base64": base64_data}
        
        # Handle JSON responses
        if "artifacts" in response and len(response["artifacts"]) > 0:
            results = []
            for artifact in response["artifacts"]:
                if "base64" in artifact:
                    results.append({
                        "image_base64": artifact["base64"],
                        "seed": artifact.get("seed"),
                        "finish_reason": artifact.get("finishReason")
                    })
            return {"results": results, "raw_response": response}
        
        # Return the raw response if we can't extract in a standard way
        return {"raw_response": response}

    def generate_image(self, prompt: str, model: str = "stable-diffusion-xl-1024-v1-0", 
                      width: int = 1024, height: int = 1024, 
                      num_images: int = 1, cfg_scale: float = 7.0) -> dict:
        """
        Generate images using Stability AI's text-to-image models.
        
        :param prompt: Text prompt describing the desired image
        :param model: Model ID to use for generation
        :param width: Width of the generated image
        :param height: Height of the generated image
        :param num_images: Number of images to generate
        :param cfg_scale: How strictly the diffusion process adheres to the prompt
        :return: Dictionary containing generated images as base64 strings
        """
        endpoint = f"generation/{model}/text-to-image"
        
        payload = {
            "text_prompts": [{"text": prompt}],
            "cfg_scale": cfg_scale,
            "width": width,
            "height": height,
            "samples": num_images,
            "steps": 30
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}

    def image_to_image(self, prompt: str, image_data, model: str = "stable-diffusion-xl-1024-v1-0",
                      image_strength: float = 0.35, num_images: int = 1) -> dict:
        """
        Generate variations of an input image guided by a text prompt.
        
        :param prompt: Text prompt describing the desired modifications
        :param image_data: Input image as bytes or base64 string
        :param model: Model ID to use for generation
        :param image_strength: How much to transform the input image (0-1)
        :param num_images: Number of images to generate
        :return: Dictionary containing generated images as base64 strings
        """
        endpoint = f"generation/{model}/image-to-image"
        
        # Convert image to bytes if it's base64
        if isinstance(image_data, str):
            if image_data.startswith("data:image"):
                # Remove data URL prefix if present
                image_data = image_data.split(",")[1]
            image_bytes = base64.b64decode(image_data)
        else:
            image_bytes = image_data
        
        files = {
            "init_image": ("image.png", BytesIO(image_bytes), "image/png")
        }
        
        payload = {
            "text_prompts[0][text]": prompt,
            "image_strength": image_strength,
            "samples": num_images,
            "steps": 30
        }

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload, files=files)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
    
    def upscale_image(self, image_data, model: str = "esrgan-v1-x2plus", width: int = None) -> dict:
        """
        Upscale an image to a higher resolution.
        
        :param image_data: Input image as bytes or base64 string
        :param model: Model ID to use for upscaling
        :param width: Target width (optional)
        :return: Dictionary containing upscaled image as base64 string
        """
        endpoint = f"generation/{model}/image-to-image/upscale"
        
        # Convert image to bytes if it's base64
        if isinstance(image_data, str):
            if image_data.startswith("data:image"):
                # Remove data URL prefix if present
                image_data = image_data.split(",")[1]
            image_bytes = base64.b64decode(image_data)
        else:
            image_bytes = image_data
        
        files = {
            "image": ("image.png", BytesIO(image_bytes), "image/png")
        }
        
        payload = {}
        if width:
            payload["width"] = width

        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload, files=files)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
    
    # Required by BaseAIProvider but not applicable for StabilityAI
    def generate_text(self, prompt: str, model: str = None, max_tokens: int = None) -> dict:
        """
        Not applicable for Stability AI, which focuses on image generation.
        """
        return {"error": "Text generation not supported by Stability AI provider"}
    
    # Required by BaseAIProvider but not applicable for StabilityAI
    def process_response(self, response: dict) -> dict:
        """
        Process the response from Stability AI API to extract generated content.
        """
        # Handle binary data (images)
        if "binary_data" in response:
            # Convert binary data to base64 for easier handling
            base64_data = base64.b64encode(response["binary_data"]).decode("utf-8")
            return {"image_base64": base64_data}
        
        # Handle JSON responses
        if "artifacts" in response and len(response["artifacts"]) > 0:
            results = []
            for artifact in response["artifacts"]:
                if "base64" in artifact:
                    results.append({
                        "image_base64": artifact["base64"],
                        "seed": artifact.get("seed"),
                        "finish_reason": artifact.get("finishReason")
                    })
            return {"results": results, "raw_response": response}
        
        # Return the raw response if we can't extract in a standard way
        return {"raw_response": response}

