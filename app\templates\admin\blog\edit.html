{% extends "admin/admin_layout.html" %}

{% block title %}ویرایش مقاله - پنل مدیریت{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3">ویرایش مقاله</h1>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="title" class="form-label">عنوان مقاله</label>
                    <input type="text" class="form-control" id="title" name="title" value="{{ blog.title }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="content" class="form-label">محتوای مقاله</label>
                    <textarea class="form-control" id="content" name="content" rows="10" required>{{ blog.content }}</textarea>
                </div>
                
                <div class="mb-3">
                    <label for="tags" class="form-label">برچسب‌ها (با کاما جدا کنید)</label>
                    <input type="text" class="form-control" id="tags" name="tags" value="{{ blog.tags|join(', ') }}" placeholder="tag1, tag2, tag3">
                </div>
                
                <div class="mb-3">
                    <label for="featured_image" class="form-label">تصویر شاخص</label>
                    {% if blog.featured_image %}
                        <div class="mb-2">
                            <img src="/{{ blog.featured_image }}" alt="تصویر فعلی" class="img-thumbnail" style="max-height: 100px;">
                            <p class="small text-muted mt-1">تصویر فعلی</p>
                        </div>
                    {% endif %}
                    <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                    <small class="form-text text-muted">برای حفظ تصویر فعلی، فایلی انتخاب نکنید</small>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="is_published" name="is_published" {% if blog.is_published %}checked{% endif %}>
                    <label class="form-check-label" for="is_published">منتشر شده</label>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('admin.blogs') }}" class="btn btn-outline-secondary">انصراف</a>
                    <button type="submit" class="btn btn-primary">به‌روزرسانی مقاله</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // You can add a rich text editor here if needed
    // For example, using TinyMCE, CKEditor, or Quill
</script>
{% endblock %}