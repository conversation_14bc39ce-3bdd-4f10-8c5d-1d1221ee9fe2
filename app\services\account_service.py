from typing import List, Optional, Dict, Any
from ..models.account import Account, Platform, AccountStatus
from ..models.user import User
from ..models.agent import Agent
from ..models.verification_code import VerificationCode, VerificationPlatform
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class AccountService:
    @staticmethod
    def create_account(
        user_id: str,
        platform: str,
        account_name: str,
        account_identifier: str,
        agent_id: Optional[str] = None
    ) -> Account:
        """
        Create a new account for a user
        
        Args:
            user_id: The ID of the user
            platform: The social media platform (must be a valid Platform enum value)
            account_name: The name of the account
            account_identifier: The identifier for the account on the platform
            agent_id: Optional ID of an agent to associate with this account
            
        Returns:
            The created Account object
        """
        # Validate platform
        if platform not in [p.value for p in Platform]:
            raise ValueError(f"Invalid platform. Must be one of: {', '.join([p.value for p in Platform])}")
        
        # Create account object
        account = Account(
            user=user_id,
            platform=platform,
            account_name=account_name,
            account_identifier=account_identifier,
            status=AccountStatus.CONNECTED
        )
        
        # Associate with agent if provided
        if agent_id:
            account.agent = agent_id
            
        account.save()
        return account
    
    @staticmethod
    def update_account(
        account_id: str,
        platform: Optional[str] = None,
        account_name: Optional[str] = None,
        account_identifier: Optional[str] = None,
        agent_id: Optional[str] = None
    ) -> Optional[Account]:
        """
        Update an existing account
        
        Args:
            account_id: The ID of the account to update
            platform: Optional new platform value
            account_name: Optional new account name
            account_identifier: Optional new account identifier
            agent_id: Optional new agent ID to associate with this account
            
        Returns:
            The updated Account object or None if not found
        """
        try:
            account = Account.objects.get(id=account_id)
            
            # Update fields if provided
            if platform:
                if platform not in [p.value for p in Platform]:
                    raise ValueError(f"Invalid platform. Must be one of: {', '.join([p.value for p in Platform])}")
                account.platform = platform
                
            if account_name:
                account.account_name = account_name
                
            if account_identifier:
                account.account_identifier = account_identifier
                
            if agent_id:
                account.agent = agent_id
                
            # Update timestamp
            account.updated_at = datetime.utcnow()
            account.save()
            
            return account
        except Account.DoesNotExist:
            logger.error(f"Account with ID {account_id} not found")
            return None
    
    @staticmethod
    def disconnect_account(account_id: str) -> bool:
        """
        Disconnect an account by setting its status to DISCONNECTED
        
        Args:
            account_id: The ID of the account to disconnect
            
        Returns:
            True if successful, False otherwise
        """
        try:
            account = Account.objects.get(id=account_id)
            account.status = AccountStatus.DISCONNECTED
            account.updated_at = datetime.utcnow()
            account.save()
            return True
        except Account.DoesNotExist:
            logger.error(f"Account with ID {account_id} not found")
            return False
    
    @staticmethod
    def get_all_accounts(user_id: str) -> List[Account]:
        """
        Get all accounts for a user
        
        Args:
            user_id: The ID of the user
            
        Returns:
            List of Account objects
        """
        return Account.objects(user=user_id)
    
    @staticmethod
    def get_account(account_id: str) -> Optional[Account]:
        """
        Get a specific account by ID
        
        Args:
            account_id: The ID of the account
            
        Returns:
            Account object or None if not found
        """
        try:
            return Account.objects.get(id=account_id)
        except Account.DoesNotExist:
            logger.error(f"Account with ID {account_id} not found")
            return None
    
    @staticmethod
    def generate_verification_code(user_id, platform):
        """Generate a verification code for connecting a social platform"""
        # Convert string platform to enum if needed
        if isinstance(platform, str):
            platform = VerificationPlatform(platform)
            
        # Delete any existing unused codes for this user and platform
        VerificationCode.objects(
            user=user_id,
            platform=platform,
            is_used=False
        ).delete()
        
        # Generate new code
        verification = VerificationCode.generate_for_user(user_id, platform)
        return verification.code
    
    @staticmethod
    def verify_platform_code(code, platform, platform_user_id):
        """Verify a code and connect the platform account if valid"""
        # Convert string platform to enum if needed
        if isinstance(platform, str):
            platform = VerificationPlatform(platform)
            
        verification = VerificationCode.verify(code, platform)
        if not verification:
            return False, "Invalid or expired verification code"
        
        # Get the corresponding platform enum for Account
        account_platform = Platform(platform.value)
        
        # Update or create account
        account = Account.objects(
            user=verification.user,
            platform=account_platform
        ).first()
        
        if account:
            account.account_identifier = platform_user_id
            account.status = AccountStatus.CONNECTED
            account.updated_at = datetime.utcnow()
            account.save()
        else:
            account = Account(
                user=verification.user,
                platform=account_platform,
                account_name=f"{platform.value.capitalize()} Account",
                account_identifier=platform_user_id,
                status=AccountStatus.CONNECTED
            ).save()
            
        return True, account
