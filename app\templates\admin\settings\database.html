{% extends "admin/admin_layout.html" %}

{% block title %}تنظیمات پایگاه داده - Rominext{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.settings-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
    color: #212529;
}

.settings-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.db-feature {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.db-feature:hover {
    border-color: #17a2b8;
    box-shadow: 0 0.125rem 0.25rem rgba(23, 162, 184, 0.15);
}

.feature-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 1rem;
}

.connection-icon { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
.performance-icon { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
.backup-icon { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }
.maintenance-icon { background: linear-gradient(135deg, #fd7e14 0%, #e8690b 100%); }

.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #17a2b8;
}

.stats-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.alert-database {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.maintenance-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.maintenance-actions .btn {
    flex: 1;
    min-width: 120px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header settings-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>
                            تنظیمات پایگاه داده
                        </h5>
                        <div>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-server me-1"></i>
                                MongoDB
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Database Statistics Section -->
                    <div class="settings-section">
                        <h6 class="section-title">
                            <i class="fas fa-chart-pie me-2"></i>
                            آمار پایگاه داده
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-number">{{ db_stats.users_count }}</div>
                                    <div class="stats-label">کاربران</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-number">{{ db_stats.posts_count }}</div>
                                    <div class="stats-label">پست‌ها</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <div class="stats-number">{{ db_stats.logs_count }}</div>
                                    <div class="stats-label">لاگ‌ها</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ url_for('admin.settings_database') }}">
                        <!-- Connection Settings Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-plug me-2"></i>
                                تنظیمات اتصال
                            </h6>
                            
                            <div class="db-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon connection-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">پول اتصال</h6>
                                        <small class="text-muted">تنظیمات اتصال به پایگاه داده</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="db_connection_pool_size" class="form-label">اندازه پول اتصال</label>
                                        <select class="form-select" id="db_connection_pool_size" name="db_connection_pool_size">
                                            <option value="5" {% if settings.get('db_connection_pool_size') == '5' %}selected{% endif %}>5 اتصال</option>
                                            <option value="10" {% if settings.get('db_connection_pool_size', '10') == '10' %}selected{% endif %}>10 اتصال</option>
                                            <option value="20" {% if settings.get('db_connection_pool_size') == '20' %}selected{% endif %}>20 اتصال</option>
                                            <option value="50" {% if settings.get('db_connection_pool_size') == '50' %}selected{% endif %}>50 اتصال</option>
                                        </select>
                                        <div class="form-text">تعداد اتصالات همزمان به پایگاه داده</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="db_query_timeout" class="form-label">مهلت کوئری (ثانیه)</label>
                                        <select class="form-select" id="db_query_timeout" name="db_query_timeout">
                                            <option value="10" {% if settings.get('db_query_timeout') == '10' %}selected{% endif %}>10 ثانیه</option>
                                            <option value="30" {% if settings.get('db_query_timeout', '30') == '30' %}selected{% endif %}>30 ثانیه</option>
                                            <option value="60" {% if settings.get('db_query_timeout') == '60' %}selected{% endif %}>60 ثانیه</option>
                                            <option value="120" {% if settings.get('db_query_timeout') == '120' %}selected{% endif %}>120 ثانیه</option>
                                        </select>
                                        <div class="form-text">حداکثر زمان انتظار برای کوئری</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance & Logging Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                عملکرد و لاگ‌گیری
                            </h6>
                            
                            <div class="db-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon performance-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">لاگ‌گیری پایگاه داده</h6>
                                        <small class="text-muted">ثبت عملیات پایگاه داده</small>
                                    </div>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enable_db_logging" name="enable_db_logging" 
                                           {% if settings.get('enable_db_logging') == 'true' %}checked{% endif %}>
                                    <label class="form-check-label" for="enable_db_logging">
                                        فعال‌سازی لاگ‌گیری پایگاه داده
                                    </label>
                                    <div class="form-text">ثبت کوئری‌ها و عملیات پایگاه داده</div>
                                </div>
                            </div>
                        </div>

                        <!-- Backup Settings Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-save me-2"></i>
                                تنظیمات پشتیبان‌گیری
                            </h6>
                            
                            <div class="db-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon backup-icon">
                                        <i class="fas fa-archive"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">پشتیبان‌گیری خودکار</h6>
                                        <small class="text-muted">تنظیمات پشتیبان‌گیری خودکار</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="auto_backup_enabled" name="auto_backup_enabled" 
                                                   {% if settings.get('auto_backup_enabled') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="auto_backup_enabled">
                                                فعال‌سازی پشتیبان‌گیری خودکار
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="backup_frequency" class="form-label">دوره پشتیبان‌گیری</label>
                                        <select class="form-select" id="backup_frequency" name="backup_frequency">
                                            <option value="hourly" {% if settings.get('backup_frequency') == 'hourly' %}selected{% endif %}>ساعتی</option>
                                            <option value="daily" {% if settings.get('backup_frequency', 'daily') == 'daily' %}selected{% endif %}>روزانه</option>
                                            <option value="weekly" {% if settings.get('backup_frequency') == 'weekly' %}selected{% endif %}>هفتگی</option>
                                            <option value="monthly" {% if settings.get('backup_frequency') == 'monthly' %}selected{% endif %}>ماهانه</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Database Maintenance Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-tools me-2"></i>
                                نگهداری پایگاه داده
                            </h6>
                            
                            <div class="db-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon maintenance-icon">
                                        <i class="fas fa-wrench"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">عملیات نگهداری</h6>
                                        <small class="text-muted">بهینه‌سازی و تمیزکاری پایگاه داده</small>
                                    </div>
                                </div>
                                
                                <div class="alert alert-database mb-3">
                                    <h6><i class="fas fa-info-circle me-2"></i>توجه:</h6>
                                    <p class="mb-0">عملیات نگهداری ممکن است موقتاً بر عملکرد سیستم تأثیر بگذارد. بهتر است در ساعات کم‌ترافیک انجام شود.</p>
                                </div>
                                
                                <div class="maintenance-actions">
                                    <button type="button" class="btn btn-warning" onclick="optimizeDatabase()">
                                        <i class="fas fa-rocket me-2"></i>
                                        بهینه‌سازی
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="cleanupLogs()">
                                        <i class="fas fa-broom me-2"></i>
                                        پاکسازی لاگ‌ها
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="createBackup()">
                                        <i class="fas fa-download me-2"></i>
                                        پشتیبان فوری
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="repairDatabase()">
                                        <i class="fas fa-tools me-2"></i>
                                        تعمیر پایگاه داده
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    ذخیره تنظیمات
                                </button>
                                <button type="reset" class="btn btn-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    بازنشانی
                                </button>
                            </div>
                            <div>
                                <a href="{{ url_for('admin.settings_general') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    تنظیمات عمومی
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function optimizeDatabase() {
    if (confirm('آیا مطمئن هستید که می‌خواهید پایگاه داده را بهینه‌سازی کنید؟')) {
        // Show loading
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>در حال بهینه‌سازی...';
        btn.disabled = true;
        
        // Simulate optimization (replace with actual API call)
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('بهینه‌سازی با موفقیت انجام شد');
        }, 3000);
    }
}

function cleanupLogs() {
    if (confirm('آیا مطمئن هستید که می‌خواهید لاگ‌های قدیمی را پاک کنید؟')) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>در حال پاکسازی...';
        btn.disabled = true;
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('پاکسازی لاگ‌ها با موفقیت انجام شد');
        }, 2000);
    }
}

function createBackup() {
    if (confirm('آیا مطمئن هستید که می‌خواهید پشتیبان فوری ایجاد کنید؟')) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>در حال پشتیبان‌گیری...';
        btn.disabled = true;
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('پشتیبان‌گیری با موفقیت انجام شد');
        }, 5000);
    }
}

function repairDatabase() {
    if (confirm('آیا مطمئن هستید که می‌خواهید پایگاه داده را تعمیر کنید؟ این عملیات ممکن است زمان‌بر باشد.')) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>در حال تعمیر...';
        btn.disabled = true;
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('تعمیر پایگاه داده با موفقیت انجام شد');
        }, 8000);
    }
}
</script>
{% endblock %}
