{% extends "admin/admin_layout.html" %}

{% block title %}تنظیمات امنیتی - Rominext{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.settings-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-1px);
}

.settings-section {
    margin-bottom: 2rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.security-feature {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.security-feature:hover {
    border-color: #dc3545;
    box-shadow: 0 0.125rem 0.25rem rgba(220, 53, 69, 0.15);
}

.security-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-right: 1rem;
}

.password-icon { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
.session-icon { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
.login-icon { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
.auth-icon { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }
.verification-icon { background: linear-gradient(135deg, #fd7e14 0%, #e8690b 100%); }
.captcha-icon { background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); }

.alert-security {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.form-range::-webkit-slider-thumb {
    background: #dc3545;
}

.form-range::-moz-range-thumb {
    background: #dc3545;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header settings-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            تنظیمات امنیتی
                        </h5>
                        <div>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                حساس
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-security mb-4">
                        <h6><i class="fas fa-info-circle me-2"></i>هشدار امنیتی</h6>
                        <p class="mb-0">تغییر این تنظیمات می‌تواند بر امنیت سیستم تأثیر بگذارد. لطفاً با احتیاط عمل کنید.</p>
                    </div>

                    <form method="POST" action="{{ url_for('admin.settings_security') }}">
                        <!-- Password Security Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-key me-2"></i>
                                امنیت رمز عبور
                            </h6>
                            
                            <div class="security-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="security-icon password-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">حداقل طول رمز عبور</h6>
                                        <small class="text-muted">تعیین حداقل تعداد کاراکتر برای رمز عبور</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="password_min_length" class="form-label">حداقل طول: <span id="password_length_value">{{ settings.get('password_min_length', '8') }}</span> کاراکتر</label>
                                        <input type="range" class="form-range" id="password_min_length" name="password_min_length" 
                                               min="6" max="20" value="{{ settings.get('password_min_length', '8') }}" 
                                               oninput="document.getElementById('password_length_value').textContent = this.value">
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="password_complexity" name="password_complexity" 
                                                   {% if settings.get('password_complexity') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="password_complexity">
                                                الزام پیچیدگی رمز عبور
                                            </label>
                                            <div class="form-text">شامل حروف بزرگ، کوچک، عدد و نماد</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Session Management Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-clock me-2"></i>
                                مدیریت جلسه
                            </h6>
                            
                            <div class="security-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="security-icon session-icon">
                                        <i class="fas fa-hourglass-half"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">مهلت جلسه</h6>
                                        <small class="text-muted">مدت زمان انقضای جلسه کاربر (ثانیه)</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="session_timeout" class="form-label">مهلت جلسه (ثانیه)</label>
                                        <select class="form-select" id="session_timeout" name="session_timeout">
                                            <option value="1800" {% if settings.get('session_timeout') == '1800' %}selected{% endif %}>30 دقیقه</option>
                                            <option value="3600" {% if settings.get('session_timeout', '3600') == '3600' %}selected{% endif %}>1 ساعت</option>
                                            <option value="7200" {% if settings.get('session_timeout') == '7200' %}selected{% endif %}>2 ساعت</option>
                                            <option value="14400" {% if settings.get('session_timeout') == '14400' %}selected{% endif %}>4 ساعت</option>
                                            <option value="28800" {% if settings.get('session_timeout') == '28800' %}selected{% endif %}>8 ساعت</option>
                                            <option value="86400" {% if settings.get('session_timeout') == '86400' %}selected{% endif %}>24 ساعت</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Login Security Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                امنیت ورود
                            </h6>
                            
                            <div class="security-feature">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="security-icon login-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">محدودیت تلاش ورود</h6>
                                        <small class="text-muted">حداکثر تعداد تلاش ناموفق ورود</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="max_login_attempts" class="form-label">حداکثر تلاش: <span id="max_attempts_value">{{ settings.get('max_login_attempts', '5') }}</span></label>
                                        <input type="range" class="form-range" id="max_login_attempts" name="max_login_attempts" 
                                               min="3" max="10" value="{{ settings.get('max_login_attempts', '5') }}" 
                                               oninput="document.getElementById('max_attempts_value').textContent = this.value">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="lockout_duration" class="form-label">مدت قفل شدن (دقیقه)</label>
                                        <select class="form-select" id="lockout_duration" name="lockout_duration">
                                            <option value="300" {% if settings.get('lockout_duration') == '300' %}selected{% endif %}>5 دقیقه</option>
                                            <option value="900" {% if settings.get('lockout_duration', '900') == '900' %}selected{% endif %}>15 دقیقه</option>
                                            <option value="1800" {% if settings.get('lockout_duration') == '1800' %}selected{% endif %}>30 دقیقه</option>
                                            <option value="3600" {% if settings.get('lockout_duration') == '3600' %}selected{% endif %}>1 ساعت</option>
                                            <option value="7200" {% if settings.get('lockout_duration') == '7200' %}selected{% endif %}>2 ساعت</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Security Features Section -->
                        <div class="settings-section">
                            <h6 class="section-title">
                                <i class="fas fa-cogs me-2"></i>
                                ویژگی‌های امنیتی پیشرفته
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="security-feature">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="security-icon auth-icon">
                                                <i class="fas fa-mobile-alt"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">احراز هویت دو مرحله‌ای</h6>
                                                <small class="text-muted">فعال‌سازی 2FA برای امنیت بیشتر</small>
                                            </div>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_two_factor_auth" name="enable_two_factor_auth" 
                                                   {% if settings.get('enable_two_factor_auth') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="enable_two_factor_auth">
                                                فعال‌سازی 2FA
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="security-feature">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="security-icon verification-icon">
                                                <i class="fas fa-envelope-open-text"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">تأیید ایمیل</h6>
                                                <small class="text-muted">الزام تأیید ایمیل برای ثبت‌نام</small>
                                            </div>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="require_email_verification" name="require_email_verification" 
                                                   {% if settings.get('require_email_verification', 'true') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="require_email_verification">
                                                الزام تأیید ایمیل
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="security-feature">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="security-icon captcha-icon">
                                                <i class="fas fa-robot"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">CAPTCHA</h6>
                                                <small class="text-muted">محافظت در برابر ربات‌ها</small>
                                            </div>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_captcha" name="enable_captcha" 
                                                   {% if settings.get('enable_captcha') == 'true' %}checked{% endif %}>
                                            <label class="form-check-label" for="enable_captcha">
                                                فعال‌سازی CAPTCHA
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    ذخیره تنظیمات
                                </button>
                                <button type="reset" class="btn btn-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>
                                    بازنشانی
                                </button>
                            </div>
                            <div>
                                <a href="{{ url_for('admin.settings_system') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    تنظیمات سیستم
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
