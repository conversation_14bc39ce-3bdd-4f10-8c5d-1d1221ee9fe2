<!DOCTYPE html>
<html lang="{{ lang|default('fa') }}" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ t('landing.title') }}</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Vazir Font for Persian text -->
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazir-font@v30.1.0/dist/font-face.css" rel="stylesheet">
    <!-- RTL overrides -->
    <link href="{{ url_for('static', filename='css/rtl.css') }}" rel="stylesheet">
    <style>
        body, h1, h2, h3, h4, h5, h6, p, a, button, input, textarea, select, .btn {
            font-family: 'Vazir', Tahoma, Arial, sans-serif !important;
        }
    </style>
    <style>
        .navbar-brand::after {
            display: none !important;
        }
    </style>
    <style>
        .social-icons a {
            margin-left: 0.05rem !important;
            margin-right: 0.05rem !important;
            text-decoration: none !important;
        }
    </style>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/social-hub.css') }}">
    {% block head_extra %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light" style="background-color: #E0F2F2 !important; box-shadow: none !important; margin-bottom: 0 !important; padding-top: 15px; padding-bottom: 15px;">
        <div class="container">
            <a class="navbar-brand me-auto pe-5 ms-5" href="{{ url_for('landing.index') }}">
                <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Rominext Logo" height="40" class="d-inline-block align-text-top ms-4">
                رومینکست
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0 text-end">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.index') }}">{{ t('nav.home') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.features_index') }}">{{ t('nav.features') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.pricing') }}">{{ t('nav.pricing') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.about') }}">{{ t('nav.about') }}</a>
                    </li>
                    <!-- <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.blog') }}">{{ t('nav.blog') }}</a>
                    </li> -->
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.index') }}">{{ t('nav.home') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.features_index') }}">{{ t('nav.features') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.pricing') }}">{{ t('nav.pricing') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.about') }}">{{ t('nav.about') }}</a>
                    </li>
                    <!-- <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('landing.blog') }}">{{ t('nav.blog') }}</a>
                    </li> -->
                    {% endif %}
                </ul>
                <ul class="navbar-nav ms-0">
                    {% if current_user.is_authenticated %}
                    <!-- Dashboard link -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard.index' %}active{% endif %}" 
                           href="{{ url_for('dashboard.profile_settings' if request.path.startswith('/dashboard') else 'dashboard.index') }}">
                            {% if request.path.startswith('/dashboard') %}
                                {{ t('nav.profile') }}
                            {% else %}
                                {{ t('nav.dashboard') }}
                            {% endif %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('vault.logout') }}">
                            {{ t('nav.logout') }}
                        </a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('vault.login') }}">
                            {{ t('nav.login') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('vault.register') }}">
                            {{ t('nav.signup') }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Toast container for flash messages - only errors and warnings -->
    <div class="toast-container position-fixed p-3" style="top: 80px; left: 20px; z-index: 1050;">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {% if category == 'error' or category == 'danger' or category == 'warning' %}
                        <div class="toast text-white bg-{{ category if category != 'error' else 'danger' }} border-0" 
                             role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000"
                             style="min-width: auto; max-width: fit-content;">
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn-close btn-close-white ms-2 me-2" data-bs-dismiss="toast" aria-label="Close"></button>
                                <div class="toast-body py-2 px-3">
                                    {{ message }}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <div class="content-wrapper">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="py-5 mt-5">
        <div class="container">
            <div class="row mb-4">
               
                <div class="col-lg-3 col-md-6 mb-4">
                    <ul class="list-unstyled footer-links">
                         <li>
                            <i class="fas fa-home me-2 text-success"></i>
                            <a href="{{ url_for('landing.index') }}">صفحه اصلی</a>
                        </li>
                        <li>
                            <i class="fas fa-star me-2 text-success"></i>
                            <a href="{{ url_for('landing.features_index') }}">ویژگی‌ها</a>
                        </li>
                        <li>
                            <i class="fas fa-tags me-2 text-success"></i>
                            <a href="{{ url_for('landing.pricing') }}">قیمت‌گذاری</a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <ul class="list-unstyled footer-links">
                       
                         <li>
                            <i class="fas fa-blog me-2 text-success"></i>
                            <a href="{{ url_for('landing.blog') }}">وبلاگ</a>
                        </li>
                        <li>
                            <i class="fas fa-question-circle me-2 text-success"></i>
                            <a href="{{ url_for('landing.help') }}">راهنما</a>
                        </li>
                        <li>
                            <i class="fas fa-file-contract me-2 text-success"></i>
                            <a href="{{ url_for('landing.terms') }}">قوانین و حریم خصوصی</a>
                        </li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <ul class="list-unstyled footer-links">
                        <li>
                            <i class="fas fa-info-circle me-2 text-success"></i>
                            <a href="{{ url_for('landing.about') }}">درباره ما</a>
                        </li>
                        <li>
                            <i class="fas fa-phone me-2 text-success"></i>
                            <a href="{{ url_for('landing.contact') }}">تماس با ما</a>
                        </li>
                        <li>
                            <i class="fas fa-question me-2 text-success"></i>
                            <a href="{{ url_for('landing.faq') }}">سوالات متداول</a>
                        </li>
                    </ul>
                </div>

                 <div class="col-lg-3 col-md-6 mb-4">
                    <ul class="list-unstyled footer-links">
                        <li>
                            <i class="fas fa-envelope me-2 text-success"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                       <li>
                            <i class="fas fa-phone-alt me-2 text-success"></i>
                            <a href="tel:02187635210">۰۲۱-۸۷۶۳۵۲۱</a>
                        </li>
                       <li>
                            <i class="fas fa-building me-2 text-success"></i>
                            <a href="https://niravin.com">محصول شرکت نیراوین</a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="row align-items-center pt-3">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">{{ t('footer.copyright') }}</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <div class="social-icons">
                        <a href="#" class="me-1"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-1"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-1"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-1"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="me-1"><i class="fab fa-telegram-plane"></i></a>
                        <a href="#" class="me-1"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
    <script>
        // Initialize toasts - only for errors and warnings
        document.addEventListener('DOMContentLoaded', function() {
            var toastElList = [].slice.call(document.querySelectorAll('.toast'));
            var toastList = toastElList.map(function(toastEl) {
                return new bootstrap.Toast(toastEl, {
                    delay: 3000
                });
            });
            
            // Show all toasts
            toastList.forEach(toast => toast.show());
            
            // Convert any remaining btn-primary to btn-success for consistency
            document.querySelectorAll('.btn-primary').forEach(button => {
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');
            });
        });
    </script>

</body>
</html>

































