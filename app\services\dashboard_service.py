from ..integrations.social.facebook_connector import FacebookConnector
from ..integrations.ai.openai_provider import OpenAIProvider
from ..integrations.ai.deepseek_provider import DeepSeek<PERSON>rovider
from typing import Dict, Any
from datetime import datetime, timedelta
from ..models import Strategy
import logging

logger = logging.getLogger(__name__)

class DashboardService:
    def __init__(self, social_connector=None, ai_provider=None):
        # Use dependency injection or create default instances
        self.social_connector = social_connector or FacebookConnector({"access_token": "", "page_id": ""})
        self.ai_provider = ai_provider or OpenAIProvider()
        self.current_strategy = None

    def get_dashboard_data(self) -> Dict[str, Any]:
        try:
            # Get active strategy
            active_strategy = Strategy.objects(is_active=True).first()
            if active_strategy:
                self.current_strategy = active_strategy
            
            posts = self.social_connector.fetch_recent_posts()
            recent_comments = self.social_connector.fetch_recent_comments(limit=5)
            profile_settings = self._get_profile_settings()
            location = self._get_user_location()
            
            # Get recent activity data
            recent_activity = self._get_recent_activity(posts)
            
            # Generate AI summaries
            activity_summary = self.generate_activity_summary(recent_activity, recent_comments)
            feedback_summary = self.analyze_feedback(recent_comments.get('data', []))
            
            post_suggestions = self._generate_post_suggestions(
                profile_settings=profile_settings,
                location=location
            )
            
            # Add fallback for missing methods
            if not hasattr(self.ai_provider, 'generate_quick_replies'):
                logger.warning("AIProvider missing generate_quick_replies method")
                self.ai_provider.generate_quick_replies = lambda comment, strategy: [
                    "Thank you for your feedback!",
                    "We appreciate your comment!",
                    "Thanks for sharing!"
                ]
            
            if not hasattr(self.ai_provider, '_parse_post_suggestions'):
                logger.warning("AIProvider missing _parse_post_suggestions method")
                self.ai_provider._parse_post_suggestions = lambda result: []
            
            return {
                'posts': posts,
                'posts_summary': self._get_posts_summary(posts),
                'sentiment_analysis': self._analyze_comments_sentiment(posts),
                'engagement_metrics': self._calculate_engagement_metrics(posts),
                'recent_activity': recent_activity,
                'recent_comments': recent_comments,
                'post_suggestions': post_suggestions.get('suggestions', []),
                'activity_summary': activity_summary,
                'feedback_summary': feedback_summary
            }
            
        except Exception as e:
            logger.error(f"Error in dashboard data retrieval: {str(e)}")
            return self._get_empty_dashboard_data()

    def _get_empty_dashboard_data(self) -> Dict[str, Any]:
        """Return empty dashboard data structure."""
        return {
            'posts': {'data': []},
            'posts_summary': {'total_posts': 0, 'posts_this_week': 0},
            'sentiment_analysis': {'positive': 0, 'negative': 0, 'neutral': 0},
            'engagement_metrics': {'total_comments': 0, 'avg_comments_per_post': 0},
            'recent_activity': [],
            'recent_comments': {'data': []},
            'post_suggestions': [],
            'activity_summary': "No recent activity to summarize.",
            'feedback_summary': {'summary': "No feedback to analyze.", 'topics': []}
        }

    def _get_posts_summary(self, posts: Dict[str, Any]) -> Dict[str, Any]:
        data = posts.get('data', [])
        return {
            'total_posts': len(data),
            'posts_this_week': sum(1 for post in data if 
                self._is_within_days(post.get('created_time', ''), 7))
        }

    def _analyze_comments_sentiment(self, posts: Dict[str, Any]) -> Dict[str, int]:
        sentiment_counts = {'positive': 0, 'negative': 0, 'neutral': 0}
        for post in posts.get('data', []):
            comments = self.social_connector.fetch_comments(post['id'])
            for comment in comments.get('data', []):
                sentiment = self.ai_provider.analyze_sentiment(comment['message'])
                sentiment_counts[sentiment['sentiment']] += 1
        return sentiment_counts

    def calculate_engagement_metrics(self, posts, analytics_data=None):
        """
        Calculate engagement metrics for posts.
        
        Args:
            posts: List of Post objects
            analytics_data: Optional analytics data
        
        Returns:
            Dictionary with engagement metrics
        """
        total_likes = 0
        total_comments = 0
        total_shares = 0
        
        # If analytics data is provided, use it
        if analytics_data:
            for analytics in analytics_data:
                total_likes += analytics.likes_count or 0
                total_comments += analytics.comments_count or 0
                total_shares += analytics.shares_count or 0
        else:
            # Otherwise calculate from posts directly
            for post in posts:
                post_id = str(post.id) if hasattr(post, 'id') else post.get('id', '')
                if not post_id:
                    continue
                    
                # Try to get comments from social connector
                try:
                    comments = self.social_connector.fetch_comments(post_id)
                    total_comments += len(comments.get('data', []))
                except Exception as e:
                    logger.error(f"Error fetching comments for post {post_id}: {str(e)}")
        
        return {
            'likes': total_likes,
            'comments': total_comments,
            'shares': total_shares
        }

    def _get_recent_activity(self, posts: Dict[str, Any]) -> list:
        recent_activities = []
        for post in posts.get('data', [])[:5]:  # Last 5 posts
            comments = self.social_connector.fetch_comments(post['id'])
            recent_activities.append({
                'post_id': post['id'],
                'message': post.get('message', ''),
                'created_time': post.get('created_time'),
                'comment_count': len(comments.get('data', []))
            })
        return recent_activities

    @staticmethod
    def _is_within_days(date_str: str, days: int = 7) -> bool:
        """Check if a date string is within the specified number of days from now."""
        try:
            post_date = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S+0000')
            return post_date > datetime.utcnow() - timedelta(days=days)
        except Exception:
            # If there's any error parsing the date, return False
            return False

    def _get_profile_settings(self) -> Dict[str, Any]:
        # This would normally come from a database
        # For now, let's return a sample profile
        return {
            'tone': 'friendly and professional',
            'topics': ['product updates', 'industry news', 'customer success stories'],
            'industry': 'technology',
            'posting_frequency': 'daily'
        }
        
    def _get_user_location(self) -> str:
        # This would normally be determined from user settings or analytics
        # For now, return a default location
        return "United States"
        
    def quick_reply_to_comment(self, comment_id: str, message: str) -> Dict[str, Any]:
        """Allow quick replies to comments from the dashboard."""
        return self.social_connector.reply_to_comment(comment_id, message)

    def generate_activity_summary(self, recent_posts, recent_comments):
        """
        Generate a summary of recent activity.
        
        Args:
            recent_posts: List of recent posts
            recent_comments: List of recent comments
        
        Returns:
            String with activity summary
        """
        try:
            # Count posts and comments
            post_count = len(recent_posts) if recent_posts else 0
            comment_count = len(recent_comments) if recent_comments else 0
            
            if post_count == 0 and comment_count == 0:
                return "No recent activity to summarize."
                
            # Generate a simple summary
            summary = f"You have {post_count} recent posts and {comment_count} recent comments."
            
            # Try to use AI provider for more detailed summary if available
            if self.ai_provider and hasattr(self.ai_provider, 'generate_summary'):
                try:
                    ai_summary = self.ai_provider.generate_summary(
                        f"Recent activity: {post_count} posts and {comment_count} comments."
                    )
                    if ai_summary:
                        return ai_summary
                except Exception as e:
                    logger.error(f"Error generating AI summary: {str(e)}")
                    
            return summary
        except Exception as e:
            logger.error(f"Error generating activity summary: {str(e)}")
            return "Unable to generate activity summary."

    def analyze_feedback(self, recent_comments):
        """
        Analyze feedback from recent comments.
        
        Args:
            recent_comments: List of recent comments
        
        Returns:
            Dictionary with feedback summary and topics
        """
        try:
            if not recent_comments or len(recent_comments) == 0:
                return {
                    "summary": "No feedback to analyze.",
                    "topics": []
                }
                
            # Extract comment messages
            comment_texts = []
            for comment in recent_comments:
                if hasattr(comment, 'message'):
                    comment_texts.append(comment.message)
                elif isinstance(comment, dict) and 'message' in comment:
                    comment_texts.append(comment['message'])
                    
            if not comment_texts:
                return {
                    "summary": "No feedback content to analyze.",
                    "topics": []
                }
                
            # Try to use AI provider for analysis if available
            if self.ai_provider and hasattr(self.ai_provider, 'analyze_feedback'):
                try:
                    return self.ai_provider.analyze_feedback(comment_texts)
                except Exception as e:
                    logger.error(f"Error analyzing feedback with AI: {str(e)}")
                    
            # Fallback to simple analysis
            return {
                "summary": f"You have {len(comment_texts)} comments to review.",
                "topics": [
                    {"name": "General Feedback", "sentiment": "neutral"}
                ]
            }
        except Exception as e:
            logger.error(f"Error analyzing feedback: {str(e)}")
            return {
                "summary": "Unable to analyze feedback.",
                "topics": []
            }

    def track_user_activity(self, user_id, activity_type, description):
        """
        Track user activity for the activity feed
        
        Args:
            user_id: User ID
            activity_type: Type of activity (post, comment, etc)
            description: Description of the activity
        
        Returns:
            Boolean indicating success
        """
        try:
            # This would normally save to a database
            # For now, just log it
            logger.info(f"User {user_id} performed {activity_type}: {description}")
            return True
        except Exception as e:
            logger.error(f"Error tracking user activity: {str(e)}")
            return False
