/* Global styles */
body {
    background-color: #f8f9fa;
}

/* Add these styles to fix footer positioning */
html, body {
  min-height: 100vh;
}

body {
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
}

/* Footer styling */
footer {
  background-color: #e0f2e9;
  border-top: 1px solid rgba(0,0,0,0.05);
  position: relative;
  min-height: 300px;
  display: flex;
  align-items: center;
}

footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #28a745, transparent);
}

.footer-brand {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.footer-brand img {
  filter: drop-shadow(0 2px 3px rgba(0,0,0,0.1));
}

.footer-brand span {
  font-size: 1.3rem;
  color: #28a745;
  font-weight: 600;
}

.footer-links {
  margin-top: 2rem;
}

.footer-links li {
  margin-bottom: 16px;
}

.footer-links a {
  color: #212529;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.footer-links a:hover {
  color: #28a745;
  padding-right: 5px;
}

/* Contact info in footer */
.contact-info li {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.contact-info i {
  width: 20px;
  text-align: center;
  margin-left: 8px;
}

.contact-info a {
  color: #212529;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contact-info a:hover {
  color: #28a745;
}

/* Social icons in footer */
.social-icons {
  display: flex;
  justify-content: flex-end;
}

.social-icons a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  transition: all 0.3s ease;
  margin: 0 3px;
}

.social-icons a:hover {
  background-color: #28a745;
  color: white;
}

/* Responsive adjustments for footer */
@media (max-width: 767px) {
  .social-icons {
    justify-content: center;
    margin-top: 15px;
  }
}

/* Copyright section */
footer .row.align-items-center {
  border-top: 1px solid rgba(0,0,0,0.05);
  padding-top: 20px;
}

footer .copyright p {
  font-size: 0.95rem;
}

/* Ensure all footer text is dark */
footer a, footer h5, footer p, footer .text-light {
  color: #212529 !important;
}

/* Card styles */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.125);
    font-weight: bold;
}

/* Dashboard stats */
.stats-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

/* Comments */
.comment-card {
    border-left: 4px solid #007bff;
    margin-bottom: 15px;
}

.sentiment-badge {
    float: right;
    font-size: 0.8em;
}

/* Forms */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Buttons */
.btn-primary {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-primary:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-primary:focus, .btn-primary.focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-primary:active, .btn-primary.active {
    background-color: #1e7e34;
    border-color: #1c7430;
}

/* Success button - already green but ensuring consistency */
.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Secondary buttons with outline */
.btn-outline-primary {
    color: #28a745;
    border-color: #28a745;
}

.btn-outline-primary:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

/* For any other button types that might be used */
.btn-info, .btn-warning, .btn-danger {
    border-radius: 4px;
}

/* Navigation */
.navbar {
  box-shadow: none !important;
  margin-bottom: 0 !important;
  background-color: #E0F2F2 !important; /* Light teal color */
  border-bottom: none;
}

.navbar-brand {
    font-weight: bold;
}

.nav-link.active {
    color: #28a745 !important;
    font-weight: bold;
}

.nav-link:hover {
    color: #28a745 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .container {
        padding: 10px;
    }
}

/* Dashboard sidebar styles */
.sidebar {
    padding: 20px 0;
    background-color: #f8f9fa;
    min-height: calc(100vh - 200px); /* Adjust based on your header/footer height */
}

/* Fix for RTL sidebar */
html[dir="rtl"] .sidebar {
    border-right: none;
    border-left: 1px solid #dee2e6;
}

html[dir="ltr"] .sidebar {
    border-right: 1px solid #dee2e6;
    border-left: none;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: .75rem 1rem;
}

html[dir="rtl"] .sidebar .nav-link {
    border-right: 3px solid transparent;
    border-left: none;
    text-align: right;
}

html[dir="ltr"] .sidebar .nav-link {
    border-left: 3px solid transparent;
    border-right: none;
    text-align: left;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 0, 0, .05);
}

.sidebar .nav-link.active {
    border-color: #28a745;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        padding-top: 1.5rem;
        margin-top: 0;
    }
}

/* Flag icon styling */
.navbar-nav .nav-link img {
    vertical-align: middle;
}

.navbar-nav .nav-link.d-flex.align-items-center {
    display: flex;
    align-items: center;
    height: 100%;
}

/* Ensure consistent styling for all nav items */
.navbar-nav .nav-item {
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    color: rgba(0, 0, 0, 0.55);
    transition: color 0.15s ease-in-out;
}

.navbar-nav .nav-link:hover {
    color: #28a745;
}

.navbar-nav .nav-link.active {
    color: #28a745;
    font-weight: 500;
}

/* Navbar styling for logo isolation */
.navbar-brand {
    position: relative;
}

.navbar-brand::after {
    content: '';
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    height: 24px;
    width: 1px;
    background-color: rgba(0, 0, 0, 0.1);
}

/* RTL support for the separator */
html[dir="rtl"] .navbar-brand::after {
    right: auto;
    left: -15px;
}

/* Add some spacing to the menu items */
.navbar-nav .nav-item:not(:last-child) {
    margin-right: 5px;
}

html[dir="rtl"] .navbar-nav .nav-item:not(:last-child) {
    margin-right: 0;
    margin-left: 5px;
}

/* Sidebar submenu styles - updated */
.sidebar .collapse {
    padding-right: 0;
}

.sidebar .collapse .nav-link {
    padding: 0.5rem 1.5rem; /* Keep consistent padding with main menu items */
    font-size: 0.9rem;
}

/* Remove any indentation styling */
.sidebar .collapse .nav-item {
    border-right: none;
    border-left: none;
}

/* Main menu items styling */
.sidebar .main-menu-item {
    font-weight: bold;
    padding: 0.8rem 1.5rem;
    border-bottom: 1px solid #eaeaea;
    background-color: #f8f9fa;
    color: #333;
}

.sidebar .main-menu-item:hover {
    background-color: #e9ecef;
}

.sidebar .main-menu-item.active {
    background-color: #e3e6e9;
    color: #000;
}

/* Submenu styling - tree-like structure */
.sidebar .submenu {
    background-color: #ffffff;
    padding-right: 0;
    margin-right: 20px; /* Indent the entire submenu */
}

.sidebar .submenu-item {
    font-weight: normal;
    font-size: 0.9rem;
    padding: 0.5rem 1rem 0.5rem 0.5rem;
    color: #555;
    border-right: 2px solid #28a745;
    margin-bottom: 2px;
    background-color: #f8f9fa;
}

.sidebar .submenu-item:hover {
    background-color: #e9ecef;
    color: #28a745;
}

/* Add tree-like connector */
.sidebar .submenu::before {
    content: "";
    position: absolute;
    top: 0;
    right: 25px;
    width: 2px;
    height: 100%;
    background-color: #dee2e6;
}

/* Adjust icon positioning for tree structure */
.sidebar .submenu-item i {
    margin-left: 8px;
    color: #6c757d;
}

/* Remove the arrow indicator for expandable menus */
.sidebar .nav-link[data-bs-toggle="collapse"]::after {
    content: none;
}

/* Remove RTL-specific arrow rules */
html[dir="rtl"] .sidebar .nav-link[data-bs-toggle="collapse"]::after,
html[dir="ltr"] .sidebar .nav-link[data-bs-toggle="collapse"]::after {
    content: none;
}

/* Fix dropdown menu positioning and scrolling issues */
body {
  overflow-x: hidden;
}

.dropdown-menu-end {
  right: auto;
  left: 0;
}

html[dir="rtl"] .dropdown-menu-end {
  left: auto;
  right: 0;
}

/* Ensure dropdowns appear above other elements */
.dropdown-menu {
  z-index: 1030;
  position: absolute;
}

/* Fix dropdown positioning for RTL */
html[dir="rtl"] .dropdown-menu {
  text-align: right;
  left: auto !important;
  right: 0 !important;
}

/* Fix dropdown toggle arrow position in RTL */
html[dir="rtl"] .dropdown-toggle::after {
  margin-right: 0.255em;
  margin-left: 0;
}

/* Navigation height adjustments */
.navbar-nav .nav-link {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    font-size: 1.05rem;
}

.navbar-brand {
    font-size: 1.4rem;
    font-weight: bold;
}
