@echo off
echo ========================================
echo    Rominext Email System Launcher
echo ========================================
echo.

echo Activating conda environment...
call conda activate rominext

if %errorlevel% neq 0 (
    echo ERROR: Failed to activate conda environment 'rominext'
    echo Please make sure the environment exists and conda is installed.
    pause
    exit /b 1
)

echo.
echo Starting Rominext application...
echo.
echo Email System Features:
echo - Multilingual email templates
echo - Background email processing
echo - Email analytics and tracking
echo - Admin interface for management
echo.
echo Access URLs:
echo - Main App: http://localhost:5000
echo - Admin Panel: http://localhost:5000/admin
echo - Email Test: http://localhost:5000/email/admin/test
echo.
echo Press Ctrl+C to stop the application
echo ========================================
echo.

python run_app.py

pause
