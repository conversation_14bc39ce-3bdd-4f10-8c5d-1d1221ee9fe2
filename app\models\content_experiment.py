from mongoengine import Document, <PERSON><PERSON><PERSON>, DateTimeField, Dict<PERSON><PERSON>
from datetime import datetime
from .user import User
from .post import Post

class ContentExperiment(Document):
    """Manage A/B testing experiments between two posts"""
    user = ReferenceField(User, required=True)
    post_a = ReferenceField(Post, required=True)
    post_b = ReferenceField(Post, required=True)
    start_date = DateTimeField(required=True)
    end_date = DateTimeField(required=True)
    winner_post = ReferenceField(Post)  # Nullable reference to winning post
    analytics_data = DictField(default={})
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'content_experiments',
        'indexes': ['user', 'start_date', 'end_date']
    }