/**
 * Advanced File Upload Helper
 * 
 * Provides comprehensive file upload functionality with:
 * - Drag and drop support
 * - Progress tracking
 * - File validation
 * - Batch uploads
 * - Preview generation
 */

class UploadHelper {
    constructor(options = {}) {
        this.options = {
            endpoint: '/api/upload/single',
            batchEndpoint: '/api/upload/batch',
            validateEndpoint: '/api/upload/validate',
            configEndpoint: '/api/upload/config',
            maxFiles: 10,
            allowedTypes: ['image', 'video', 'document', 'audio'],
            autoUpload: false,
            showPreviews: true,
            enableDragDrop: true,
            enableProgress: true,
            chunkSize: 8192,
            ...options
        };
        
        this.files = [];
        this.config = null;
        this.isUploading = false;
        
        this.init();
    }
    
    async init() {
        // Load upload configuration
        await this.loadConfig();
        
        // Initialize UI if container is provided
        if (this.options.container) {
            this.initializeUI();
        }
    }
    
    async loadConfig() {
        try {
            const response = await fetch(this.options.configEndpoint);
            const data = await response.json();
            
            if (data.success) {
                this.config = data.config;
            } else {
                console.error('Failed to load upload config:', data.error);
            }
        } catch (error) {
            console.error('Failed to load upload config:', error);
        }
    }
    
    initializeUI() {
        const container = document.querySelector(this.options.container);
        if (!container) return;
        
        container.innerHTML = this.generateHTML();
        this.bindEvents();
    }
    
    generateHTML() {
        return `
            <div class="upload-container">
                <div class="upload-dropzone" id="upload-dropzone">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">
                        <h3>Drop files here or click to browse</h3>
                        <p>Supports images, videos, documents, and audio files</p>
                    </div>
                    <input type="file" id="file-input" multiple style="display: none;">
                </div>
                
                <div class="upload-files" id="upload-files" style="display: none;">
                    <h4>Selected Files</h4>
                    <div class="file-list" id="file-list"></div>
                </div>
                
                <div class="upload-controls" id="upload-controls" style="display: none;">
                    <div class="upload-options">
                        <label>
                            <input type="checkbox" id="process-files" checked>
                            Process files (thumbnails, optimization)
                        </label>
                        <label>
                            Folder: <input type="text" id="upload-folder" placeholder="Optional folder name">
                        </label>
                    </div>
                    <div class="upload-buttons">
                        <button class="btn btn-secondary" id="clear-files">Clear All</button>
                        <button class="btn btn-primary" id="start-upload">Upload Files</button>
                    </div>
                </div>
                
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">Uploading...</div>
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        const dropzone = document.getElementById('upload-dropzone');
        const fileInput = document.getElementById('file-input');
        const clearBtn = document.getElementById('clear-files');
        const uploadBtn = document.getElementById('start-upload');
        
        // Drag and drop events
        if (this.options.enableDragDrop) {
            dropzone.addEventListener('dragover', this.handleDragOver.bind(this));
            dropzone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            dropzone.addEventListener('drop', this.handleDrop.bind(this));
        }
        
        // Click to browse
        dropzone.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        
        // Control buttons
        clearBtn.addEventListener('click', this.clearFiles.bind(this));
        uploadBtn.addEventListener('click', this.startUpload.bind(this));
    }
    
    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('drag-over');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('drag-over');
    }
    
    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        this.addFiles(files);
    }
    
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.addFiles(files);
    }
    
    async addFiles(files) {
        // Validate file count
        if (this.files.length + files.length > this.options.maxFiles) {
            this.showError(`Maximum ${this.options.maxFiles} files allowed`);
            return;
        }
        
        // Validate and add files
        for (const file of files) {
            const validation = await this.validateFile(file);
            
            if (validation.valid) {
                this.files.push({
                    file: file,
                    id: this.generateId(),
                    metadata: validation.metadata,
                    status: 'pending',
                    progress: 0
                });
            } else {
                this.showError(`${file.name}: ${validation.error}`);
            }
        }
        
        this.updateUI();
    }
    
    async validateFile(file) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await fetch(this.options.validateEndpoint, {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                return {
                    valid: data.valid,
                    error: data.error,
                    metadata: data.metadata
                };
            } else {
                return {
                    valid: false,
                    error: data.error || 'Validation failed'
                };
            }
        } catch (error) {
            return {
                valid: false,
                error: 'Validation failed'
            };
        }
    }
    
    updateUI() {
        const filesContainer = document.getElementById('upload-files');
        const controlsContainer = document.getElementById('upload-controls');
        const fileList = document.getElementById('file-list');
        
        if (this.files.length > 0) {
            filesContainer.style.display = 'block';
            controlsContainer.style.display = 'block';
            
            fileList.innerHTML = this.files.map(fileData => this.generateFileHTML(fileData)).join('');
        } else {
            filesContainer.style.display = 'none';
            controlsContainer.style.display = 'none';
        }
    }
    
    generateFileHTML(fileData) {
        const { file, metadata, status, progress } = fileData;
        const sizeText = this.formatFileSize(file.size);
        const statusClass = status === 'completed' ? 'success' : status === 'error' ? 'error' : '';
        
        return `
            <div class="file-item ${statusClass}" data-id="${fileData.id}">
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-details">${sizeText} • ${metadata?.file_type || 'Unknown'}</div>
                </div>
                <div class="file-progress">
                    <div class="progress-bar small">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn btn-sm btn-danger" onclick="uploadHelper.removeFile('${fileData.id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    removeFile(fileId) {
        this.files = this.files.filter(f => f.id !== fileId);
        this.updateUI();
    }
    
    clearFiles() {
        this.files = [];
        this.updateUI();
    }
    
    async startUpload() {
        if (this.isUploading || this.files.length === 0) return;
        
        this.isUploading = true;
        const processFiles = document.getElementById('process-files').checked;
        const folder = document.getElementById('upload-folder').value;
        
        // Show progress
        document.getElementById('upload-progress').style.display = 'block';
        
        try {
            if (this.files.length === 1) {
                await this.uploadSingle(this.files[0], processFiles, folder);
            } else {
                await this.uploadBatch(processFiles, folder);
            }
        } catch (error) {
            this.showError('Upload failed: ' + error.message);
        } finally {
            this.isUploading = false;
            document.getElementById('upload-progress').style.display = 'none';
        }
    }
    
    async uploadSingle(fileData, processFile, folder) {
        const formData = new FormData();
        formData.append('file', fileData.file);
        formData.append('file_type', fileData.metadata.file_type);
        formData.append('process_file', processFile);
        
        if (folder) {
            formData.append('folder', folder);
        }
        
        const response = await fetch(this.options.endpoint, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            fileData.status = 'completed';
            fileData.progress = 100;
            this.onUploadComplete([data.asset]);
        } else {
            fileData.status = 'error';
            throw new Error(data.error);
        }
        
        this.updateUI();
    }
    
    async uploadBatch(processFiles, folder) {
        const formData = new FormData();
        
        this.files.forEach(fileData => {
            formData.append('files', fileData.file);
        });
        
        formData.append('file_type', this.files[0].metadata.file_type);
        formData.append('process_files', processFiles);
        
        if (folder) {
            formData.append('folder', folder);
        }
        
        const response = await fetch(this.options.batchEndpoint, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            this.files.forEach(fileData => {
                fileData.status = 'completed';
                fileData.progress = 100;
            });
            this.onUploadComplete(data.assets);
        } else {
            this.files.forEach(fileData => {
                fileData.status = 'error';
            });
            throw new Error(data.error);
        }
        
        this.updateUI();
    }
    
    onUploadComplete(assets) {
        // Override this method to handle upload completion
        console.log('Upload completed:', assets);
        
        // Trigger custom event
        if (this.options.container) {
            const container = document.querySelector(this.options.container);
            container.dispatchEvent(new CustomEvent('uploadComplete', {
                detail: { assets }
            }));
        }
    }
    
    showError(message) {
        // Simple error display - can be enhanced
        alert(message);
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
}

// Global instance for easy access
let uploadHelper;
