{% extends "dashboard/dashboard_layout.html" %}

{% block title %}{{ t('nav.comments') }} - Rominext{% endblock %}

{% block dashboard_content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0"><i class="fas fa-comments text-primary me-2"></i> {{ t('nav.comments') }}</h5>
                <p class="text-muted mb-0">نظارت و پاسخ به کامنت‌های پست‌های شما</p>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs mb-3" id="commentsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">همه کامنت‌ها</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="unread-tab" data-bs-toggle="tab" data-bs-target="#unread" type="button" role="tab">خوانده نشده</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="flagged-tab" data-bs-toggle="tab" data-bs-target="#flagged" type="button" role="tab">علامت‌گذاری شده</button>
                    </li>
                </ul>
                
                <div class="tab-content" id="commentsTabContent">
                    <div class="tab-pane fade show active" id="all" role="tabpanel">
                        {% if comments and comments|length > 0 %}
                            {% for comment in comments %}
                            <div class="card mb-3 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">از: {{ comment.from_user.name }}</small>
                                        <span class="badge bg-{{ 'success' if comment.sentiment.sentiment == 'positive' else 'danger' }}">
                                            {{ comment.sentiment.sentiment }}
                                        </span>
                                    </div>
                                    <p class="card-text mt-2">{{ comment.message }}</p>
                                    <div class="mt-2">
                                        <div class="reply-form-{{ comment.id }} d-none">
                                            <textarea class="form-control mb-2" rows="2" placeholder="پاسخ خود را بنویسید..."></textarea>
                                            <div class="text-end">
                                                <button class="btn btn-sm btn-secondary" onclick="hideReplyForm('{{ comment.id }}')">لغو</button>
                                                <button class="btn btn-sm btn-primary" onclick="submitReply('{{ comment.id }}')">ارسال</button>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-end">
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="showReplyForm('{{ comment.id }}')">پاسخ</button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="generateAutoReply('{{ comment.id }}', '{{ comment.message|e }}')">پاسخ خودکار</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-5">
                                <p class="text-muted">کامنتی برای نمایش وجود ندارد.</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="tab-pane fade" id="unread" role="tabpanel">
                        <div class="text-center py-5">
                            <p class="text-muted">کامنت خوانده نشده‌ای وجود ندارد.</p>
                        </div>
                    </div>
                    
                    <div class="tab-pane fade" id="flagged" role="tabpanel">
                        <div class="text-center py-5">
                            <p class="text-muted">کامنت علامت‌گذاری شده‌ای وجود ندارد.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showReplyForm(commentId) {
    document.querySelector(`.reply-form-${commentId}`).classList.remove('d-none');
}

function hideReplyForm(commentId) {
    document.querySelector(`.reply-form-${commentId}`).classList.add('d-none');
}

function submitReply(commentId) {
    const replyText = document.querySelector(`.reply-form-${commentId} textarea`).value;
    fetch(`/comments/${commentId}/reply`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: replyText })
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            location.reload();
        }
    });
}

function generateAutoReply(commentId, commentText) {
    fetch(`/comments/auto-reply/${commentId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ comment: commentText })
    })
    .then(response => response.json())
    .then(data => {
        if (data.reply) {
            location.reload();
        }
    });
}
</script>
{% endblock %}

