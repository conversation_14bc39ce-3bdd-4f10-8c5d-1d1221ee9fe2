{% extends "admin/admin_layout.html" %}

{% block title %}مدیریت کاربران - Rominext{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center py-3">
          <div>
            <h5 class="mb-0">مدیریت کاربران</h5>
            <p class="text-muted mb-0 small">مدیریت و دسترسی کاربران سیستم</p>
          </div>
          <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-1"></i> افزودن کاربر
          </button>
        </div>
        
        <!-- Filters in a single line -->
        <div class="card-body pb-0">
          <div class="d-flex flex-wrap align-items-center gap-2">
            <div class="flex-grow-1 me-auto">
              <input type="text" id="searchInput" class="form-control" placeholder="جستجو: نام یا ایمیل...">
            </div>
            <div class="d-flex align-items-center gap-2">
              <select id="roleFilter" class="form-select" style="width: auto; min-width: 120px;">
                <option value="">نقش: همه</option>
                <option value="ADMIN">مدیر</option>
                <option value="CUSTOMER">کاربر</option>
                <option value="SUPPORT">پشتیبان</option>
              </select>
              
              <select id="statusFilter" class="form-select" style="width: auto; min-width: 120px;">
                <option value="">وضعیت: همه</option>
                <option value="ACTIVE">فعال</option>
                <option value="INACTIVE">غیرفعال</option>
              </select>
              
              <button id="applyFilters" class="btn btn-success">اعمال فیلتر</button>
              <button id="resetFilters" class="btn btn-outline-secondary">حذف فیلتر</button>
            </div>
          </div>
        </div>
        
        <!-- Table -->
        <div class="table-responsive">
          <table class="table align-items-center mb-0">
            <thead>
              <tr>
                <th class="text-end">نام</th>
                <th class="text-end">ایمیل</th>
                <th class="text-end">نقش</th>
                <th class="text-end">وضعیت</th>
                <th class="text-end">تاریخ ثبت نام</th>
                <th class="text-center">عملیات</th>
              </tr>
            </thead>
            <tbody id="usersTableBody">
              {% for user in users %}
              <tr>
                <td>{{ user.name }}</td>
                <td>{{ user.email }}</td>
                <td>
                  {% if user.role.value == 'admin' %}
                  مدیر
                  {% elif user.role.value == 'support' %}
                  پشتیبان
                  {% else %}
                  کاربر
                  {% endif %}
                </td>
                <td>
                  {% if user.is_active %}
                  <span class="badge bg-success">فعال</span>
                  {% else %}
                  <span class="badge bg-danger">غیرفعال</span>
                  {% endif %}
                </td>
                <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                <td class="text-center">
                  <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                      عملیات
                    </button>
                    <ul class="dropdown-menu">
                      <li><a class="dropdown-item" href="#" onclick="editUser('{{ user.id }}')"><i class="fas fa-edit me-1"></i> ویرایش</a></li>
                      <li><a class="dropdown-item" href="#" onclick="resetPassword('{{ user.id }}')"><i class="fas fa-key me-1"></i> بازنشانی رمز عبور</a></li>
                      <li><a class="dropdown-item" href="#" onclick="viewUserDetails('{{ user.id }}')"><i class="fas fa-info-circle me-1"></i> مشاهده جزئیات</a></li>
                      {% if user.is_active %}
                      <li><hr class="dropdown-divider"></li>
                      <li><a class="dropdown-item text-danger" href="#" onclick="toggleUserStatus('{{ user.id }}', false)"><i class="fas fa-ban me-1"></i> غیرفعال کردن</a></li>
                      {% else %}
                      <li><hr class="dropdown-divider"></li>
                      <li><a class="dropdown-item text-success" href="#" onclick="toggleUserStatus('{{ user.id }}', true)"><i class="fas fa-check-circle me-1"></i> فعال کردن</a></li>
                      {% endif %}
                    </ul>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer py-3">
          <div class="d-flex justify-content-between align-items-center">
            <p class="text-sm mb-0">نمایش <span id="currentItems">5</span> از <span id="totalItems">5</span> کاربر</p>
            <nav aria-label="Page navigation">
              <ul class="pagination pagination-sm mb-0">
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item disabled">
                  <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addUserModalLabel">افزودن کاربر جدید</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ url_for('admin.add_user') }}" method="POST">
        <div class="modal-body">
          <div class="mb-3">
            <label for="name" class="form-label">نام</label>
            <input type="text" class="form-control" id="name" name="name" required>
          </div>
          <div class="mb-3">
            <label for="email" class="form-label">ایمیل</label>
            <input type="email" class="form-control" id="email" name="email" required>
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">رمز عبور</label>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>
          <div class="mb-3">
            <label for="role" class="form-label">نقش</label>
            <select class="form-select" id="role" name="role">
              <option value="{{ UserRole.CUSTOMER.value }}">کاربر</option>
              <option value="{{ UserRole.ADMIN.value }}">مدیر</option>
              <option value="{{ UserRole.SUPPORT.value }}">پشتیبان</option>
            </select>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
            <label class="form-check-label" for="is_active">فعال</label>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
          <button type="submit" class="btn btn-primary">ذخیره</button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // State
    let currentPage = 1;
    let totalPages = 1; // Default value
    let totalItems = 5;
    let itemsPerPage = 10;
    
    // Elements
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const resetFiltersBtn = document.getElementById('resetFilters');
    const usersTableBody = document.getElementById('usersTableBody');
    const currentItemsEl = document.getElementById('currentItems');
    const totalItemsEl = document.getElementById('totalItems');
    
    // Update pagination info
    if (currentItemsEl && totalItemsEl) {
      const rowCount = usersTableBody.querySelectorAll('tr').length;
      currentItemsEl.textContent = rowCount;
      totalItemsEl.textContent = rowCount;
    }
    
    // Event Listeners for filters
    if (applyFiltersBtn) {
      applyFiltersBtn.addEventListener('click', function() {
        // In a real implementation, this would call an API with the filter values
        alert('فیلترها اعمال شدند');
      });
    }
    
    if (resetFiltersBtn) {
      resetFiltersBtn.addEventListener('click', function() {
        if (searchInput) searchInput.value = '';
        if (roleFilter) roleFilter.value = '';
        if (statusFilter) statusFilter.value = '';
        
        // In a real implementation, this would reset the table data
        alert('فیلترها حذف شدند');
      });
    }
  });
  
  // Toggle user status function
  function toggleUserStatus(userId, active) {
    if (confirm(active ? 'آیا از فعال کردن این کاربر اطمینان دارید؟' : 'آیا از غیرفعال کردن این کاربر اطمینان دارید؟')) {
      // In a real implementation, this would call an API to toggle the user status
      alert((active ? 'فعال' : 'غیرفعال') + ' کردن کاربر با شناسه ' + userId);
    }
  }
  
  // Edit user function
  function editUser(userId) {
    // Open edit modal or redirect to edit page
    alert('ویرایش کاربر با شناسه ' + userId);
  }
  
  // Reset password function
  function resetPassword(userId) {
    if (confirm('آیا از بازنشانی رمز عبور این کاربر اطمینان دارید؟')) {
      // API call to reset password
      alert('بازنشانی رمز عبور برای کاربر با شناسه ' + userId);
    }
  }
  
  // View user details function
  function viewUserDetails(userId) {
    // Redirect to user details page or open modal
    alert('مشاهده جزئیات کاربر با شناسه ' + userId);
  }
</script>
{% endblock %}
