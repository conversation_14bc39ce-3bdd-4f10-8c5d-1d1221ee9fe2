# Bot Integration Flow Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Bot Communication Patterns](#bot-communication-patterns)
4. [Telegram Bot Integration](#telegram-bot-integration)
5. [Instagram Bot Integration](#instagram-bot-integration)
6. [Bot Hosting Guide](#bot-hosting-guide)
7. [API Endpoints](#api-endpoints)
8. [Environment Configuration](#environment-configuration)
9. [Deployment Strategies](#deployment-strategies)
10. [Troubleshooting](#troubleshooting)

## Overview

Rominext is a comprehensive social media management platform that integrates with various social media platforms through bots and connectors. This document explains how the system communicates with bots (Instagram, Telegram, etc.), how to host these bots, and the interaction patterns between the main system and external platforms.

### Key Components

- **Main Application**: Flask-based web application (`app/`)
- **Bot Services**: Platform-specific bot implementations (`bot/`)
- **API Layer**: RESTful APIs for bot communication (`api/`)
- **Social Connectors**: Platform integration modules (`app/integrations/social/`)
- **Service Layer**: Business logic orchestrators (`app/services/`)

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Main System   │    │   Bot Services  │    │ Social Platforms│
│   (Flask App)   │◄──►│  (Telegram,     │◄──►│ (Telegram,      │
│                 │    │   Instagram)    │    │  Instagram)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   API Layer     │    │   Webhooks      │
│   (MongoDB)     │    │   (REST APIs)   │    │   (Platform     │
│                 │    │                 │    │    Callbacks)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Component Interaction Flow

1. **User Interaction**: Users interact with the main Flask application
2. **Service Layer**: Services orchestrate business logic and coordinate between components
3. **Social Connectors**: Handle platform-specific API interactions
4. **Bot Services**: Independent services that handle real-time bot interactions
5. **API Layer**: Provides endpoints for bot-to-system communication
6. **Database**: Stores user data, configurations, and bot states

## Bot Communication Patterns

### Pattern 1: Direct API Integration (Instagram)
- Uses Instagram Graph API directly through connectors
- No separate bot service required
- Real-time posting and content management
- Webhook support for receiving updates

### Pattern 2: Bot Service Integration (Telegram)
- Separate bot service handles Telegram Bot API
- Communicates with main system via REST APIs
- Supports both polling and webhook modes
- Independent deployment and scaling

### Pattern 3: Hybrid Integration
- Combines direct API access with bot services
- Used for platforms requiring both real-time and batch operations
- Provides flexibility for different use cases

## Telegram Bot Integration

### Architecture Overview

The Telegram bot integration consists of:
- **Telegram Bot Service** (`bot/telegram/telegram_bot.py`)
- **Telegram API Layer** (`api/telegram_api.py`)
- **Telegram Connector** (`app/integrations/social/telegram_connector.py`)

### Communication Flow

```
User ──► Telegram App ──► Telegram Bot ──► Rominext API ──► Main System
                                     │
                                     ▼
                              Database Updates
```

### Bot Features

1. **User Verification**: Handles one-time verification codes
2. **Channel Connection**: Links Telegram channels to user accounts
3. **Message Handling**: Processes commands and forwarded messages
4. **Error Handling**: Comprehensive logging and error recovery

### Key Endpoints

- `/api/telegram/verify-code`: Verify user with one-time code
- `/api/telegram/connect-channel`: Connect Telegram channel
- `/api/telegram/get-connected-channels`: Retrieve user's channels
- `/api/telegram/send-message`: Send messages to channels
- `/api/telegram/set-webhook`: Configure webhook URL
- `/api/telegram/remove-webhook`: Remove webhook configuration

## Instagram Bot Integration

### Architecture Overview

Instagram integration uses direct API access through:
- **Instagram Connector** (`app/integrations/social/instagram_connector.py`)
- **Instagram API Layer** (`api/instagram_api.py`)
- **Social Connector Manager** (`app/services/social_connector_manager.py`)

### Communication Flow

```
Main System ──► Instagram Connector ──► Instagram Graph API ──► Instagram Platform
     │                                                                    │
     ▼                                                                    ▼
Database Updates ◄────────────────── Webhook Callbacks ◄─────────────────┘
```

### Supported Features

1. **Content Posting**: Images, videos, and stories
2. **Content Management**: Edit captions, delete posts
3. **Analytics**: Fetch metrics and insights
4. **Profile Management**: Update profile information
5. **Comment Management**: Post and manage comments

### Key Methods

- `post_content()`: Publish media content
- `edit_content()`: Modify existing posts
- `delete_content()`: Remove posts
- `fetch_metrics()`: Get post analytics
- `fetch_recent_posts()`: Retrieve recent media
- `post_comment()`: Add comments to posts

## Bot Hosting Guide

### Prerequisites

- Docker and Docker Compose
- Environment variables configured
- Platform API credentials
- Network access to external APIs

### Hosting Options

#### Option 1: Docker Compose (Recommended)

1. **Main Application Hosting**:
```bash
# Clone repository
git clone <repository-url>
cd rominext

# Configure environment
cp .env.example .env
# Edit .env with your credentials

# Start services
docker-compose up -d
```

2. **Telegram Bot Hosting**:
```bash
# Navigate to bot directory
cd bot/telegram

# Configure environment
export TELEGRAM_BOT_TOKEN="your_bot_token"
export BACKEND_URL="http://localhost:5000/api/telegram"

# Start bot service
docker-compose up -d
```

#### Option 2: Separate Deployment

1. **Main Application**:
```bash
# Install dependencies
pip install -r requirements.txt

# Configure environment
export FLASK_APP=run.py
export FLASK_ENV=production

# Start application
python run.py
```

2. **Telegram Bot**:
```bash
# Navigate to bot directory
cd bot/telegram

# Install dependencies
pip install -r requirements.txt

# Start bot
python telegram_bot.py
```

### Environment Configuration

#### Main Application (.env)
```env
# Database
MONGODB_URI=mongodb://localhost:27017/rominext

# Social Media APIs
FACEBOOK_PAGE_ACCESS_TOKEN=your_facebook_token
INSTAGRAM_ACCESS_TOKEN=your_instagram_token
TELEGRAM_BOT_TOKEN=your_telegram_token

# AI Providers
OPENAI_API_KEY=your_openai_key
GROQ_API_KEY=your_groq_key

# Security
SECRET_KEY=your_secret_key
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret

# API Configuration
TELEGRAM_API_RATE_LIMIT=100
INSTAGRAM_API_RATE_LIMIT=200
```

#### Telegram Bot Environment
```env
TELEGRAM_BOT_TOKEN=your_bot_token
BACKEND_URL=http://your-domain.com/api/telegram
```

## API Endpoints

### Telegram API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/telegram/verify-code` | POST | Verify user with code |
| `/api/telegram/connect-channel` | POST | Connect Telegram channel |
| `/api/telegram/get-connected-channels` | GET | Get user channels |
| `/api/telegram/send-message` | POST | Send message to channel |
| `/api/telegram/set-webhook` | POST | Configure webhook |
| `/api/telegram/remove-webhook` | POST | Remove webhook |

### Instagram API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/instagram/post` | POST | Create Instagram post |
| `/api/instagram/edit` | PUT | Edit existing post |
| `/api/instagram/delete` | DELETE | Delete post |
| `/api/instagram/metrics` | GET | Get post analytics |
| `/api/instagram/profile` | GET | Get profile info |

## Deployment Strategies

### Production Deployment

1. **Container Orchestration**:
   - Use Docker Swarm or Kubernetes
   - Implement health checks
   - Configure auto-scaling

2. **Load Balancing**:
   - Use nginx or HAProxy
   - Distribute traffic across instances
   - Implement SSL termination

3. **Monitoring**:
   - Set up logging aggregation
   - Implement metrics collection
   - Configure alerting

### Security Considerations

1. **API Security**:
   - Use HTTPS for all communications
   - Implement rate limiting
   - Validate webhook signatures

2. **Credential Management**:
   - Use environment variables
   - Implement secret rotation
   - Secure credential storage

3. **Network Security**:
   - Configure firewalls
   - Use VPNs for internal communication
   - Implement IP whitelisting

## Troubleshooting

### Common Issues

1. **Bot Not Responding**:
   - Check bot token validity
   - Verify network connectivity
   - Review error logs

2. **API Rate Limiting**:
   - Implement exponential backoff
   - Monitor rate limit headers
   - Distribute requests over time

3. **Webhook Issues**:
   - Verify SSL certificate
   - Check webhook URL accessibility
   - Validate webhook signatures

### Debugging Tools

1. **Logging**:
   - Enable debug logging
   - Use structured logging
   - Implement log aggregation

2. **Monitoring**:
   - Set up health checks
   - Monitor API response times
   - Track error rates

3. **Testing**:
   - Use API testing tools
   - Implement integration tests
   - Test webhook endpoints

### Support Resources

- Platform documentation (Telegram Bot API, Instagram Graph API)
- Community forums and support channels
- Internal documentation and runbooks
