{% extends "dashboard/dashboard_layout.html" %}

{% block title %}تولید محتوا - Rominext{% endblock %}

{% block dashboard_content %}
<div class="content-creation-dashboard">
  <!-- Platform Selection Step -->
  <div class="card mb-4" id="platformSelectionCard">
    <div class="card-body">
      <h5 class="card-title mb-3">انتخاب پلتفرم</h5>
      <div class="d-flex flex-wrap gap-2 mb-3">
        <button class="btn platform-btn active" data-platform="all-social">
          <i class="fas fa-share-alt ms-2"></i> همه شبکه‌های اجتماعی
        </button>
        <div class="vr mx-2"></div> <!-- Vertical separator -->
        <button class="btn platform-btn multi-select" data-platform="instagram">
          <i class="fab fa-instagram ms-2"></i> اینستاگرام
        </button>
        <button class="btn platform-btn multi-select" data-platform="telegram">
          <i class="fab fa-telegram ms-2"></i> تلگرام
        </button>
        <button class="btn platform-btn multi-select" data-platform="twitter">
          <i class="fab fa-twitter ms-2"></i> توییتر
        </button>
      </div>
      <div class="d-flex flex-wrap gap-2">
        <button class="btn platform-btn blog-platform" data-platform="blog">
          <i class="fas fa-rss ms-2"></i> بلاگ
        </button>
      </div>
    </div>
  </div>

  <!-- Creation Mode Selection (initially hidden) -->
  <div class="card mb-4" id="creationModeCard" style="display: none;">
    <div class="card-body">
      <h5 class="card-title mb-3">روش تولید محتوا</h5>
      <div class="d-flex flex-wrap gap-2">
        <button class="btn creation-mode-btn" data-mode="ai-assisted">
          <i class="fas fa-robot ms-2"></i> کاملا با هوش مصنوعی
        </button>
        <button class="btn creation-mode-btn" data-mode="manual">
          <i class="fas fa-edit ms-2"></i> کاملا دستی
        </button>
        <button class="btn creation-mode-btn" data-mode="template">
          <i class="fas fa-copy ms-2"></i> ترکیب دستی و هوش مصنوعی
        </button>
      </div>
    </div>
  </div>

  <!-- Content Creation Area -->
  <div class="row">
    <!-- Input Form -->
    <div class="col-md-6">
      <div class="card h-100">
        <div class="card-body d-flex flex-column">
          <h5 class="card-title mb-3">اطلاعات محتوا</h5>
          
          <!-- Platform Tabs for Input Form -->
          <ul class="nav nav-tabs mb-3" id="inputFormTabs" role="tablist">
            <li class="nav-item social-platform-tab" role="presentation">
              <button class="nav-link active" id="instagram-input-tab" data-bs-toggle="tab" data-bs-target="#instagram-input" type="button" role="tab">
                <i class="fab fa-instagram ms-1"></i> اینستاگرام
              </button>
            </li>
            <li class="nav-item social-platform-tab" role="presentation">
              <button class="nav-link" id="telegram-input-tab" data-bs-toggle="tab" data-bs-target="#telegram-input" type="button" role="tab">
                <i class="fab fa-telegram ms-1"></i> تلگرام
              </button>
            </li>
            <li class="nav-item social-platform-tab" role="presentation">
              <button class="nav-link" id="twitter-input-tab" data-bs-toggle="tab" data-bs-target="#twitter-input" type="button" role="tab">
                <i class="fab fa-twitter ms-1"></i> توییتر
              </button>
            </li>
            <li class="nav-item blog-platform-tab" role="presentation">
              <button class="nav-link" id="blog-input-tab" data-bs-toggle="tab" data-bs-target="#blog-input" type="button" role="tab">
                <i class="fas fa-rss ms-1"></i> بلاگ
              </button>
            </li>
          </ul>
          
          <!-- Content Creation Form -->
          <form id="contentCreationForm" class="flex-grow-1">
            <!-- Common fields -->
            <div class="mb-3">
              <label class="form-label">موضوع / هدف</label>
              <input type="text" class="form-control" name="topic" placeholder="موضوع محتوا را وارد کنید...">
            </div>
            
            <div class="mb-3">
              <label class="form-label">لحن و سبک</label>
              <select class="form-select" name="tone">
                <option value="professional">رسمی</option>
                <option value="casual">غیررسمی</option>
                <option value="friendly">دوستانه</option>
                <option value="humorous">طنز</option>
                <option value="educational">آموزشی</option>
              </select>
            </div>
            
            <!-- Tab Content for Input Form -->
            <div class="tab-content" id="inputFormTabsContent">
              <!-- Instagram Input -->
              <div class="tab-pane fade show active" id="instagram-input" role="tabpanel">
                <div class="mb-3">
                  <label class="form-label">کپشن اینستاگرام</label>
                  <textarea class="form-control" name="instagram_caption" rows="3" placeholder="کپشن اینستاگرام را وارد کنید..."></textarea>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">هشتگ‌ها</label>
                  <input type="text" class="form-control" name="instagram_hashtags" placeholder="هشتگ‌ها را وارد کنید...">
                  <button type="button" class="btn btn-sm btn-outline-secondary mt-2 ai-field">
                    <i class="fas fa-magic ms-1"></i> پیشنهاد هشتگ
                  </button>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">منشن‌ها</label>
                  <input type="text" class="form-control" name="instagram_mentions" placeholder="@username1 @username2">
                </div>
                
                <div class="mb-3">
                  <label class="form-label">موقعیت مکانی</label>
                  <input type="text" class="form-control" name="instagram_location" placeholder="افزودن موقعیت مکانی...">
                </div>
              </div>
              
              <!-- Telegram Input -->
              <div class="tab-pane fade" id="telegram-input" role="tabpanel">
                <div class="mb-3">
                  <label class="form-label">متن تلگرام</label>
                  <textarea class="form-control" name="telegram_text" rows="4" placeholder="متن پیام تلگرام را وارد کنید..."></textarea>
                  <small class="text-muted">از فرمت‌های Markdown برای متن پشتیبانی می‌شود</small>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">دکمه‌های اینلاین</label>
                  <div id="telegram-buttons-container">
                    <div class="input-group mb-2">
                      <input type="text" class="form-control" placeholder="متن دکمه" name="button_text[]">
                      <input type="text" class="form-control" placeholder="لینک" name="button_url[]">
                      <button type="button" class="btn btn-outline-danger" onclick="removeButton(this)">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                  <button type="button" class="btn btn-sm btn-outline-secondary" onclick="addTelegramButton()">
                    <i class="fas fa-plus ms-1"></i> افزودن دکمه
                  </button>
                </div>
                
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="disableNotification" name="disable_notification">
                    <label class="form-check-label" for="disableNotification">
                      ارسال بدون اعلان
                    </label>
                  </div>
                </div>
              </div>
              
              <!-- Twitter Input -->
              <div class="tab-pane fade" id="twitter-input" role="tabpanel">
                <div class="mb-3">
                  <label class="form-label">متن توییت</label>
                  <textarea class="form-control" name="twitter_text" rows="3" placeholder="متن توییت را وارد کنید..." maxlength="280"></textarea>
                  <div class="d-flex justify-content-between">
                    <small class="text-muted">حداکثر ۲۸۰ کاراکتر</small>
                    <small id="twitterCharCount" class="text-muted">۰/۲۸۰</small>
                  </div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">هشتگ‌ها</label>
                  <input type="text" class="form-control" name="twitter_hashtags" placeholder="هشتگ‌ها را وارد کنید...">
                </div>
                
                <div class="mb-3">
                  <label class="form-label">منشن‌ها</label>
                  <input type="text" class="form-control" name="twitter_mentions" placeholder="@username1 @username2">
                </div>
                
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="allowReplies" name="allow_replies" checked>
                    <label class="form-check-label" for="allowReplies">
                      اجازه پاسخ‌دهی
                    </label>
                  </div>
                </div>
              </div>
              
              <!-- Blog Input -->
              <div class="tab-pane fade" id="blog-input" role="tabpanel">
                <div class="mb-3">
                  <label class="form-label">عنوان مقاله</label>
                  <input type="text" class="form-control" name="blog_title" placeholder="عنوان مقاله را وارد کنید...">
                </div>
                
                <div class="mb-3">
                  <label class="form-label">خلاصه مقاله</label>
                  <textarea class="form-control" name="blog_excerpt" rows="2" placeholder="خلاصه‌ای از مقاله را وارد کنید..."></textarea>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">متن مقاله</label>
                  <textarea class="form-control" name="blog_content" rows="6" placeholder="متن مقاله را وارد کنید..."></textarea>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">برچسب‌ها</label>
                  <input type="text" class="form-control" name="blog_tags" placeholder="برچسب‌ها را با کاما جدا کنید...">
                </div>
                
                <div class="mb-3">
                  <label class="form-label">دسته‌بندی</label>
                  <select class="form-select" name="blog_category">
                    <option value="">انتخاب دسته‌بندی...</option>
                    <option value="technology">تکنولوژی</option>
                    <option value="business">کسب و کار</option>
                    <option value="marketing">بازاریابی</option>
                    <option value="lifestyle">سبک زندگی</option>
                  </select>
                </div>
              </div>
            </div>
            
            <!-- AI-specific fields -->
            <div class="mb-3 ai-field" style="display: none;">
              <label class="form-label">متن مرجع / لینک‌ها</label>
              <textarea class="form-control" name="referenceText" rows="3" placeholder="متن مرجع یا لینک‌های مرتبط را وارد کنید..."></textarea>
            </div>
            
            <!-- Common fields for all platforms -->
            <div class="mb-3">
              <label class="form-label">توضیحات تصویر</label>
              <textarea class="form-control" name="imagePreferences" rows="2" placeholder="توضیحات تصویر مورد نظر را وارد کنید..."></textarea>
            </div>
            
            <button type="button" id="generateContentBtn" class="btn btn-primary w-100">
              <i class="fas fa-magic ms-2"></i> تولید محتوا
            </button>
          </form>
        </div>
      </div>
    </div>
    
    <!-- Preview Area -->
    <div class="col-md-6">
      <div class="card h-100">
        <div class="card-body d-flex flex-column">
          <h5 class="card-title mb-3">پیش‌نمایش محتوا</h5>
          
          <!-- Platform Tabs -->
          <ul class="nav nav-tabs mb-3" id="platformTabs" role="tablist">
            <li class="nav-item social-platform-tab" role="presentation">
              <button class="nav-link active" id="instagram-tab" data-bs-toggle="tab" data-bs-target="#instagram" type="button" role="tab">
                <i class="fab fa-instagram ms-1"></i> اینستاگرام
              </button>
            </li>
            <li class="nav-item social-platform-tab" role="presentation">
              <button class="nav-link" id="telegram-tab" data-bs-toggle="tab" data-bs-target="#telegram" type="button" role="tab">
                <i class="fab fa-telegram ms-1"></i> تلگرام
              </button>
            </li>
            <li class="nav-item social-platform-tab" role="presentation">
              <button class="nav-link" id="twitter-tab" data-bs-toggle="tab" data-bs-target="#twitter" type="button" role="tab">
                <i class="fab fa-twitter ms-1"></i> توییتر
              </button>
            </li>
            <li class="nav-item blog-platform-tab" role="presentation">
              <button class="nav-link" id="blog-tab" data-bs-toggle="tab" data-bs-target="#blog" type="button" role="tab">
                <i class="fas fa-rss ms-1"></i> بلاگ
              </button>
            </li>
          </ul>
          
          <!-- Tab Content -->
          <div class="tab-content flex-grow-1" id="platformTabsContent">
            <!-- Instagram Preview -->
            <div class="tab-pane fade show active" id="instagram" role="tabpanel">
              <div class="instagram-preview border rounded-md overflow-hidden">
                <div class="bg-gray-50 p-2 border-b d-flex align-items-center">
                  <div class="rounded-circle bg-gray-300 me-2" style="width: 32px; height: 32px;"></div>
                  <div class="fw-medium">نام کاربری</div>
                </div>
                <div id="instagramImage" class="bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                  <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                <div class="p-3">
                  <p id="instagramText" class="mb-2">محتوای تولید شده اینجا نمایش داده می‌شود...</p>
                  <div id="instagramHashtags" class="text-primary"></div>
                </div>
              </div>
            </div>
            
            <!-- Telegram Preview -->
            <div class="tab-pane fade" id="telegram" role="tabpanel">
              <div class="telegram-preview border rounded p-3">
                <div class="d-flex align-items-center mb-2">
                  <div class="rounded-circle bg-gray-300 me-2" style="width: 40px; height: 40px;"></div>
                  <div>
                    <div class="fw-bold">نام کانال</div>
                    <div class="text-muted small">امروز</div>
                  </div>
                </div>
                <p id="telegramText" class="mb-2">محتوای تولید شده اینجا نمایش داده می‌شود...</p>
                <div id="telegramImage" class="bg-light d-flex align-items-center justify-content-center mb-2" style="height: 200px;">
                  <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                <div class="d-flex justify-content-between text-muted">
                  <span><i class="far fa-eye"></i> 0</span>
                  <span><i class="far fa-share-square"></i> 0</span>
                </div>
              </div>
            </div>
            
            <!-- Blog Preview -->
            <div class="tab-pane fade" id="blog" role="tabpanel">
              <div class="blog-preview border rounded p-3">
                <h3 id="blogTitle" class="mb-3">عنوان مقاله</h3>
                <div id="blogImage" class="bg-light d-flex align-items-center justify-content-center mb-3" style="height: 200px;">
                  <i class="fas fa-image fa-3x text-muted"></i>
                </div>
                <div id="blogContent">محتوای تولید شده اینجا نمایش داده می‌شود...</div>
                <div class="mt-3 text-muted">
                  <span class="me-3"><i class="far fa-clock me-1"></i> زمان مطالعه: ۵ دقیقه</span>
                  <span><i class="far fa-folder me-1"></i> دسته‌بندی</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Content Actions -->
          <div class="mt-auto pt-3">
            <div class="d-flex flex-wrap gap-2">
              <button class="btn btn-outline-primary" id="editContentBtn">
                <i class="fas fa-edit ms-1"></i> ویرایش
              </button>
              <button class="btn btn-outline-secondary" id="regenerateBtn">
                <i class="fas fa-sync-alt ms-1"></i> تولید مجدد
              </button>
              <button class="btn btn-outline-success" id="enhanceBtn">
                <i class="fas fa-magic ms-1"></i> بهبود محتوا
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Scheduling Panel -->
  <div class="card mt-4">
    <div class="card-body">
      <h5 class="card-title mb-3">زمان‌بندی انتشار</h5>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">تاریخ انتشار</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-calendar"></i></span>
              <input type="date" class="form-control" id="publishDate">
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">زمان انتشار</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-clock"></i></span>
              <input type="time" class="form-control" id="publishTime">
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end gap-2">
        <button class="btn btn-success" id="publishNowBtn">
          <i class="fas fa-paper-plane ms-1"></i> انتشار فوری
        </button>
        <button class="btn btn-primary" id="scheduleBtn">
          <i class="fas fa-calendar-check ms-1"></i> زمان‌بندی انتشار
        </button>
      </div>
    </div>
  </div>
  
  <!-- Smart Tips (moved to end of page) -->
  <div class="card mt-4 smart-tips-card bg-gradient-light border-0 shadow-sm">
    <div class="card-body">
      <h5 class="card-title mb-3"><i class="fas fa-brain text-primary me-2"></i> نکات هوشمند</h5>
      <div id="smartTips">
        <!-- Default tips -->
        <div class="tip-item mb-3 p-2 rounded bg-light-subtle border-start border-4 border-warning">
          <i class="fas fa-lightbulb text-warning me-2 fa-lg"></i>
          <span>افزودن هشتگ‌های مرتبط می‌تواند دسترسی‌پذیری پست شما را افزایش دهد.</span>
        </div>
        <div class="tip-item mb-3 p-2 rounded bg-light-subtle border-start border-4 border-info">
          <i class="fas fa-clock text-info me-2 fa-lg"></i>
          <span>بهترین زمان انتشار برای مخاطبان شما ساعت ۱۸ تا ۲۰ است.</span>
        </div>
        
        <!-- Instagram-specific tips -->
        <div class="tip-item mb-3 p-2 rounded bg-light-subtle border-start border-4 border-primary instagram-tip" style="display: none;">
          <i class="fab fa-instagram text-primary me-2 fa-lg"></i>
          <span>استفاده از ۵ تا ۹ هشتگ در اینستاگرام بهترین نتیجه را دارد.</span>
        </div>
        
        <!-- Telegram-specific tips -->
        <div class="tip-item mb-3 p-2 rounded bg-light-subtle border-start border-4 border-primary telegram-tip" style="display: none;">
          <i class="fab fa-telegram text-primary me-2 fa-lg"></i>
          <span>استفاده از دکمه‌های اینلاین در تلگرام می‌تواند تعامل کاربران را افزایش دهد.</span>
        </div>
        
        <!-- Blog-specific tips -->
        <div class="tip-item mb-3 p-2 rounded bg-light-subtle border-start border-4 border-primary blog-tip" style="display: none;">
          <i class="fas fa-rss text-primary me-2 fa-lg"></i>
          <span>مقالات با طول ۱۵۰۰ تا ۲۵۰۰ کلمه بهترین رتبه‌بندی را در موتورهای جستجو دارند.</span>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Platform selection
  const platformBtns = document.querySelectorAll('.platform-btn');
  const creationModeCard = document.getElementById('creationModeCard');
  const contentCreationForm = document.getElementById('contentCreationForm');
  
  // Sync tabs between input form and preview
  const inputTabs = document.querySelectorAll('#inputFormTabs .nav-link');
  const previewTabs = document.querySelectorAll('#platformTabs .nav-link');
  
  // Add click event listeners to input tabs
  inputTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Deactivate all input tabs
      inputTabs.forEach(t => {
        t.classList.remove('active');
        const target = document.querySelector(t.getAttribute('data-bs-target'));
        if (target) target.classList.remove('show', 'active');
      });
      
      // Activate this tab
      this.classList.add('active');
      const target = document.querySelector(this.getAttribute('data-bs-target'));
      if (target) target.classList.add('show', 'active');
      
      // Get the corresponding platform
      const platform = this.id.replace('-input-tab', '');
      
      // Deactivate all preview tabs
      previewTabs.forEach(t => {
        t.classList.remove('active');
        const target = document.querySelector(t.getAttribute('data-bs-target'));
        if (target) target.classList.remove('show', 'active');
      });
      
      // Activate the corresponding preview tab
      const previewTab = document.querySelector(`#${platform}-tab`);
      if (previewTab) {
        previewTab.classList.add('active');
        const target = document.querySelector(previewTab.getAttribute('data-bs-target'));
        if (target) target.classList.add('show', 'active');
      }
    });
  });
  
  // Add click event listeners to preview tabs
  previewTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Deactivate all preview tabs
      previewTabs.forEach(t => {
        t.classList.remove('active');
        const target = document.querySelector(t.getAttribute('data-bs-target'));
        if (target) target.classList.remove('show', 'active');
      });
      
      // Activate this tab
      this.classList.add('active');
      const target = document.querySelector(this.getAttribute('data-bs-target'));
      if (target) target.classList.add('show', 'active');
      
      // Get the platform
      const platform = this.id.replace('-tab', '');
      
      // Deactivate all input tabs
      inputTabs.forEach(t => {
        t.classList.remove('active');
        const target = document.querySelector(t.getAttribute('data-bs-target'));
        if (target) target.classList.remove('show', 'active');
      });
      
      // Activate the corresponding input tab
      const inputTab = document.querySelector(`#${platform}-input-tab`);
      if (inputTab) {
        inputTab.classList.add('active');
        const target = document.querySelector(inputTab.getAttribute('data-bs-target'));
        if (target) target.classList.add('show', 'active');
      }
    });
  });
  
  // Auto-select All Social Networks platform on page load
  const allSocialBtn = document.querySelector('[data-platform="all-social"]');
  if (allSocialBtn) {
    allSocialBtn.classList.add('active');
    creationModeCard.style.display = 'block';
    updateFormForSelectedPlatforms();
  }
  
  // Auto-select AI-assisted creation mode on page load
  const aiAssistedBtn = document.querySelector('[data-mode="ai-assisted"]');
  if (aiAssistedBtn) {
    aiAssistedBtn.classList.add('active');
    
    // Show content form
    if (contentCreationForm) {
      contentCreationForm.style.display = 'block';
    }
    
    // Update form for AI-assisted mode
    updateFormForCreationMode('ai-assisted');
  }
  
  // Platform selection handler
  platformBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const platform = this.getAttribute('data-platform');
      
      if (platform === 'all-social') {
        // If "all social" is clicked, deselect all others
        platformBtns.forEach(b => b.classList.remove('active'));
        this.classList.add('active');
      } else if (this.classList.contains('multi-select')) {
        // For multi-select platforms, toggle active state
        document.querySelector('[data-platform="all-social"]').classList.remove('active');
        this.classList.toggle('active');
        
        // Check if at least one platform is selected
        const hasActivePlatform = document.querySelector('.platform-btn.active') !== null;
        if (!hasActivePlatform) {
          // If no platform is selected, re-select this one
          this.classList.add('active');
        }
      } else {
        // For blog, deselect all others
        platformBtns.forEach(b => b.classList.remove('active'));
        this.classList.add('active');
      }
      
      // Show creation mode selection if at least one platform is selected
      const hasActivePlatform = document.querySelector('.platform-btn.active') !== null;
      creationModeCard.style.display = hasActivePlatform ? 'block' : 'none';
      
      // Update form fields based on selected platforms
      updateFormForSelectedPlatforms();
    });
  });
  
  // Creation mode selection
  const creationModeBtns = document.querySelectorAll('.creation-mode-btn');
  creationModeBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      creationModeBtns.forEach(b => b.classList.remove('active'));
      this.classList.add('active');
      
      // Show content form
      if (contentCreationForm) {
        contentCreationForm.style.display = 'block';
      }
      
      // Update form based on creation mode
      const mode = this.getAttribute('data-mode');
      updateFormForCreationMode(mode);
    });
  });
  
  // Function to update form fields and tabs based on selected platforms
  function updateFormForSelectedPlatforms() {
    // Get all active platforms
    const activePlatformButtons = document.querySelectorAll('.platform-btn.active');
    const selectedPlatforms = Array.from(activePlatformButtons).map(btn => 
      btn.getAttribute('data-platform')
    );
    
    // Check if "all-social" is selected
    const isAllSocial = selectedPlatforms.includes('all-social');
    
    // Get platform-specific elements
    const instagramFields = document.querySelectorAll('.instagram-field');
    const telegramFields = document.querySelectorAll('.telegram-field');
    const twitterFields = document.querySelectorAll('.twitter-field');
    const blogFields = document.querySelectorAll('.blog-field');
    
    // Get all platform tabs
    const socialPlatformTabs = document.querySelectorAll('.social-platform-tab');
    const blogPlatformTab = document.querySelectorAll('.blog-platform-tab');
    
    // Get input form tabs
    const inputFormTabs = document.querySelectorAll('#inputFormTabs .nav-item');
    const previewTabs = document.querySelectorAll('#platformTabs .nav-item');
    
    // Hide all platform-specific fields
    [instagramFields, telegramFields, twitterFields, blogFields].forEach(fields => {
      fields.forEach(field => field.style.display = 'none');
    });
    
    // Hide all tabs first
    socialPlatformTabs.forEach(tab => tab.style.display = 'none');
    blogPlatformTab.forEach(tab => tab.style.display = 'none');
    
    // Reset all tab states
    document.querySelectorAll('#inputFormTabs .nav-link, #platformTabs .nav-link').forEach(tab => {
      tab.classList.remove('active');
      const tabTarget = document.querySelector(tab.getAttribute('data-bs-target'));
      if (tabTarget) tabTarget.classList.remove('show', 'active');
    });
    
    if (isAllSocial) {
      // Show all social media tabs
      socialPlatformTabs.forEach(tab => tab.style.display = 'block');
      
      // Activate Instagram tabs by default when all social is selected
      document.querySelector('#instagram-tab').classList.add('active');
      document.querySelector('#instagram').classList.add('show', 'active');
      document.querySelector('#instagram-input-tab').classList.add('active');
      document.querySelector('#instagram-input').classList.add('show', 'active');
      
      // Show Instagram fields by default
      instagramFields.forEach(field => field.style.display = 'block');
    } else if (selectedPlatforms.includes('blog')) {
      // Show only Blog tab
      blogPlatformTab.forEach(tab => tab.style.display = 'block');
      
      // Activate Blog tabs
      document.querySelector('#blog-tab').classList.add('active');
      document.querySelector('#blog').classList.add('show', 'active');
      document.querySelector('#blog-input-tab').classList.add('active');
      document.querySelector('#blog-input').classList.add('show', 'active');
      
      // Show Blog fields
      blogFields.forEach(field => field.style.display = 'block');
    } else {
      // Show selected platform tabs and fields
      if (selectedPlatforms.includes('instagram')) {
        document.querySelector('#instagram-tab').parentElement.style.display = 'block';
        document.querySelector('#instagram-input-tab').parentElement.style.display = 'block';
      }
      
      if (selectedPlatforms.includes('telegram')) {
        document.querySelector('#telegram-tab').parentElement.style.display = 'block';
        document.querySelector('#telegram-input-tab').parentElement.style.display = 'block';
      }
      
      if (selectedPlatforms.includes('twitter')) {
        document.querySelector('#twitter-tab').parentElement.style.display = 'block';
        document.querySelector('#twitter-input-tab').parentElement.style.display = 'block';
      }
      
      // Activate the first selected platform tab
      if (selectedPlatforms.length > 0) {
        const firstPlatform = selectedPlatforms[0];
        if (firstPlatform !== 'all-social') {
          document.querySelector(`#${firstPlatform}-tab`).classList.add('active');
          document.querySelector(`#${firstPlatform}`).classList.add('show', 'active');
          document.querySelector(`#${firstPlatform}-input-tab`).classList.add('active');
          document.querySelector(`#${firstPlatform}-input`).classList.add('show', 'active');
          
          // Show fields for the first selected platform
          if (firstPlatform === 'instagram') {
            instagramFields.forEach(field => field.style.display = 'block');
          } else if (firstPlatform === 'telegram') {
            telegramFields.forEach(field => field.style.display = 'block');
          } else if (firstPlatform === 'twitter') {
            twitterFields.forEach(field => field.style.display = 'block');
          }
        }
      }
    }
    
    // Update tips based on selected platforms
    updateTipsForSelectedPlatforms(selectedPlatforms);
  }
  
  // Function to update form based on creation mode
  function updateFormForCreationMode(mode) {
    // Get mode-specific elements
    const aiFields = document.querySelectorAll('.ai-field');
    const manualFields = document.querySelectorAll('.manual-field');
    const templateFields = document.querySelectorAll('.template-field');
    
    // Hide all mode-specific fields
    [aiFields, manualFields, templateFields].forEach(fields => {
      fields.forEach(field => field.style.display = 'none');
    });
    
    // Show mode-specific fields
    switch(mode) {
      case 'ai-assisted':
        aiFields.forEach(field => field.style.display = 'block');
        break;
      case 'manual':
        manualFields.forEach(field => field.style.display = 'block');
        break;
      case 'template':
        templateFields.forEach(field => field.style.display = 'block');
        break;
    }
  }
  
  // Function to update preview tabs based on selected platforms
  function updatePreviewTabs(selectedPlatforms) {
    const platformTabs = document.getElementById('platformTabs');
    const tabLinks = platformTabs.querySelectorAll('.nav-link');
    
    // Hide all tabs
    tabLinks.forEach(tab => {
      const tabTarget = document.querySelector(tab.getAttribute('data-bs-target'));
      tab.classList.remove('active');
      if (tabTarget) tabTarget.classList.remove('show', 'active');
    });
    
    // Show selected platform tabs
    if (selectedPlatforms.includes('all-social')) {
      // Show first social tab as active
      const firstSocialTab = document.querySelector('.social-platform-tab .nav-link');
      if (firstSocialTab) {
        firstSocialTab.classList.add('active');
        const tabTarget = document.querySelector(firstSocialTab.getAttribute('data-bs-target'));
        if (tabTarget) tabTarget.classList.add('show', 'active');
      }
    } else {
      // Show first selected platform tab as active
      let firstActiveTabSet = false;
      
      selectedPlatforms.forEach(platform => {
        if (platform !== 'blog') {
          const activeTab = document.getElementById(`${platform}-tab`);
          if (activeTab) {
            if (!firstActiveTabSet) {
              activeTab.classList.add('active');
              const tabTarget = document.querySelector(activeTab.getAttribute('data-bs-target'));
              if (tabTarget) tabTarget.classList.add('show', 'active');
              firstActiveTabSet = true;
            }
          }
        }
      });
    }
  }

  // Function to update tips based on selected platforms
  function updateTipsForSelectedPlatforms(selectedPlatforms) {
    const instagramTips = document.querySelectorAll('.instagram-tip');
    const telegramTips = document.querySelectorAll('.telegram-tip');
    const twitterTips = document.querySelectorAll('.twitter-tip');
    const blogTips = document.querySelectorAll('.blog-tip');
    
    // Hide all platform-specific tips
    [instagramTips, telegramTips, twitterTips, blogTips].forEach(tips => {
      tips.forEach(tip => tip.style.display = 'none');
    });
    
    // Show platform-specific tips
    if (selectedPlatforms.includes('all-social')) {
      // Show all social media tips
      instagramTips.forEach(tip => tip.style.display = 'block');
      telegramTips.forEach(tip => tip.style.display = 'block');
      twitterTips.forEach(tip => tip.style.display = 'block');
    } else {
      if (selectedPlatforms.includes('instagram')) {
        instagramTips.forEach(tip => tip.style.display = 'block');
      }
      
      if (selectedPlatforms.includes('telegram')) {
        telegramTips.forEach(tip => tip.style.display = 'block');
      }
      
      if (selectedPlatforms.includes('twitter')) {
        twitterTips.forEach(tip => tip.style.display = 'block');
      }
      
      if (selectedPlatforms.includes('blog')) {
        blogTips.forEach(tip => tip.style.display = 'block');
      }
    }
  }
  
  // Generate content button
  document.getElementById('generateContentBtn').addEventListener('click', function() {
    // Show loading state
    this.innerHTML = '<span class="spinner-border spinner-border-sm ms-2"></span> در حال تولید...';
    this.disabled = true;
    
    // Get form data
    const formData = new FormData(document.getElementById('contentCreationForm'));
    const data = Object.fromEntries(formData.entries());
    
    // Mock API call - replace with actual API call
    setTimeout(() => {
      // Reset button state
      this.innerHTML = '<i class="fas fa-magic ms-2"></i> تولید محتوا';
      this.disabled = false;
      
      // Update previews with generated content
      updatePreviews({
        instagram: {
          text: `محتوای تولید شده برای ${data.topic} با لحن ${data.tone}.`,
          hashtags: '#محتوا #هوش_مصنوعی #رومینکست'
        },
        telegram: {
          text: `محتوای تولید شده برای ${data.topic} با لحن ${data.tone}.`
        },
        blog: {
          title: data.topic || 'عنوان مقاله',
          content: `محتوای تولید شده برای ${data.topic} با لحن ${data.tone}. این یک متن نمونه است که با هوش مصنوعی تولید شده است.`
        }
      });
      
      // Show success message
      showToast('محتوا با موفقیت تولید شد!', 'success');
    }, 2000);
  });
  
  // Update preview content
  function updatePreviews(content) {
    // Instagram
    document.getElementById('instagramText').textContent = content.instagram.text;
    document.getElementById('instagramHashtags').textContent = content.instagram.hashtags;
    
    // Telegram
    document.getElementById('telegramText').textContent = content.telegram.text;
    
    // Blog
    document.getElementById('blogTitle').textContent = content.blog.title;
    document.getElementById('blogContent').textContent = content.blog.content;
  }
  
  // Toast notification function
  function showToast(message, type = 'info') {
    // Implement toast notification logic
    console.log(`${type}: ${message}`);
  }
  
  // Publish now button
  document.getElementById('publishNowBtn').addEventListener('click', function() {
    showToast('محتوا با موفقیت منتشر شد!', 'success');
  });
  
  // Schedule button
  document.getElementById('scheduleBtn').addEventListener('click', function() {
    const date = document.getElementById('publishDate').value;
    const time = document.getElementById('publishTime').value;
    
    if (!date || !time) {
      showToast('لطفا تاریخ و زمان انتشار را مشخص کنید.', 'warning');
      return;
    }
    
    showToast(`محتوا برای انتشار در تاریخ ${date} ساعت ${time} زمان‌بندی شد.`, 'success');
  });
});

// Function to add Telegram button
function addTelegramButton() {
  const container = document.getElementById('telegram-buttons-container');
  const newButtonGroup = document.createElement('div');
  newButtonGroup.className = 'input-group mb-2';
  newButtonGroup.innerHTML = `
    <input type="text" class="form-control" placeholder="متن دکمه" name="button_text[]">
    <input type="text" class="form-control" placeholder="لینک" name="button_url[]">
    <button type="button" class="btn btn-outline-danger" onclick="removeButton(this)">
      <i class="fas fa-times"></i>
    </button>
  `;
  container.appendChild(newButtonGroup);
}

// Function to remove Telegram button
function removeButton(button) {
  const buttonGroup = button.closest('.input-group');
  buttonGroup.remove();
}
</script>
{% endblock %}











