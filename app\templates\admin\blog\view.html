{% extends "main_layout.html" %}

{% block title %}{{ blog.title }} - Rominext{% endblock %}

{% block content %}
<article class="blog-detail-container">
    <!-- Blog Header Section -->
    <section class="blog-header-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="blog-header-content text-center">
                        <h1 class="blog-title display-4 fw-bold mb-4">{{ blog.title }}</h1>
                        <div class="blog-meta d-flex justify-content-center align-items-center flex-wrap gap-3 mb-4">
                            {% if blog.published_at %}
                            <div class="meta-item d-flex align-items-center">
                                <i class="far fa-calendar-alt text-primary me-2"></i>
                                <span class="text-muted">{{ t('blog.published_on') }} {{ blog.published_at.strftime('%Y/%m/%d') }}</span>
                            </div>
                            {% else %}
                            <div class="meta-item">
                                <span class="badge bg-warning text-dark">پیش‌نویس</span>
                            </div>
                            {% endif %}

                            {% if blog.user %}
                            <div class="meta-item d-flex align-items-center">
                                <i class="far fa-user text-primary me-2"></i>
                                <span class="text-muted">{{ blog.user.name }}</span>
                            </div>
                            {% endif %}

                            <!-- Admin/Author Controls -->
                            {% if current_user.is_authenticated and (current_user.role.value == 'admin' or current_user.id == blog.user.id) %}
                            <div class="meta-item">
                                <a href="{{ url_for('blog.edit', blog_id=blog.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i>ویرایش
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Content Section -->
    <section class="blog-content-section py-4">
        <div class="container">
            <div class="row">
                <div class="blog-content">
                    {{ blog.content|safe }}
                </div>
            </div>
        </div>
    </section>

    <!-- Tags Section -->
    {% if blog.tags %}
    <section class="py-4">
        <div class="container">
            <div class="row">
                <div class="col-lg-10 col-xl-8">
                    <div class="tags-wrapper">
                        <h6 class="tags-title mb-3 text-muted">برچسب‌ها:</h6>
                        <div class="tags-list">
                            {% for tag in blog.tags %}
                                <span class="badge bg-primary me-2 mb-2 px-3 py-2">{{ tag }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Admin Back Button (only for admin/author) -->
    {% if current_user.is_authenticated and (current_user.role.value == 'admin' or current_user.id == blog.user.id) %}
    <section class="admin-controls py-4">
        <div class="container">
            <div class="row">
                <div class="col-lg-10 col-xl-8">
                    <div class="admin-controls-wrapper">
                        <a href="{{ url_for('blog.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>{{ t('blog.back_to_blog') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}
</article>

<style>
    .blog-detail-container {
        background: #ffffff;
    }

    .blog-header-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;
    }

    .blog-header-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        opacity: 0.5;
    }

    .blog-header-content {
        position: relative;
        z-index: 1;
    }

    .blog-title {
        color: #2c3e50;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .blog-meta .meta-item {
        background: rgba(255, 255, 255, 0.9);
        padding: 0.5rem 1rem;
        border-radius: 25px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .featured-image-wrapper {
        position: relative;
        overflow: hidden;
    }

    .featured-image-wrapper img {
        transition: transform 0.3s ease;
    }

    .featured-image-wrapper:hover img {
        transform: scale(1.02);
    }

    .blog-content-wrapper {
        background: #ffffff;
        padding: 2rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .blog-content {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #2c3e50;
        text-align: right;
    }

    .blog-content h1, .blog-content h2, .blog-content h3,
    .blog-content h4, .blog-content h5, .blog-content h6 {
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
        text-align: right;
    }

    .blog-content p {
        margin-bottom: 1.5rem;
        text-align: right;
    }

    .blog-content img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
        margin: 1.5rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .blog-tags-section {
        border-top: 1px solid rgba(0,0,0,0.1);
    }

    .tags-title {
        font-weight: 600;
        color: #6c757d;
    }

    .tags-list .badge {
        font-size: 0.9rem;
        font-weight: 500;
        transition: transform 0.2s ease;
    }

    .tags-list .badge:hover {
        transform: translateY(-2px);
    }

    .admin-controls {
        border-top: 1px solid rgba(0,0,0,0.1);
        background: #f8f9fa;
    }

    @media (max-width: 768px) {
        .blog-title {
            font-size: 2rem;
        }

        .blog-content-wrapper {
            padding: 1.5rem;
        }

        .blog-meta {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .blog-content {
            font-size: 1rem;
        }

        .blog-content h1, .blog-content h2, .blog-content h3,
        .blog-content h4, .blog-content h5, .blog-content h6,
        .blog-content p {
            text-align: right;
        }
    }
</style>
{% endblock %}