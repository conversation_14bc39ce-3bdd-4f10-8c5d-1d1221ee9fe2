import os
import uuid
from typing import List, Optional, Dict, Any
from werkzeug.utils import secure_filename
from datetime import datetime
import logging
from flask import current_app
from ..models.asset import Asset, AssetType
from ..utils.file_upload_helper import FileUploadHelper, FileType
from ..utils.upload_config import get_upload_config

logger = logging.getLogger(__name__)

class AssetService:
    @staticmethod
    def _get_upload_helper() -> FileUploadHelper:
        """Get configured file upload helper instance"""
        config = get_upload_config()
        return FileUploadHelper(config.__dict__)

    @staticmethod
    def upload_asset(
        user_id: str,
        file,
        file_type: str,
        metadata: Optional[Dict[str, Any]] = None,
        process_file: bool = True,
        folder: Optional[str] = None
    ) -> Asset:
        """
        Upload a new asset using the advanced file upload helper

        Args:
            user_id: The ID of the user uploading the asset
            file: The file object to upload
            file_type: The type of asset (must be a valid AssetType enum value)
            metadata: Optional metadata for the asset
            process_file: Whether to process the file (thumbnails, optimization, etc.)
            folder: Optional subfolder for organization

        Returns:
            The created Asset object
        """
        # Validate file type
        if file_type not in [t.value for t in AssetType]:
            raise ValueError(f"Invalid file type. Must be one of: {', '.join([t.value for t in AssetType])}")

        try:
            # Use the advanced file upload helper
            upload_helper = AssetService._get_upload_helper()

            # Upload file
            upload_result = upload_helper.upload_file(
                file=file,
                user_id=user_id,
                folder=folder or file_type,
                process_file=process_file
            )

            if not upload_result.success:
                raise ValueError(f"Upload failed: {upload_result.error_message}")

            # Prepare metadata
            if metadata is None:
                metadata = {}

            # Add file information from upload result
            metadata.update({
                'original_filename': upload_result.metadata.original_filename,
                'file_size': upload_result.metadata.file_size,
                'file_extension': upload_result.metadata.file_extension,
                'mime_type': upload_result.metadata.mime_type,
                'checksum': upload_result.metadata.checksum,
                'dimensions': upload_result.metadata.dimensions,
                'duration': upload_result.metadata.duration,
                'storage_backend': upload_result.storage_backend.value,
                'processing_status': upload_result.processing_status.value
            })

            # Add thumbnail and variants if available
            if upload_result.thumbnail_url:
                metadata['thumbnail_url'] = upload_result.thumbnail_url

            if upload_result.processed_variants:
                metadata['processed_variants'] = upload_result.processed_variants

            # Create asset object
            asset = Asset(
                user=user_id,
                file_url=upload_result.file_url,
                file_type=file_type,
                metadata=metadata
            )

            asset.save()
            return asset

        except Exception as e:
            logger.error(f"Asset upload failed: {str(e)}")
            raise
    
    @staticmethod
    def get_all_assets_for_user(user_id: str, file_type: Optional[str] = None) -> List[Asset]:
        """
        Get all assets for a user, optionally filtered by type
        
        Args:
            user_id: The ID of the user
            file_type: Optional filter by asset type
            
        Returns:
            List of Asset objects
        """
        query = {'user': user_id}
        
        if file_type:
            if file_type not in [t.value for t in AssetType]:
                raise ValueError(f"Invalid file type. Must be one of: {', '.join([t.value for t in AssetType])}")
            query['file_type'] = file_type
            
        return Asset.objects(**query).order_by('-created_at')
    
    @staticmethod
    def get_asset_by_id(asset_id: str) -> Optional[Asset]:
        """
        Get a specific asset by ID
        
        Args:
            asset_id: The ID of the asset
            
        Returns:
            Asset object or None if not found
        """
        try:
            return Asset.objects.get(id=asset_id)
        except Asset.DoesNotExist:
            logger.error(f"Asset with ID {asset_id} not found")
            return None
    
    @staticmethod
    def delete_asset(asset_id: str) -> bool:
        """
        Delete an asset by ID using the advanced file upload helper

        Args:
            asset_id: The ID of the asset to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            asset = Asset.objects.get(id=asset_id)

            # Use upload helper to delete file and its variants
            upload_helper = AssetService._get_upload_helper()

            # For local storage, convert URL to file path
            if asset.metadata.get('storage_backend') == 'local':
                file_path = os.path.join(
                    current_app.config.get('UPLOAD_FOLDER', 'static/uploads'),
                    asset.file_url.lstrip('/static/')
                )
                file_deleted = upload_helper.delete_file(file_path)
            else:
                # For cloud storage, use the file path from metadata or URL
                file_path = asset.metadata.get('file_path', asset.file_url)
                file_deleted = upload_helper.delete_file(file_path)

            # Delete the asset from the database regardless of file deletion success
            asset.delete()

            if not file_deleted:
                logger.warning(f"Asset {asset_id} deleted from database but file deletion failed")

            return True

        except Asset.DoesNotExist:
            logger.error(f"Asset with ID {asset_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error deleting asset: {str(e)}")
            return False
    
    @staticmethod
    def update_metadata(asset_id: str, metadata: Dict[str, Any]) -> Optional[Asset]:
        """
        Update the metadata of an asset
        
        Args:
            asset_id: The ID of the asset
            metadata: The new metadata to set
            
        Returns:
            Updated Asset object or None if not found
        """
        try:
            asset = Asset.objects.get(id=asset_id)
            
            # Update metadata
            asset.metadata.update(metadata)
            asset.updated_at = datetime.utcnow()
            asset.save()
            
            return asset
        except Asset.DoesNotExist:
            logger.error(f"Asset with ID {asset_id} not found")
            return None

    @staticmethod
    def upload_multiple_assets(
        user_id: str,
        files: List,
        file_type: str,
        metadata: Optional[Dict[str, Any]] = None,
        process_files: bool = True,
        folder: Optional[str] = None
    ) -> List[Asset]:
        """
        Upload multiple assets in batch

        Args:
            user_id: The ID of the user uploading the assets
            files: List of file objects to upload
            file_type: The type of assets (must be a valid AssetType enum value)
            metadata: Optional metadata for all assets
            process_files: Whether to process the files
            folder: Optional subfolder for organization

        Returns:
            List of created Asset objects
        """
        # Validate file type
        if file_type not in [t.value for t in AssetType]:
            raise ValueError(f"Invalid file type. Must be one of: {', '.join([t.value for t in AssetType])}")

        try:
            # Use the advanced file upload helper
            upload_helper = AssetService._get_upload_helper()

            # Upload files in batch
            upload_results = upload_helper.upload_multiple_files(
                files=files,
                user_id=user_id,
                folder=folder or file_type,
                process_files=process_files
            )

            assets = []
            for upload_result in upload_results:
                if upload_result.success:
                    # Prepare metadata
                    asset_metadata = metadata.copy() if metadata else {}

                    # Add file information from upload result
                    asset_metadata.update({
                        'original_filename': upload_result.metadata.original_filename,
                        'file_size': upload_result.metadata.file_size,
                        'file_extension': upload_result.metadata.file_extension,
                        'mime_type': upload_result.metadata.mime_type,
                        'checksum': upload_result.metadata.checksum,
                        'dimensions': upload_result.metadata.dimensions,
                        'duration': upload_result.metadata.duration,
                        'storage_backend': upload_result.storage_backend.value,
                        'processing_status': upload_result.processing_status.value
                    })

                    # Add thumbnail and variants if available
                    if upload_result.thumbnail_url:
                        asset_metadata['thumbnail_url'] = upload_result.thumbnail_url

                    if upload_result.processed_variants:
                        asset_metadata['processed_variants'] = upload_result.processed_variants

                    # Create asset object
                    asset = Asset(
                        user=user_id,
                        file_url=upload_result.file_url,
                        file_type=file_type,
                        metadata=asset_metadata
                    )

                    asset.save()
                    assets.append(asset)
                else:
                    logger.error(f"Failed to upload file: {upload_result.error_message}")

            return assets

        except Exception as e:
            logger.error(f"Batch asset upload failed: {str(e)}")
            raise

    @staticmethod
    def optimize_asset_for_platform(
        asset_id: str,
        platform: str,
        content_type: str = 'post'
    ) -> Optional[str]:
        """
        Optimize an existing asset for a specific social media platform

        Args:
            asset_id: The ID of the asset to optimize
            platform: Target platform (instagram, facebook, twitter, linkedin)
            content_type: Type of content (post, story, cover, etc.)

        Returns:
            URL of optimized file or None if optimization failed
        """
        try:
            asset = Asset.objects.get(id=asset_id)

            # Get file path from URL (this assumes local storage)
            # For cloud storage, this would need to be adapted
            file_path = os.path.join(
                current_app.config.get('UPLOAD_FOLDER', 'static/uploads'),
                asset.file_url.lstrip('/static/')
            )

            if not os.path.exists(file_path):
                logger.error(f"Asset file not found: {file_path}")
                return None

            # Use upload helper to optimize
            upload_helper = AssetService._get_upload_helper()
            optimized_path = upload_helper.optimize_for_platform(
                file_path=file_path,
                platform=platform,
                content_type=content_type
            )

            if optimized_path:
                # Convert path to URL
                optimized_url = optimized_path.replace(os.sep, '/').replace(
                    current_app.config.get('UPLOAD_FOLDER', 'static/uploads'), '/static/uploads'
                )

                # Update asset metadata with optimized version
                if 'platform_optimized' not in asset.metadata:
                    asset.metadata['platform_optimized'] = {}

                asset.metadata['platform_optimized'][f"{platform}_{content_type}"] = optimized_url
                asset.updated_at = datetime.utcnow()
                asset.save()

                return optimized_url

            return None

        except Asset.DoesNotExist:
            logger.error(f"Asset with ID {asset_id} not found")
            return None
        except Exception as e:
            logger.error(f"Asset optimization failed: {str(e)}")
            return None