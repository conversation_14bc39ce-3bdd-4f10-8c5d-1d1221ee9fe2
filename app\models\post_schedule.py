from mongoengine import Document, <PERSON><PERSON>ield, <PERSON>Field, DateTimeField, EnumField
from datetime import datetime
from enum import Enum
from .post import Post

class RecurrenceType(str, Enum):
    NONE = "none"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"

class PostSchedule(Document):
    post = ReferenceField(Post, required=True)
    scheduled_at = DateTimeField(required=True)
    recurrence = EnumField(RecurrenceType, default=RecurrenceType.NONE)
    created_at = DateTimeField(default=datetime.utcnow)
    updated_at = DateTimeField(default=datetime.utcnow)

    meta = {
        'collection': 'post_schedules',
        'indexes': ['post', 'scheduled_at']
    }