import requests
from typing import Optional
from abc import ABC, abstractmethod

class BaseAIProvider:
    """
    Base class for AI service providers.
    """
    
    def __init__(self, base_url: str, cache=None):
        """
        Initialize the provider with base URL and optional cache.
        
        :param base_url: The base URL for API requests
        :param cache: Optional cache mechanism
        """
        self.base_url = base_url
        self.cache = cache
        self.auth_token = None
        self.retry_count = 3
        self.retry_delay = 1  # seconds
        
        # Try to authenticate, but don't fail if it doesn't work
        try:
            self.authenticate()
        except Exception as e:
            import logging
            logging.warning(f"Authentication failed for {self.__class__.__name__}: {str(e)}")
    
    @abstractmethod
    def authenticate(self):
        """
        Authenticate with the AI provider.
        Must be implemented by concrete classes to set up their own authentication.
        """
        pass
    
    @abstractmethod
    def get_headers(self):
        """
        Get headers for API requests.
        Must be implemented by concrete classes based on their authentication method.
        """
        pass
    
    def log_request(self, endpoint, payload):
        """
        Log API request details.
        """
        # Implement logging logic here
        pass
    
    def handle_error(self, error):
        """
        Handle API errors.
        """
        # Implement error handling logic here
        pass
    
    def get_cached_response(self, key):
        """
        Get cached response if available.
        """
        if self.cache:
            return self.cache.get(key)
        return None
    
    def cache_response(self, key, response):
        """
        Cache API response.
        """
        if self.cache:
            self.cache.set(key, response)
    
    def retry_request(self, request_func, *args, max_retries=3, **kwargs):
        """
        Retry a request with exponential backoff.
        """
        # Implement retry logic here
        return request_func(*args, **kwargs)
    
    @abstractmethod
    def make_request(self, endpoint: str, payload: dict) -> dict:
        """
        Make a request to the AI provider API.
        Must be implemented by concrete classes.
        """
        pass
    
    @abstractmethod
    def process_response(self, response: dict) -> dict:
        """
        Process the response from the AI provider.
        Must be implemented by concrete classes.
        """
        pass
    
    @abstractmethod
    def generate_text(self, prompt: str, model: str, max_tokens: int) -> dict:
        """
        Generate text using the AI provider.
        Must be implemented by concrete classes.
        """
        pass

class OpenAIProvider(BaseAIProvider):
    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1", cache=None):
        """
        Initialize OpenAI provider with API key and optional cache.
        
        :param api_key: The OpenAI API key for authentication
        :param base_url: The base URL for OpenAI API, defaulting to OpenAI's endpoint
        :param cache: Optional cache mechanism
        """
        self.api_key = api_key
        super().__init__(base_url=base_url, cache=cache)

    def authenticate(self):
        """
        Set the API key as auth token for header usage.
        """
        self.auth_token = self.api_key

    def get_headers(self):
        """
        Get headers for API requests.
        """
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        return headers

    def make_request(self, endpoint: str, payload: dict) -> dict:
        """
        Make a POST request to the OpenAI API.
        """
        self.log_request(endpoint, payload)

        # Check cache
        cached = self.get_cached_response(endpoint)
        if cached:
            return cached

        headers = self.get_headers()
        response = requests.post(
            url=self.base_url.rstrip("/") + "/" + endpoint.lstrip("/"),
            headers=headers,
            json=payload
        )

        if response.ok:
            response_data = response.json()
            self.cache_response(endpoint, response_data)
            return response_data
        else:
            self.handle_error(f"Error {response.status_code}: {response.text}")
            response.raise_for_status()

    def process_response(self, response: dict) -> dict:
        """
        Process the response to extract generated text.
        """
        if "choices" in response and response["choices"]:
            choice = response["choices"][0]
            if "message" in choice:  # Chat models
                return {"text": choice["message"].get("content", "")}
            return {"text": choice.get("text", "")}  # Completion models
        return {"error": "No valid response."}

    def generate_text(self, prompt: str, model: str = "gpt-3.5-turbo", max_tokens: int = 100) -> dict:
        """
        Generate text using the OpenAI Completion endpoint.
        """
        endpoint = "completions"
        payload = {
            "model": model,
            "prompt": prompt,
            "max_tokens": max_tokens,
            "temperature": 0.7
        }
        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}

    def chat_with_gpt(self, messages: list, model: str = "gpt-3.5-turbo", max_tokens: int = 150) -> dict:
        """
        Chat with OpenAI's ChatGPT model.
        """
        endpoint = "chat/completions"
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens
        }
        try:
            raw_response = self.retry_request(self.make_request, endpoint, payload)
            return self.process_response(raw_response)
        except Exception as e:
            self.handle_error(e)
            return {"error": str(e)}
