{% extends "admin/admin_layout.html" %}

{% block title %}مدیریت وبلاگ - پنل مدیریت{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">مدیریت وبلاگ</h1>
        <a href="{{ url_for('admin.create_blog') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>ایجاد مقاله جدید
        </a>
    </div>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">جستجو در عنوان و محتوا</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request.args.get('search', '') }}" placeholder="جستجو...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">وضعیت</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">همه</option>
                        <option value="published" {% if request.args.get('status') == 'published' %}selected{% endif %}>منتشر شده</option>
                        <option value="draft" {% if request.args.get('status') == 'draft' %}selected{% endif %}>پیش‌نویس</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>جستجو
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="{{ url_for('admin.blogs') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>پاک کردن
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Blog Posts Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-blog me-2"></i>
                لیست مقالات
                {% if total_blogs %}
                    <span class="badge bg-primary ms-2">{{ total_blogs }} مقاله</span>
                {% endif %}
            </h5>
        </div>
        <div class="card-body">
            {% if blogs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>عنوان</th>
                                <th>نویسنده</th>
                                <th>وضعیت</th>
                                <th>تاریخ ایجاد</th>
                                <th>تاریخ انتشار</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for blog in blogs %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if blog.featured_image %}
                                            <img src="/static/{{ blog.featured_image }}" alt="{{ blog.title }}"
                                                 class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        {% endif %}
                                        <div>
                                            <h6 class="mb-1">{{ blog.title }}</h6>
                                            <small class="text-muted">{{ blog.content|striptags|truncate(80) }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ blog.user.name if blog.user else 'نامشخص' }}</td>
                                <td>
                                    {% if blog.is_published %}
                                        <span class="badge bg-success">منتشر شده</span>
                                    {% else %}
                                        <span class="badge bg-warning">پیش‌نویس</span>
                                    {% endif %}
                                </td>
                                <td>{{ blog.created_at.strftime('%Y/%m/%d') if blog.created_at else '-' }}</td>
                                <td>{{ blog.published_at.strftime('%Y/%m/%d') if blog.published_at else '-' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('blog.view', slug=blog.slug) }}" 
                                           class="btn btn-sm btn-outline-info" target="_blank" title="مشاهده">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('admin.edit_blog', blog_id=blog.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="ویرایش">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete('{{ blog.id }}', '{{ blog.title }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if total_pages > 1 %}
                <nav aria-label="صفحه‌بندی مقالات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if current_page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.blogs', page=current_page-1, search=request.args.get('search', ''), status=request.args.get('status', '')) }}">
                                قبلی
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in range(1, total_pages + 1) %}
                            {% if page_num == current_page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% elif page_num <= current_page + 2 and page_num >= current_page - 2 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.blogs', page=page_num, search=request.args.get('search', ''), status=request.args.get('status', '')) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if current_page < total_pages %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.blogs', page=current_page+1, search=request.args.get('search', ''), status=request.args.get('status', '')) }}">
                                بعدی
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">هیچ مقاله‌ای یافت نشد</h5>
                    <p class="text-muted">برای شروع، اولین مقاله خود را ایجاد کنید.</p>
                    <a href="{{ url_for('admin.create_blog') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>ایجاد مقاله جدید
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأیید حذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>آیا از حذف مقاله "<span id="blogTitle"></span>" اطمینان دارید؟</p>
                <p class="text-danger small">این عمل قابل بازگشت نیست.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(blogId, blogTitle) {
    document.getElementById('blogTitle').textContent = blogTitle;
    document.getElementById('deleteForm').action = "{{ url_for('admin.delete_blog', blog_id='BLOG_ID') }}".replace('BLOG_ID', blogId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
