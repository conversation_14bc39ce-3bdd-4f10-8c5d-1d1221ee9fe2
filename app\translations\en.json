{"landing": {"title": "Rominext - Intelligent Content", "hero": {"title": "Intelligent Social Media Management with AI", "description": "With Rominext, let AI handle your content management, comment responses, and performance analytics for your social media.", "start_free": "Start Free", "learn_more": "Learn More"}, "features": {"title": "Rominext Features", "subtitle": "Smart tools for better social media management", "learn_more": "Learn More"}, "how_it_works": {"title": "How It Works", "subtitle": "Three simple steps to get started with Rominext", "step1": {"title": "Create Account", "description": "Quickly sign up and set up your page profile."}, "step2": {"title": "Create Content Strategy", "description": "Define your content strategy so AI can better assist you."}, "step3": {"title": "Leverage AI Power", "description": "Generate content, respond to comments, and analyze your performance."}}, "pricing": {"title": "Pricing", "subtitle": "Affordable plans for businesses of all sizes", "basic_plan": "Basic Plan"}}, "nav": {"dashboard": "Dashboard", "profile": "Profile", "content_strategy": "Content Strategy", "create_post": "Create Post", "scheduled_posts": "Scheduled Posts", "comments": "Comments", "settings": "Settings", "home": "Home", "features": "Features", "features_content": "Content Creation", "features_analytics": "Analytics", "features_automation": "Automation", "pricing": "Pricing", "about": "About Us", "blog": "Blog", "login": "<PERSON><PERSON>", "signup": "Sign Up", "logout": "Logout", "register": "Register"}, "footer": {"about": "About Rominext", "about_desc": "Smart social media management powered by AI — a fast and accurate solution for content creation, comment responses, and performance analysis.", "useful_links": "Useful Links", "help": "Help", "terms": "Terms of Service", "privacy": "Privacy Policy", "contact": "Contact Us", "email": "<EMAIL>", "copyright": "© 2024 Rominext. All rights reserved."}, "auth": {"email": "Email", "password": "Password", "name": "Name", "confirm_password": "Confirm Password", "create_account": "Create an Account", "no_account": "Don't have an account?", "have_account": "Already have an account?", "remember_me": "Remember me", "forgot_password": "Forgot password?", "reset_password": "Reset Password", "back_to_login": "Back to Login", "agree_terms": "I agree to the Terms of Service and Privacy Policy", "login_success": "You have successfully logged in", "register_success": "Your account has been created successfully", "invalid_credentials": "Invalid email or password. Please try again.", "email_exists": "A user with this email already exists", "registration_failed": "Registration failed. Please try again.", "provide_all_fields": "Please provide all required fields", "provide_email": "Please provide your email address", "reset_link_sent": "Password reset link has been sent to your email", "user_created_success": "New user has been created successfully", "validation_error": "Data validation error", "user_creation_error": "Error creating user", "access_denied": "You do not have access to this page", "admin_access_denied": "You do not have access to the admin panel", "provide_email_password": "Please enter email and password", "welcome": "Welcome!", "login_error": "Login error", "logout_success": "You have successfully logged out of the admin panel", "post_not_found": "The requested post was not found", "post_deleted_success": "Post has been successfully deleted", "post_delete_error": "Error deleting post", "post_published_success": "Post has been successfully published", "post_publish_error": "Error publishing post", "post_created_success": "New post has been successfully created", "post_creation_error": "Error creating post", "blog_created_success": "Blog post created successfully!", "blog_creation_error": "Error creating blog post", "blog_updated_success": "Blog post updated successfully!", "blog_update_error": "Error updating blog post", "blog_deleted_success": "Blog post deleted successfully!", "blog_delete_error": "Error deleting blog post", "blog_create_input_error": "Error creating blog post. Please check your input.", "blog_create_general_error": "An error occurred while creating the blog post.", "blog_update_input_error": "Error updating blog post. Please check your input.", "blog_update_general_error": "An error occurred while updating the blog post.", "blog_delete_general_error": "An error occurred while deleting the blog post.", "account_not_found": "Account not found", "unauthorized": "Unauthorized", "account_disconnect_failed": "Failed to disconnect account", "account_disconnected_success": "Account disconnected successfully", "account_disconnect_error": "An error occurred while disconnecting the account", "accounts_retrieve_failed": "Failed to retrieve accounts", "account_connected_success": "Account connected successfully", "account_connect_failed": "Failed to connect account", "verification_code_failed": "Failed to generate verification code", "terms_title": "Terms and Conditions", "terms_intro": "By creating an account on Rominext, you agree to the following terms and conditions:", "terms_section1_title": "Account Responsibility", "terms_section1_content": "You are responsible for maintaining the confidentiality of your account information and password. You agree to accept responsibility for all activities that occur under your account.", "terms_section2_title": "Content Usage", "terms_section2_content": "Rominext uses AI to generate content for your social media. While we strive for high quality, you are responsible for reviewing all content before publishing it to your social media accounts.", "terms_section3_title": "Data Privacy", "terms_section3_content": "We collect and process your data according to our Privacy Policy. This includes information about your social media accounts and usage patterns to improve our service.", "why_join_title": "Why Join <PERSON>?", "benefit1_title": "AI-Powered Content", "benefit1_desc": "Let our advanced AI create engaging content tailored to your audience and business needs.", "benefit2_title": "Automated Responses", "benefit2_desc": "Save time with intelligent auto-replies to comments and messages on your social media.", "benefit3_title": "Performance Analytics", "benefit3_desc": "Get detailed insights and analytics to understand what works best for your audience.", "welcome_back": "Welcome Back!", "login_message": "Log in to your Rominext account to continue managing your social media with AI-powered tools and analytics.", "secure_login_title": "<PERSON><PERSON>", "secure_login_desc": "Your account security is our priority. We use encryption to protect your login information.", "login_help_title": "Need Help?", "login_help_desc": "If you're having trouble logging in, please contact our support <NAME_EMAIL>."}, "errors": {"unauthorized": {"title": "Unauthorized Access", "message": "You need to log in to access this page.", "login_button": "Log In", "register_button": "Register"}, "not_found": {"title": "Page Not Found", "message": "The page you are looking for does not exist or has been removed.", "home_button": "Return to Home"}, "server_error": {"title": "Server Error", "message": "Sorry, an error occurred on the server. Please try again later.", "home_button": "Return to Home"}}, "common": {"coming_soon": "Content coming soon..."}, "pricing": {"title": "Pricing", "subtitle": "Choose the plan that fits your needs"}, "about": {"title": "About Us", "subtitle": "Learn more about Rominext and our mission"}, "blog": {"title": "Blog", "subtitle": "Educational articles and news", "published_on": "Published on", "back_to_blog": "Back to Blog"}, "email": {"templates": {"title": "Email Templates", "create_new": "Create New Template", "list_title": "Email Templates", "name": "Name", "key": "Template Key", "category": "Category", "languages": "Languages", "status": "Status", "created": "Created", "actions": "Actions", "system": "System", "active": "Active", "inactive": "Inactive", "no_templates": "No Email Templates", "no_templates_desc": "You haven't created any email templates yet.", "create_first": "Create Your First Template", "preview": "Preview Template", "language": "Language", "subject": "Subject", "content": "Content", "variables": "Variables", "no_variables": "No variables defined", "delete_confirm": "Delete Template", "delete_warning": "Are you sure you want to delete this template? This action cannot be undone.", "edit_title": "Edit <PERSON><PERSON>late", "create_title": "Create <PERSON><PERSON>", "basic_info": "Basic Information", "key_help": "Unique identifier for this template (e.g., welcome, password_reset)", "description": "Description", "default_language": "Default Language", "variables_help": "Template variables (comma-separated)", "html_content": "HTML Content", "text_content": "Text Content (Optional)", "variables_help_title": "Template Variables", "variables_help_desc": "Use these variables in your templates. They will be replaced with actual values when sending emails.", "common_variables": "Common Variables", "auth_variables": "Authentication Variables", "duplicate": "Duplicate Template"}, "categories": {"auth": "Authentication", "notification": "Notification", "marketing": "Marketing", "system": "System"}, "variables": {"user_name": "User's name", "site_name": "Site name", "current_year": "Current year", "unsubscribe_url": "Unsubscribe URL", "reset_link": "Password reset link", "verification_link": "Email verification link"}, "analytics": {"title": "Email Analytics", "total_sent": "Total Sent", "delivery_rate": "Delivery Rate", "open_rate": "Open Rate", "click_rate": "Click Rate", "template_performance": "Template Performance", "sent": "<PERSON><PERSON>", "delivered": "Delivered", "opened": "Opened", "clicked": "Clicked", "no_data": "No Data Available", "no_data_desc": "No email data available for the selected period.", "queue_status": "Queue Status", "queue_size": "<PERSON><PERSON> Size", "worker_status": "Worker Status", "max_retries": "Max Retries", "retry_delay": "Retry Delay", "quick_actions": "Quick Actions", "test_email": "Test Email", "manage_templates": "Manage Templates", "refresh": "Refresh", "daily_stats": "Daily Statistics", "daily_email_stats": "Daily Email Statistics"}, "test": {"title": "Test Email System", "smtp_test": "SMTP Connection Test", "smtp_test_desc": "Test the SMTP connection to ensure emails can be sent.", "test_connection": "Test Connection", "send_test": "Send Test Email", "recipient": "Recipient Email", "subject": "Subject", "html_content": "HTML Content", "text_content": "Text Content", "send_email": "Send Test Email", "current_settings": "Current SMTP Settings", "smtp_host": "SMTP Host", "smtp_port": "SMTP Port", "smtp_username": "SMTP Username", "use_tls": "Use TLS", "use_ssl": "Use SSL", "from_email": "From Email", "template_test": "Template Test", "template_test_desc": "Send a test email using an existing template.", "template": "Template", "language": "Language", "send_template": "Send Template Email", "quick_actions": "Quick Actions", "quick_test": "Quick Test Email", "quick_test_desc": "Send a quick test email to mail.eh<PERSON><PERSON><PERSON>@gmail.com to verify the system is working.", "manage_templates": "Manage Templates", "view_analytics": "View Analytics"}}}